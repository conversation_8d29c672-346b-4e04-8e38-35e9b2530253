from typing import Any, List, BinaryIO, Enum, ClassVar

import numpy


""" This block is generated by tools/stackvm_gen/CApiGen at 12/20/2024 5:27:07 PM +08:00. """


class MemoryAccessArchitecture(Enum):
    UMA = 0
    NUMA = 1


class NocArchitecture(Enum):
    Mesh = 0
    CrossBar = 1


class HierarchyKind(Enum):
    Parallel = 0
    SMT = 1


""" end the auto generated block by tools/stackvm_gen/CApiGen at 12/20/2024 5:27:07 PM +08:00. """

""" This block is generated by tools/stackvm_gen/CApiGen at 12/20/2024 5:27:07 PM +08:00. """


class NTTTargetOptions:
    def __init__(self) -> None: ...
    ModelName: str
    Vectorize: bool
    UnifiedMemoryArch: bool
    MemoryAccessArch: MemoryAccessArchitecture
    NocArch: NocArchitecture
    HierarchyKind: HierarchyKind
    Hierarchies: List[List[int]]
    HierarchyNames: str
    HierarchySizes: List[int]
    HierarchyLatencies: List[int]
    HierarchyBandWidths: List[int]
    MemoryCapacities: List[int]
    MemoryBandWidths: List[int]
    DistributedScheme: str
    CustomOpScheme: str


""" end the auto generated block by tools/stackvm_gen/CApiGen at 12/20/2024 5:27:07 PM +08:00. """


class CompileOptions:
    benchmark_only: bool
    dump_asm: bool
    dump_dir: Path
    dump_ir: bool
    swapRB: bool
    input_file: str
    input_range: List[float]
    input_shape: List[int]
    input_type: str
    is_fpga: bool
    mean: List[float]
    std: List[float]
    output_type: str
    preprocess: bool
    quant_type: str
    target: str
    w_quant_type: str
    use_mse_quant_w: bool
    input_layout: str
    output_layout: str
    letterbox_value: float
    def __init__(self) -> None: ...


class Compiler:
    def __init__(self, compile_options: CompileOptions) -> None: ...
    def compile(self) -> None: ...
    def create_evaluator(self, stage: int) -> GraphEvaluator: ...
    def gencode(self, stream: BinaryIO) -> None: ...
    def gencode_tobytes(self) -> bytes: ...
    def import_caffe(self, model: bytes, prototxt: bytes) -> None: ...
    def import_onnx(self, model: bytes, options: ImportOptions) -> None: ...
    def import_tflite(self, model: bytes, options: ImportOptions) -> None: ...
    def import_huggingface(self, model_path:str) -> None: ...
    def use_ptq(self, ptq_dataset_options: PTQTensorOptions) -> None: ...


class GraphEvaluator:
    def __init__(self) -> None: ...
    def get_input_tensor(self, index: int) -> Any: ...
    def get_output_tensor(self, index: int) -> Any: ...
    def run(self) -> None: ...
    @property
    def outputs_size(self) -> int: ...


class ImportOptions:
    huggingface_options: HuggingFaceOptions
    def __init__(self, import_options) -> None: ...


class MemoryRange:
    dtype: numpy.dtype
    location: int
    size: int
    start: int
    def __init__(self) -> None: ...


class PTQTensorOptions:
    calibrate_method: str
    input_mean: float
    input_std: float
    samples_count: int
    quant_type: str
    w_quant_type: str
    finetune_weights_method: str
    use_mix_quant: bool
    def __init__(self) -> None: ...
    def set_tensor_data(self, bytes: bytes) -> None: ...

class HuggingFaceAttentionBackend:
    __members__: ClassVar[dict] = ...  # read-only
    Default: ClassVar[HuggingFaceAttentionBackend] = ...
    PagedAttention: ClassVar[HuggingFaceAttentionBackend] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

class HuggingFaceOptions:
    attention_backend: HuggingFaceAttentionBackend
    config: PagedAttentionConfig
    max_model_len: int
    output_logits: bool
    output_hidden_states: bool
    num_layers: int
    tensor_type: str
    def __init__(self) -> None: ...

class Path:
    def __init__(self, path: str) -> None: ...


class RuntimeTensor:
    def __init__(self) -> None: ...
    def copy_to(self, to: RuntimeTensor) -> None: ...
    @staticmethod
    def from_numpy(self, arr: numpy.ndarray) -> Any: ...
    @staticmethod
    def from_object(arg0) -> RuntimeTensor: ...
    def to_numpy(self) -> numpy.ndarray: ...
    @property
    def dtype(self) -> dtype: ...
    @property
    def shape(self) -> List[int]: ...

class Expr:
    def __init__(self, *args, **kwargs) -> None: ...
    def evaluate(self, params: list, inputs: list) -> RTValue: ...

class DimensionKind:
    __members__: ClassVar[dict] = ...  # read-only
    Dynamic: ClassVar[DimensionKind] = ...
    Fixed: ClassVar[DimensionKind] = ...
    Unknown: ClassVar[DimensionKind] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

class Dimension:
    def __init__(self, *args, **kwargs) -> None: ...
    @property
    def kind(self) -> DimensionKind: ...

class Var(Expr):
    def __init__(self, *args, **kwargs) -> None: ...
    def dimensions(self, *args, **kwargs) -> list[Dimension]: ...

class Function(Expr):
    def __init__(self, *args, **kwargs) -> None: ...
    @property
    def body(self) -> Expr: ...
    @property
    def parameters(self) -> list[Var]: ...

class RTValue:
    def __init__(self, *args, **kwargs) -> None: ...
    @staticmethod
    def from_runtime_tensor(arg0: RuntimeTensor) -> RTValue: ...
    def to_runtime_tensor(self) -> RuntimeTensor: ...
    def to_runtime_tensors(self) -> list[RuntimeTensor]: ...

class Simulator:
    def __init__(self) -> None: ...
    def get_input_desc(self, index: int) -> MemoryRange: ...
    def get_input_tensor(self, index: int) -> RuntimeTensor: ...
    def get_output_desc(self, index: int) -> MemoryRange: ...
    def get_output_tensor(self, index: int) -> RuntimeTensor: ...
    def load_model(self, model: bytes) -> None: ...
    def run(self) -> None: ...
    def enable_profiling(self) -> None: ...
    def set_input_tensor(self, index: int, tensor: RuntimeTensor) -> None: ...
    def set_output_tensor(self, index: int, tensor: RuntimeTensor) -> None: ...
    @property
    def inputs_size(self) -> int: ...
    @property
    def outputs_size(self) -> int: ...


def test_target(target: str) -> bool: ...

class PagedKVCacheDimKind:
    __members__: ClassVar[dict] = ...  # read-only
    BlockSize: ClassVar[PagedKVCacheDimKind] = ...
    HeadDim: ClassVar[PagedKVCacheDimKind] = ...
    KV: ClassVar[PagedKVCacheDimKind] = ...
    NumBlocks: ClassVar[PagedKVCacheDimKind] = ...
    NumKVHeads: ClassVar[PagedKVCacheDimKind] = ...
    NumLayers: ClassVar[PagedKVCacheDimKind] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

class Object:
    def __init__(self, *args, **kwargs) -> None: ...

class AttentionConfig(Object):
    head_dim: int
    kv_type: numpy.dtype
    num_kv_heads: int
    num_layers: int
    def __init__(self, arg0: int, arg1: int, arg2: int, arg3: numpy.dtype) -> None: ...

class PagedAttentionConfig(AttentionConfig):
    axis_policies: list[list[int]]
    block_size: int
    cache_layout: list[PagedKVCacheDimKind]
    lanes: list[int]
    vectorized_axes: list[PagedKVCacheDimKind]
    sharding_axes: list[PagedKVCacheDimKind]
    def __init__(self, num_layers: int, num_kv_heads: int, head_dim: int, kv_type: numpy.dtype, block_size: int, cache_layout=..., vectorized_axes: list[PagedKVCacheDimKind] = ..., lanes: list[int] = ..., sharding_axes: list[PagedKVCacheDimKind] = ..., axis_policies: list[list[int]] = ...) -> None: ...
    def set_axis_policy(self, arg0: int, arg1: list[int]) -> None: ...
    @property
    def block_layout(self): ...

class PagedAttentionKVCache(Object):
    block_table: RuntimeTensor
    context_lens: RuntimeTensor
    kv_topo: list[int]
    num_blocks: int
    num_seqs: int
    num_tokens: int
    seq_lens: RuntimeTensor
    slot_mapping: RuntimeTensor
    def __init__(self, arg0: PagedAttentionConfig) -> None: ...
    @overload
    def kv_cache(self, arg0: list[int], arg1: RuntimeTensor) -> None: ...
    @overload
    def kv_cache(self, arg0: list[int]) -> RuntimeTensor: ...
    @property
    def config(self) -> PagedAttentionConfig: ...

class IValue:
    def __init__(self, *args, **kwargs) -> None: ...

class RefPagedAttentionKVCache:
    def __init__(self, *args, **kwargs) -> None: ...
    def as_ivalue(self) -> IValue: ...
    @property
    def block_table(self) -> RTValue: ...
    @property
    def context_lens(self) -> RTValue: ...
    @property
    def kv_caches(self) -> RTValue: ...
    @property
    def num_blocks(self) -> int: ...
    @property
    def num_seqs(self) -> int: ...
    @property
    def num_tokens(self) -> int: ...
    @property
    def seq_lens(self) -> RTValue: ...
    @property
    def slot_mapping(self) -> RTValue: ...

class AttentionDimKind:
    __members__: ClassVar[dict] = ...  # read-only
    Dim: ClassVar[AttentionDimKind] = ...
    Head: ClassVar[AttentionDimKind] = ...
    Seq: ClassVar[AttentionDimKind] = ...
    __entries: ClassVar[dict] = ...
    def __init__(self, value: int) -> None: ...
    def __eq__(self, other: object) -> bool: ...
    def __hash__(self) -> int: ...
    def __index__(self) -> int: ...
    def __int__(self) -> int: ...
    def __ne__(self, other: object) -> bool: ...
    @property
    def name(self) -> str: ...
    @property
    def value(self) -> int: ...

class PagedAttentionScheduler(Object):
    def __init__(self, config: PagedAttentionConfig, num_blocks: int, max_model_len: int, hierarchy: list[int]) -> None: ...
    def schedule(self, session_ids: list[int], query_lens: list[int]) -> PagedAttentionKVCache: ...

class RefPagedAttentionScheduler:
    def __init__(self, config: PagedAttentionConfig, num_blocks: int, max_model_len: int, hierarchy: list[int]) -> None: ...
    def create_test_function(self, num_q_head: int, q_layout: list[AttentionDimKind], kv_layout: list[AttentionDimKind]) -> Function: ...
    def schedule(self, session_ids: list[int], query_lens: list[int]) -> RefPagedAttentionKVCache: ...
