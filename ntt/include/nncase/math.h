/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include <cmath>

#if defined(__NEWLIB__) && !defined(NNCASE_XPU_MODULE)
namespace std {
using ::erff;
using ::roundf;

inline float erf(float v) noexcept { return erff(v); }
inline float round(float v) noexcept { return roundf(v); }
} // namespace std
#endif
