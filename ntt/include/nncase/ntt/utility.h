/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "nncase/ntt/dimension.h"
#include "shape.h"
#include "tensor_traits.h"
#include <cstring>
#include <span>
#include <type_traits>

namespace nncase::ntt {
namespace utility_detail {
template <size_t Axis, Tensor TTensor, Shape TOutShape>
constexpr auto get_safe_stride(const TTensor &tensor,
                               const TOutShape &out_shape) noexcept {
    auto dim_ext = out_shape.rank() - tensor.rank();
    if constexpr (Axis < dim_ext) {
        return dim_zero;
    } else {
        auto actual_axis = fixed_dim_v<Axis> - dim_ext;
        auto actual_dim = tensor.shape()[actual_axis];
        if constexpr (FixedDimension<std::decay_t<decltype(actual_dim)>>) {
            if constexpr (actual_dim == 1) {
                return dim_zero;
            } else {
                return tensor.strides()[actual_axis];
            }
        } else {
            if (actual_dim == 1) {
                return (dim_t)0;
            } else {
                return dim_value(tensor.strides()[actual_axis]);
            }
        }
    }
}
} // namespace utility_detail

template <class U, class T, size_t Extent>
constexpr auto span_cast(std::span<T, Extent> src) noexcept {
    using return_type = std::span<U, Extent == std::dynamic_extent
                                         ? std::dynamic_extent
                                         : Extent * sizeof(T) / sizeof(U)>;
    if constexpr (std::is_const_v<U>) {
        return return_type{(const U *)src.data(), src.size_bytes() / sizeof(U)};
    } else {
        return return_type{(U *)src.data(), src.size_bytes() / sizeof(U)};
    }
}

template <class T, size_t SrcExtent, Dimension TOffset, Dimension TExtent>
constexpr auto make_subspan(std::span<T, SrcExtent> src, const TOffset &offset,
                            const TExtent &extent) noexcept {
    using return_type = std::span<
        T, ntt::where(std::integral_constant<bool, FixedDimension<TExtent>>{},
                      TExtent{}, std::dynamic_extent)>;
    return return_type{src.data() + dim_value(offset),
                       static_cast<size_t>(dim_value(extent))};
}
} // namespace nncase::ntt
