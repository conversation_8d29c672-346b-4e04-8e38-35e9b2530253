/* Copyright 2019-2024 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "../../../half.h"
#include "../../vector.h"
#include "arch_types.h"
#include "primitive_ops.float32.h"
#include "rvv_mathfun.h"
#include "rvv_mathfun_half.h"
#ifdef __riscv_vector
#include <riscv_vector.h>
#endif

namespace nncase::ntt::ops {

#ifndef REGISTER_RVV_FP16_KERNEL
#define REGISTER_RVV_FP16_KERNEL(kernel)                                       \
    kernel(1, 16) kernel(2, 8) kernel(4, 4) kernel(8, 2)
#endif

#ifndef REGISTER_RVV_KERNEL_2_1
#define REGISTER_RVV_KERNEL_2_1(kernel)                                        \
    kernel(1, f4, 16) kernel(2, f2, 8) kernel(4, 1, 4) kernel(8, 2, 2)
#endif

#ifndef REGISTER_RVV_FP16_x2_KERNEL
#define REGISTER_RVV_FP16_x2_KERNEL(kernel)                                    \
    kernel(1, 2, 16) kernel(2, 4, 8) kernel(4, 8, 4)
#endif

#ifndef REGISTER_RVV_FP16_d2_KERNEL
#define REGISTER_RVV_FP16_d2_KERNEL(kernel)                                    \
    kernel(2, 1, 16) kernel(4, 2, 8) kernel(8, 4, 4)
#endif

// float32 intermediate
#ifndef REGISTER_RVV_FP16_KERNEL_FP32_IM
#define REGISTER_RVV_FP16_KERNEL_FP32_IM(kernel)                               \
    kernel(1, 16) kernel(2, 8) kernel(4, 4)
#endif

#define RVV_UNARY_FP16_OP(op, dtype, vl, kernel)                               \
    template <> struct op<ntt::vector<dtype, vl>> {                            \
        ntt::vector<dtype, vl>                                                 \
        operator()(const ntt::vector<dtype, vl> &v) const noexcept {           \
            return kernel(v, vl);                                              \
        }                                                                      \
    };

// unary with half
#define REGISTER_RVV_UNARY_FP16_OP(OP, dtype, kernel)                          \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 1), kernel)      \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 2), kernel)      \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 4), kernel)      \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 8), kernel)

#define REGISTER_RVV_UNARY_FP16_x2_OP(OP, dtype, kernel)                       \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 1), kernel)      \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 2), kernel)      \
    RVV_UNARY_FP16_OP(OP, dtype, NTT_VL(sizeof(dtype) * 8, *, 4), kernel)

// abs
#define ABS_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t abs_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        return __riscv_vfabs_v_f16m##lmul(v, vl);                              \
    }

REGISTER_RVV_FP16_KERNEL(ABS_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(abs, half, abs_float16)

// acos
#define ACOS_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t acos_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        constexpr auto pc0 = (_Float16)(0x1.55555ep-3);                        \
        constexpr auto pc1 = (_Float16)(0x1.33261ap-4);                        \
        constexpr auto pc2 = (_Float16)(0x1.70d7dcp-5);                        \
        constexpr auto pc3 = (_Float16)(0x1.921fb6p+1f);                       \
        constexpr auto pc4 = (_Float16)(0x1.921fb6p+0f);                       \
        constexpr auto pc5 = (_Float16)(0x1.3af7d8p-5);                        \
        constexpr auto pc6 = (_Float16)(0x1.b059dp-6);                         \
        auto zero = __riscv_vfmv_v_f_f16m##lmul(0.f16, vl);                    \
        auto half = __riscv_vfmv_v_f_f16m##lmul(0.5f16, vl);                   \
        auto one = __riscv_vfmv_v_f_f16m##lmul(1.f16, vl);                     \
        auto two = __riscv_vfmv_v_f_f16m##lmul(2.f16, vl);                     \
        auto minus_one = __riscv_vfmv_v_f_f16m##lmul(-1.f16, vl);              \
        auto p0 = __riscv_vfmv_v_f_f16m##lmul(pc0, vl);                        \
        auto p1 = __riscv_vfmv_v_f_f16m##lmul(pc1, vl);                        \
        auto p2 = __riscv_vfmv_v_f_f16m##lmul(pc2, vl);                        \
        auto neg_mask = __riscv_vmflt_vf_f16m##lmul##_b##mlen(v, 0.f16, vl);   \
        auto x = __riscv_vfabs_v_f16m##lmul(v, vl);                            \
        auto off = __riscv_vfmerge_vfm_f16m##lmul(zero, pc3, neg_mask, vl);    \
        auto mul1 = __riscv_vfmerge_vfm_f16m##lmul(two, -2.f16, neg_mask, vl); \
        auto mul2 =                                                            \
            __riscv_vfmerge_vfm_f16m##lmul(minus_one, 1.f16, neg_mask, vl);    \
        auto le_half_mask =                                                    \
            __riscv_vmfle_vv_f16m##lmul##_b##mlen(x, half, vl);                \
        auto tmp = __riscv_vmv_v_v_f16m##lmul(x, vl);                          \
        auto mul =                                                             \
            __riscv_vmerge_vvm_f16m##lmul(mul1, mul2, le_half_mask, vl);       \
        tmp = __riscv_vfnmsub_vv_f16m##lmul(tmp, half, half, vl);              \
        auto v2 = __riscv_vfmul_vv_f16m##lmul(v, v, vl);                       \
        auto add = __riscv_vfmerge_vfm_f16m##lmul(off, pc4, le_half_mask, vl); \
        auto z2 = __riscv_vmerge_vvm_f16m##lmul(tmp, v2, le_half_mask, vl);    \
        auto y1 = __riscv_vfmv_v_f_f16m##lmul(pc5, vl);                        \
        auto y2 = __riscv_vfmv_v_f_f16m##lmul(pc6, vl);                        \
        tmp = __riscv_vfsqrt_v_f16m##lmul(z2, vl);                             \
        auto z4 = __riscv_vfmul_vv_f16m##lmul(z2, z2, vl);                     \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, z4, p2, vl);                     \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, z4, p1, vl);                     \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, z4, p0, vl);                     \
        auto z = __riscv_vmerge_vvm_f16m##lmul(tmp, x, le_half_mask, vl);      \
        y1 = __riscv_vfmacc_vv_f16m##lmul(y1, y2, z2, vl);                     \
        mul = __riscv_vfmul_vv_f16m##lmul(mul, z, vl);                         \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, z2, one, vl);                    \
        return __riscv_vfmadd_vv_f16m##lmul(y1, mul, add, vl);                 \
    }

REGISTER_RVV_FP16_KERNEL(ACOS_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(acos, half, acos_float16)

// acosh
// acosh(v) = ln(v + sqrt(v^2 - 1)), v >= 1
#define ACOSH_FLOAT16(lmul, mlen)                                              \
    inline vfloat16m##lmul##_t acosh_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        auto sub = __riscv_vfsub_vf_f16m##lmul(v, 1.f16, vl);                  \
        auto add = __riscv_vfadd_vf_f16m##lmul(v, 1.f16, vl);                  \
        auto mul = __riscv_vfmul_vv_f16m##lmul(sub, add, vl);                  \
        auto sqrt = __riscv_vfsqrt_v_f16m##lmul(mul, vl);                      \
        return log_ps_fp16(__riscv_vfadd_vv_f16m##lmul(v, sqrt, vl), vl);      \
    }

REGISTER_RVV_FP16_KERNEL(ACOSH_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(acosh, half, acosh_float16)

// asin
#define ASIN_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t asin_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        constexpr auto pc0 = (_Float16)(0x1.921fb6p+0f);                       \
        constexpr auto pc1 = (_Float16)(0x1.55555ep-3);                        \
        constexpr auto pc2 = (_Float16)(0x1.31661ap-4);                        \
        constexpr auto pc3 = (_Float16)(0x1.70d7dcp-5);                        \
        constexpr auto pc4 = (_Float16)(0x1.3af7d8p-5);                        \
        constexpr auto pc5 = (_Float16)(0x1.b059dp-6);                         \
        auto half = __riscv_vfmv_v_f_f16m##lmul(0.5f16, vl);                   \
        auto one = __riscv_vfmv_v_f_f16m##lmul(1.f16, vl);                     \
        auto minus_two = __riscv_vfmv_v_f_f16m##lmul(-2.f16, vl);              \
        auto pi_over_2f = __riscv_vfmv_v_f_f16m##lmul(pc0, vl);                \
        auto p0 = __riscv_vfmv_v_f_f16m##lmul(pc1, vl);                        \
        auto p1 = __riscv_vfmv_v_f_f16m##lmul(pc2, vl);                        \
        auto p2 = __riscv_vfmv_v_f_f16m##lmul(pc3, vl);                        \
        auto neg_mask = __riscv_vmflt_vf_f16m##lmul##_b##mlen(v, 0.f16, vl);   \
        auto x = __riscv_vfabs_v_f16m##lmul(v, vl);                            \
        auto mul1 = __riscv_vfmerge_vfm_f16m##lmul(one, -1.f16, neg_mask, vl); \
                                                                               \
        /* Evaluate polynomial Q(x) = z + z * z2 * P(z2) with                  \
            z2 = x ^ 2         and z = |x|     , if |x| < 0.5                  \
            z2 = (1 - |x|) / 2 and z = sqrt(z2), if |x| >= 0.5.  */            \
        auto lt_half_mask =                                                    \
            __riscv_vmflt_vv_f16m##lmul##_b##mlen(x, half, vl);                \
        auto tmp = __riscv_vmv_v_v_f16m##lmul(x, vl);                          \
        auto mul2 = __riscv_vfmerge_vfm_f16m##lmul(minus_two, 1.f16,           \
                                                   lt_half_mask, vl);          \
        tmp = __riscv_vfnmsub_vv_f16m##lmul(tmp, half, half, vl);              \
        auto add = __riscv_vfmerge_vfm_f16m##lmul(pi_over_2f, 0.f16,           \
                                                  lt_half_mask, vl);           \
        auto v2 = __riscv_vfmul_vv_f16m##lmul(v, v, vl);                       \
        auto z2 = __riscv_vmerge_vvm_f16m##lmul(tmp, v2, lt_half_mask, vl);    \
        /* asin(|x|) = Q(|x|),        for |x| < 0.5                            \
                = pi / 2 - 2 Q(|x|) , for |x| >= 0.5.  */                      \
        auto y1 = __riscv_vfmv_v_f_f16m##lmul(pc4, vl);                        \
        auto y2 = __riscv_vfmv_v_f_f16m##lmul(pc5, vl);                        \
        auto z4 = __riscv_vfmul_vv_f16m##lmul(z2, z2, vl);                     \
        tmp = __riscv_vfsqrt_v_f16m##lmul(z2, vl);                             \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, z4, p2, vl);                     \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, z4, p1, vl);                     \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, z4, p0, vl);                     \
        auto z = __riscv_vmerge_vvm_f16m##lmul(tmp, x, lt_half_mask, vl);      \
        y1 = __riscv_vfmacc_vv_f16m##lmul(y1, y2, z2, vl);                     \
        z2 = __riscv_vfmul_vv_f16m##lmul(z2, z, vl);                           \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, z2, z, vl);                      \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, mul2, add, vl);                  \
        return __riscv_vfmul_vv_f16m##lmul(y1, mul1, vl);                      \
    }

REGISTER_RVV_FP16_KERNEL(ASIN_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(asin, half, asin_float16)

// asinh
#define ASINH_FLOAT16(lmul, mlen)                                              \
    inline vfloat16m##lmul##_t asinh_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        auto x = __riscv_vfsgnj_vf_f16##m##lmul(v, 1.f16, vl);                 \
        auto two = __riscv_vfmv_v_f_f16m##lmul(2.f16, vl);                     \
        auto add = __riscv_vfadd_vf_f16m##lmul(x, 1.f16, vl);                  \
        auto sub = __riscv_vfsub_vf_f16m##lmul(x, 1.f16, vl);                  \
        add = __riscv_vfmadd_vv_f16m##lmul(add, sub, two, vl);                 \
        auto sqrt = __riscv_vfsqrt_v_f16m##lmul(add, vl);                      \
        auto ret = log_ps_fp16(__riscv_vfadd_vv_f16m##lmul(x, sqrt, vl), vl);  \
        return __riscv_vfsgnj_vv_f16##m##lmul(ret, v, vl);                     \
    }

REGISTER_RVV_FP16_KERNEL(ASINH_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(asinh, half, asinh_float16)

// ceil
#define CEIL_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t ceil_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        auto vi = __riscv_vfcvt_x_f_v_i16m##lmul(v, vl);                       \
        auto vf = __riscv_vfcvt_f_x_v_f16m##lmul(vi, vl);                      \
        auto mask = __riscv_vmflt_vv_f16m##lmul##_b##mlen(vf, v, vl);          \
        vf = __riscv_vfadd_vf_f16m##lmul##_m(mask, vf, 1.f16, vl);             \
        return vf;                                                             \
    }

REGISTER_RVV_FP16_KERNEL(CEIL_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(ceil, half, ceil_float16)

// cos
#define COS_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t cos_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        constexpr auto p0 = (_Float16)(0x1.45f306p-2f);                        \
        constexpr auto p1 = (_Float16)(-0x1.555548p-3f);                       \
        constexpr auto p2 = (_Float16)(-0x1.9f42eap-13f);                      \
        constexpr auto p3 = (_Float16)(0x1.921fb6p+1f);                        \
        constexpr auto p4 = (_Float16)(-0x1.777a5cp-24f);                      \
        constexpr auto p5 = (_Float16)(-0x1.ee59dap-49f);                      \
        constexpr auto p6 = (_Float16)(0x1.5b2e76p-19f);                       \
        constexpr auto p7 = (_Float16)(0x1.110df4p-7f);                        \
        auto n = __riscv_vfmv_v_f_f16m##lmul(p0, vl);                          \
        auto half = __riscv_vfmv_v_f_f16m##lmul(0.5f16, vl);                   \
        auto c0 = __riscv_vfmv_v_f_f16m##lmul(p1, vl);                         \
        auto c2 = __riscv_vfmv_v_f_f16m##lmul(p2, vl);                         \
                                                                               \
        auto r = __riscv_vfabs_v_f16m##lmul(v, vl);                            \
        n = __riscv_vfmadd_vv_f16m##lmul(r, n, half, vl);                      \
        auto ni = __riscv_vfcvt_x_f_v_i16m##lmul(n, vl);                       \
        n = __riscv_vfcvt_f_x_v_f16m##lmul(ni, vl);                            \
        auto parity = __riscv_vand_vx_i16m##lmul(ni, 1, vl);                   \
        auto odd = __riscv_vsll_vx_i16m##lmul(parity, 15, vl);                 \
        n = __riscv_vfsub_vf_f16m##lmul(n, 0.5f16, vl);                        \
                                                                               \
        r = __riscv_vfnmsac_vf_f16m##lmul(r, p3, n, vl);                       \
        r = __riscv_vfnmsac_vf_f16m##lmul(r, p4, n, vl);                       \
        r = __riscv_vfnmsac_vf_f16m##lmul(r, p5, n, vl);                       \
                                                                               \
        auto r2 = __riscv_vfmul_vv_f16m##lmul(r, r, vl);                       \
        auto y1 = __riscv_vfmv_v_f_f16m##lmul(p6, vl);                         \
        auto y2 = __riscv_vfmv_v_f_f16m##lmul(p7, vl);                         \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, r2, c2, vl);                     \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, r2, c0, vl);                     \
        auto r4 = __riscv_vfmul_vv_f16m##lmul(r2, r2, vl);                     \
        auto r3 = __riscv_vfmul_vv_f16m##lmul(r2, r, vl);                      \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, r4, y2, vl);                     \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, r3, r, vl);                      \
                                                                               \
        auto tmp = __riscv_vreinterpret_v_f16m##lmul##_i16m##lmul(y1);         \
        tmp = __riscv_vxor_vv_i16m##lmul(tmp, odd, vl);                        \
        return __riscv_vreinterpret_v_i16m##lmul##_f16m##lmul(tmp);            \
    }

REGISTER_RVV_FP16_KERNEL(COS_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(cos, half, cos_float16)

// cosh
#define COSH_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t cosh_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        auto a = exp_ps_fp16(v, vl);                                           \
        auto b = __riscv_vfrdiv_vf_f16m##lmul(a, 1.f16, vl);                   \
        auto sum = __riscv_vfadd_vv_f16m##lmul(a, b, vl);                      \
        return __riscv_vfmul_vf_f16m##lmul(sum, 0.5f16, vl);                   \
    }

REGISTER_RVV_FP16_KERNEL(COSH_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(cosh, half, cosh_float16)

// floor
#define FLOOR_FLOAT16(lmul, mlen)                                              \
    inline vfloat16m##lmul##_t floor_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        auto vi = __riscv_vfcvt_x_f_v_i16m##lmul(v, vl);                       \
        auto vf = __riscv_vfcvt_f_x_v_f16m##lmul(vi, vl);                      \
        auto mask = __riscv_vmfgt_vv_f16m##lmul##_b##mlen(vf, v, vl);          \
        __asm__ volatile("" ::: "memory");                                     \
        vf = __riscv_vfsub_vf_f16m##lmul##_m(mask, vf, 1.f16, vl);             \
        return vf;                                                             \
    }

REGISTER_RVV_FP16_KERNEL(FLOOR_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(floor, half, floor_float16)

// neg
#define NEG_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t neg_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        return __riscv_vfneg_v_f16m##lmul(v, vl);                              \
    }

REGISTER_RVV_FP16_KERNEL(NEG_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(neg, half, neg_float16)

// rsqrt
#define RSQRT_FLOAT16(lmul, mlen)                                              \
    inline vfloat16m##lmul##_t rsqrt_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        auto one_point_five = __riscv_vfmv_v_f_f16m##lmul(1.5f16, vl);         \
                                                                               \
        auto ux = __riscv_vreinterpret_v_f16m##lmul##_u16m##lmul(v);           \
        ux = __riscv_vsrl_vx_u16m##lmul(ux, 1, vl);                            \
        ux = __riscv_vrsub_vx_u16m##lmul(                                      \
            ux, static_cast<uint16_t>(0x5f375a86), vl);                        \
        auto y = __riscv_vreinterpret_v_u16m##lmul##_f16m##lmul(ux);           \
                                                                               \
        auto y2 = __riscv_vfmul_vv_f16m##lmul(y, y, vl);                       \
        auto x = __riscv_vfmul_vf_f16m##lmul(v, -0.5f16, vl);                  \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, x, one_point_five, vl);          \
        y = __riscv_vfmul_vv_f16m##lmul(y, y2, vl);                            \
                                                                               \
        y2 = __riscv_vfmul_vv_f16m##lmul(y, y, vl);                            \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, x, one_point_five, vl);          \
        y = __riscv_vfmul_vv_f16m##lmul(y, y2, vl);                            \
                                                                               \
        y2 = __riscv_vfmul_vv_f16m##lmul(y, y, vl);                            \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, x, one_point_five, vl);          \
        y = __riscv_vfmul_vv_f16m##lmul(y, y2, vl);                            \
                                                                               \
        y2 = __riscv_vfmul_vv_f16m##lmul(y, y, vl);                            \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, x, one_point_five, vl);          \
        y = __riscv_vfmul_vv_f16m##lmul(y, y2, vl);                            \
        return y;                                                              \
    }

REGISTER_RVV_FP16_KERNEL(RSQRT_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(rsqrt, half, rsqrt_float16)

// round
#define ROUND_FLOAT16(lmul, mlen)                                              \
    inline vfloat16m##lmul##_t round_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        return __riscv_vfcvt_f_x_v_f16m##lmul(                                 \
            __riscv_vfcvt_x_f_v_i16m##lmul(v, vl), vl);                        \
    }

REGISTER_RVV_FP16_KERNEL(ROUND_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(round, half, round_float16)

// sign
#define SIGN_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t sign_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        auto ret = __riscv_vfmv_v_f_f16m##lmul(0.f16, vl);                     \
        auto gt_mask = __riscv_vmfgt_vf_f16m##lmul##_b##mlen(v, 0.f16, vl);    \
        ret = __riscv_vfmerge_vfm_f16m##lmul(ret, 1.f16, gt_mask, vl);         \
        auto lt_mask = __riscv_vmflt_vf_f16m##lmul##_b##mlen(v, 0.f16, vl);    \
        return __riscv_vfmerge_vfm_f16m##lmul(ret, -1.f16, lt_mask, vl);       \
    }

REGISTER_RVV_FP16_KERNEL(SIGN_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(sign, half, sign_float16)

// sin
#define SIN_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t sin_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        constexpr auto pc0 = (_Float16)(-0x1.555548p-3f);                      \
        constexpr auto pc1 = (_Float16)(-0x1.9f42eap-13f);                     \
        constexpr auto pc2 = (_Float16)(0x1.45f306p-2f);                       \
        constexpr auto pc3 = (_Float16)(0x1.921fb6p+1f);                       \
        constexpr auto pc4 = (_Float16)(-0x1.777a5cp-24f);                     \
        constexpr auto pc5 = (_Float16)(-0x1.ee59dap-49f);                     \
        constexpr auto pc6 = (_Float16)(0x1.5b2e76p-19f);                      \
        constexpr auto pc7 = (_Float16)(0x1.110df4p-7f);                       \
        auto c0 = __riscv_vfmv_v_f_f16m##lmul(pc0, vl);                        \
        auto c2 = __riscv_vfmv_v_f_f16m##lmul(pc1, vl);                        \
                                                                               \
        /* n = rint(|x|/pi) */                                                 \
        auto r = __riscv_vfabs_v_f16m##lmul(v, vl);                            \
        auto n = __riscv_vfmul_vf_f16m##lmul(r, pc2, vl);                      \
        auto sign = __riscv_vxor_vv_i16m##lmul(                                \
            __riscv_vreinterpret_v_f16m##lmul##_i16m##lmul(v),                 \
            __riscv_vreinterpret_v_f16m##lmul##_i16m##lmul(r), vl);            \
        auto ni = __riscv_vfcvt_x_f_v_i16m##lmul(n, vl);                       \
        n = __riscv_vfcvt_f_x_v_f16m##lmul(ni, vl);                            \
        auto odd = __riscv_vand_vx_i16m##lmul(ni, 1, vl);                      \
                                                                               \
        /* r = |x| - n*pi  (range reduction into -pi/2 .. pi/2).  */           \
        r = __riscv_vfnmsac_vf_f16m##lmul(r, pc3, n, vl);                      \
        odd = __riscv_vsll_vx_i16m##lmul(odd, 15, vl);                         \
        r = __riscv_vfnmsac_vf_f16m##lmul(r, pc4, n, vl);                      \
        r = __riscv_vfnmsac_vf_f16m##lmul(r, pc5, n, vl);                      \
                                                                               \
        /* y = sin(r).  */                                                     \
        auto r2 = __riscv_vfmul_vv_f16m##lmul(r, r, vl);                       \
        auto y1 = __riscv_vfmv_v_f_f16m##lmul(pc6, vl);                        \
        auto y2 = __riscv_vfmv_v_f_f16m##lmul(pc7, vl);                        \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, r2, c2, vl);                     \
        y2 = __riscv_vfmadd_vv_f16m##lmul(y2, r2, c0, vl);                     \
        auto r4 = __riscv_vfmul_vv_f16m##lmul(r2, r2, vl);                     \
        auto r3 = __riscv_vfmul_vv_f16m##lmul(r2, r, vl);                      \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, r4, y2, vl);                     \
        sign = __riscv_vxor_vv_i16m##lmul(sign, odd, vl);                      \
        y1 = __riscv_vfmadd_vv_f16m##lmul(y1, r3, r, vl);                      \
        auto tmp = __riscv_vreinterpret_v_f16m##lmul##_i16m##lmul(y1);         \
        tmp = __riscv_vxor_vv_i16m##lmul(tmp, sign, vl);                       \
        return __riscv_vreinterpret_v_i16m##lmul##_f16m##lmul(tmp);            \
    }

REGISTER_RVV_FP16_KERNEL(SIN_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(sin, half, sin_float16)

// sinh
// sinh(v) = (exp(v) - exp(-v)) / 2
#define SINH_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t sinh_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        auto a = exp_ps_fp16(v, vl);                                           \
        auto b = __riscv_vfrdiv_vf_f16m##lmul(a, 1.f16, vl);                   \
        return __riscv_vfmul_vf_f16m##lmul(                                    \
            __riscv_vfsub_vv_f16m##lmul(a, b, vl), 0.5f16, vl);                \
    }

REGISTER_RVV_FP16_KERNEL(SINH_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(sinh, half, sinh_float16)

// sqrt
#define SQRT_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t sqrt_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        return __riscv_vfsqrt_v_f16m##lmul(v, vl);                             \
    }

REGISTER_RVV_FP16_KERNEL(SQRT_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(sqrt, half, sqrt_float16)

// square
#define SQUARE_FLOAT16(lmul, mlen)                                             \
    inline vfloat16m##lmul##_t square_float16(const vfloat16m##lmul##_t &v,    \
                                              const size_t vl) {               \
        return __riscv_vfmul_vv_f16m##lmul(v, v, vl);                          \
    }

REGISTER_RVV_FP16_KERNEL(SQUARE_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(square, half, square_float16)

// exp
#define EXP_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t exp_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        return exp_ps_fp16(v, vl);                                             \
    }

REGISTER_RVV_FP16_KERNEL(EXP_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(exp, half, exp_float16)

// log
#define LOG_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t log_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        return log_ps_fp16(v, vl);                                             \
    }

REGISTER_RVV_FP16_KERNEL(LOG_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(log, half, log_float16)

// tanh
#define TANH_FLOAT16(lmul, mlen)                                               \
    inline vfloat16m##lmul##_t tanh_float16(const vfloat16m##lmul##_t &v,      \
                                            const size_t vl) {                 \
        return tanh_ps_fp16(v, vl);                                            \
    }

REGISTER_RVV_FP16_KERNEL(TANH_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(tanh, half, tanh_float16)

// swish
// swish(v) = v / (exp(-v) + 1)
#if not defined(NNCASE_XPU_MODULE)
#define SWISH_FLOAT16(lmul, mlen)                                              \
    inline vfloat16m##lmul##_t swish_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        auto tmp = exp_ps_fp16(__riscv_vfneg_v_f16m##lmul(v, vl), vl);         \
        return __riscv_vfdiv_vv_f16m##lmul(                                    \
            v, __riscv_vfadd_vf_f16m##lmul(tmp, 1.f16, vl), vl);               \
    }
REGISTER_RVV_FP16_KERNEL(SWISH_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(swish, half, swish_float16)
#else
#define SWISH_FLOAT16(lmul, lmulx2, mlen)                                      \
    inline vfloat16m##lmul##_t swish_float16(const vfloat16m##lmul##_t &v,     \
                                             const size_t vl) {                \
        auto v_w = __riscv_vfwcvt_f_f_v_f32m##lmulx2(v, vl);                   \
        auto v_sig = __riscv_th_vfsig_v_f32m##lmulx2(v_w, vl * 2);             \
        auto v_n = __riscv_vfncvt_f_f_w_f16m##lmul(v_sig, vl * 2);             \
        return __riscv_vfmul_vv_f16m##lmul(v, v_n, vl);                        \
    }
REGISTER_RVV_FP16_x2_KERNEL(SWISH_FLOAT16)
REGISTER_RVV_UNARY_FP16_x2_OP(swish, half, swish_float16)
#endif

// register swishb kernel
// swishb(v) = v / (exp(-v*beta) + 1)
#define SWISHB_FLOAT16(lmul, mlen)                                             \
    inline vfloat16m##lmul##_t swishb_float16(const vfloat16m##lmul##_t &v,    \
                                              half beta, const size_t vl) {    \
        auto tmp = __riscv_vfmul_vf_f16m##lmul(v, -beta, vl);                  \
        tmp = exp_ps_fp16(tmp, vl);                                            \
        tmp = __riscv_vfadd_vf_f16m##lmul(tmp, 1.f16, vl);                     \
        return __riscv_vfdiv_vv_f16m##lmul(v, tmp, vl);                        \
    }

REGISTER_RVV_FP16_KERNEL(SWISHB_FLOAT16)

// register swishb op
#define RVV_SWISHB_FP16_OP(dtype, vl, kernel)                                  \
    template <> struct swishb<ntt::vector<dtype, vl>, dtype> {                 \
        ntt::vector<dtype, vl> operator()(const ntt::vector<dtype, vl> &v,     \
                                          const dtype &beta) const noexcept {  \
            return kernel(v, beta, vl);                                        \
        }                                                                      \
    };

#define REGISTER_RVV_SWISHB_FP16_OP(dtype, kernel)                             \
    RVV_SWISHB_FP16_OP(dtype, NTT_VL(sizeof(dtype) * 8, *, 1), kernel)         \
    RVV_SWISHB_FP16_OP(dtype, NTT_VL(sizeof(dtype) * 8, *, 2), kernel)         \
    RVV_SWISHB_FP16_OP(dtype, NTT_VL(sizeof(dtype) * 8, *, 4), kernel)         \
    RVV_SWISHB_FP16_OP(dtype, NTT_VL(sizeof(dtype) * 8, *, 8), kernel)

REGISTER_RVV_SWISHB_FP16_OP(half, swishb_float16)

// erf
#define ERF_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t erf_float16(const vfloat16m##lmul##_t &v,       \
                                           const size_t vl) {                  \
        return erf_ps_fp16(v, vl);                                             \
    }

REGISTER_RVV_FP16_KERNEL(ERF_FLOAT16)
REGISTER_RVV_UNARY_FP16_OP(erf, half, erf_float16)

// binary
#define RVV_BINARY_fp16_OP(op, dtype, vl, kernel)                              \
    template <> struct op<ntt::vector<dtype, vl>, ntt::vector<dtype, vl>> {    \
        ntt::vector<dtype, vl>                                                 \
        operator()(const ntt::vector<dtype, vl> &v1,                           \
                   const ntt::vector<dtype, vl> &v2) const noexcept {          \
            return kernel(v1, v2, vl);                                         \
        }                                                                      \
    };                                                                         \
    template <> struct op<ntt::vector<dtype, vl>, dtype> {                     \
        ntt::vector<dtype, vl> operator()(const ntt::vector<dtype, vl> &v,     \
                                          const dtype &s) const noexcept {     \
            return kernel(v, s, vl);                                           \
        }                                                                      \
    };                                                                         \
    template <> struct op<dtype, ntt::vector<dtype, vl>> {                     \
        ntt::vector<dtype, vl>                                                 \
        operator()(const dtype &s,                                             \
                   const ntt::vector<dtype, vl> &v) const noexcept {           \
            return kernel(s, v, vl);                                           \
        };                                                                     \
    };

#define RVV_BINARY_fp32_fp16_OP(op, vl, kernel)                                \
    template <>                                                                \
    struct op<ntt::vector<float, 2, vl / 2>, ntt::vector<half, vl>> {          \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<float, 2, vl / 2> &v1,                    \
                   const ntt::vector<half, vl> &v2) const noexcept {           \
            return kernel(v1, v2, vl);                                         \
        }                                                                      \
    };                                                                         \
    template <>                                                                \
    struct op<ntt::vector<half, vl>, ntt::vector<float, 2, vl / 2>> {          \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<half, vl> &v1,                            \
                   const ntt::vector<float, 2, vl / 2> &v2) const noexcept {   \
            return kernel(v1, v2, vl);                                         \
        }                                                                      \
    };                                                                         \
    template <> struct op<ntt::vector<float, 2, vl / 2>, half> {               \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<float, 2, vl / 2> &v,                     \
                   const half &s) const noexcept {                             \
            return kernel(v, s, vl);                                           \
        }                                                                      \
    };                                                                         \
    template <> struct op<half, ntt::vector<float, 2, vl / 2>> {               \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const half &s,                                              \
                   const ntt::vector<float, 2, vl / 2> &v) const noexcept {    \
            return kernel(s, v, vl);                                           \
        };                                                                     \
    };

// binary op
#define REGISTER_RVV_BINARY_FP16_OP(op, dtype, kernel)                         \
    RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 1), kernel)     \
        RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 2), kernel) \
            RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 4),     \
                               kernel)                                         \
                RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 8), \
                                   kernel)

#define REGISTER_RVV_BINARY_FP32_FP16_OP(op, kernel)                           \
    RVV_BINARY_fp32_fp16_OP(op, NTT_VL(sizeof(half) * 8, *, 1), kernel)        \
        RVV_BINARY_fp32_fp16_OP(op, NTT_VL(sizeof(half) * 8, *, 2), kernel)    \
            RVV_BINARY_fp32_fp16_OP(op, NTT_VL(sizeof(half) * 8, *, 4),        \
                                    kernel)

// Fp32 as immidiate result
#define REGISTER_RVV_BINARY_FP16_OPS_FP32_IM(op, dtype, kernel)                \
    RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 1), kernel)     \
        RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 2), kernel) \
            RVV_BINARY_fp16_OP(op, dtype, NTT_VL(sizeof(dtype) * 8, *, 4),     \
                               kernel)
// add
template <> struct add<float, half> {
    constexpr float operator()(const float &a, const half &b) const noexcept {
        return a + static_cast<float>(b);
    }
};

#define ADD_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t add_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return __riscv_vfadd_vv_f16m##lmul(v1, v2, vl);                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t add_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        return __riscv_vfadd_vf_f16m##lmul(v, s, vl);                          \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t add_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v, const size_t vl) {        \
        return __riscv_vfadd_vf_f16m##lmul(v, s, vl);                          \
    }

REGISTER_RVV_FP16_KERNEL(ADD_FLOAT16)
REGISTER_RVV_BINARY_FP16_OP(add, half, add_float16)

#define ADD_FLOAT32_FLOAT16(lmul, lmulx2, mlen)                                \
    inline vfloat32m##lmulx2##_t add_float16(const vfloat32m##lmulx2##_t &v1,  \
                                             const vfloat16m##lmul##_t &v2,    \
                                             const size_t vl) {                \
        return __riscv_vfwadd_wv_f32m##lmulx2(v1, v2, vl);                     \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t add_float16(const vfloat16m##lmul##_t &v1,    \
                                             const vfloat32m##lmulx2##_t &v2,  \
                                             const size_t vl) {                \
        return __riscv_vfwadd_wv_f32m##lmulx2(v2, v1, vl);                     \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t add_float16(const vfloat32m##lmulx2##_t &v,   \
                                             const half &s, const size_t vl) { \
        return __riscv_vfwadd_wf_f32m##lmulx2(v, s, vl);                       \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t add_float16(                                  \
        const half &s, const vfloat32m##lmulx2##_t &v, const size_t vl) {      \
        return __riscv_vfwadd_wf_f32m##lmulx2(v, s, vl);                       \
    }

REGISTER_RVV_FP16_x2_KERNEL(ADD_FLOAT32_FLOAT16);
REGISTER_RVV_BINARY_FP32_FP16_OP(add, add_float16)

// sub
#define SUB_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t sub_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return __riscv_vfsub_vv_f16m##lmul(v1, v2, vl);                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t sub_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        return __riscv_vfsub_vf_f16m##lmul(v, s, vl);                          \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t sub_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v, const size_t vl) {        \
        return __riscv_vfrsub_vf_f16m##lmul(v, s, vl);                         \
    }

REGISTER_RVV_FP16_KERNEL(SUB_FLOAT16)
REGISTER_RVV_BINARY_FP16_OP(sub, half, sub_float16)

// mul
#define MUL_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t mul_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return __riscv_vfmul_vv_f16m##lmul(v1, v2, vl);                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mul_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        return __riscv_vfmul_vf_f16m##lmul(v, s, vl);                          \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mul_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v, const size_t vl) {        \
        return __riscv_vfmul_vf_f16m##lmul(v, s, vl);                          \
    }

REGISTER_RVV_FP16_KERNEL(MUL_FLOAT16)
REGISTER_RVV_BINARY_FP16_OP(mul, half, mul_float16)

// div
#define DIV_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t div_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return __riscv_vfdiv_vv_f16m##lmul(v1, v2, vl);                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t div_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        return __riscv_vfdiv_vf_f16m##lmul(v, s, vl);                          \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t div_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v, const size_t vl) {        \
        return __riscv_vfrdiv_vf_f16m##lmul(v, s, vl);                         \
    }

REGISTER_RVV_FP16_KERNEL(DIV_FLOAT16)
REGISTER_RVV_BINARY_FP16_OP(div, half, div_float16)

// pow
#define POW_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t pow_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return pow_ps_fp16(v1, v2, vl);                                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t pow_float16(const vfloat16m##lmul##_t &v1,      \
                                           const half &s, const size_t vl) {   \
        auto v2 = __riscv_vfmv_v_f_f16m##lmul(s, vl);                          \
        return pow_ps_fp16(v1, v2, vl);                                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t pow_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v2, const size_t vl) {       \
        auto v1 = __riscv_vfmv_v_f_f16m##lmul(s, vl);                          \
        return pow_ps_fp16(v1, v2, vl);                                        \
    }

// REGISTER_RVV_FP16_KERNEL(POW_FLOAT16)
// REGISTER_RVV_BINARY_FP16_OP(pow, half, pow_float16)

#define LMUL_DBL_1 2
#define LMUL_DBL_2 4
#define LMUL_DBL_4 8

#define CONCAT_IMPL(a, b) a##b
#define CONCAT(a, b) CONCAT_IMPL(a, b)

#define DOUBLE_LMUL(lmul) CONCAT(LMUL_DBL_, lmul)
#define CALL_DBL_LMUL(name, lmul) CONCAT(name, DOUBLE_LMUL(lmul))

// mod
#define MOD_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t mod_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        auto v1_f32 = CALL_DBL_LMUL(__riscv_vfwcvt_f_f_v_f32m, lmul)(v1, vl);  \
        auto v2_f32 = CALL_DBL_LMUL(__riscv_vfwcvt_f_f_v_f32m, lmul)(v2, vl);  \
        auto division_f32 =                                                    \
            CALL_DBL_LMUL(__riscv_vfdiv_vv_f32m, lmul)(v1_f32, v2_f32, vl);    \
        auto quotient_int = CALL_DBL_LMUL(__riscv_vfcvt_rtz_x_f_v_i32m,        \
                                          lmul)(division_f32, vl);             \
        auto quotient_f32 =                                                    \
            CALL_DBL_LMUL(__riscv_vfcvt_f_x_v_f32m, lmul)(quotient_int, vl);   \
        auto result_f32 = CALL_DBL_LMUL(__riscv_vfnmsub_vv_f32m, lmul)(        \
            quotient_f32, v2_f32, v1_f32, vl);                                 \
        auto result_f16 = __riscv_vfncvt_f_f_w_f16m##lmul(result_f32, vl);     \
        return result_f16;                                                     \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mod_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        float s_f32 = (float)s;                                                \
        auto v_f32 = CALL_DBL_LMUL(__riscv_vfwcvt_f_f_v_f32m, lmul)(v, vl);    \
        auto division_f32 =                                                    \
            CALL_DBL_LMUL(__riscv_vfdiv_vf_f32m, lmul)(v_f32, s_f32, vl);      \
        auto quotient_int = CALL_DBL_LMUL(__riscv_vfcvt_rtz_x_f_v_i32m,        \
                                          lmul)(division_f32, vl);             \
        auto quotient_f32 =                                                    \
            CALL_DBL_LMUL(__riscv_vfcvt_f_x_v_f32m, lmul)(quotient_int, vl);   \
        auto result_f32 = CALL_DBL_LMUL(__riscv_vfnmsub_vf_f32m,               \
                                        lmul)(quotient_f32, s_f32, v_f32, vl); \
        auto result_f16 = __riscv_vfncvt_f_f_w_f16m##lmul(result_f32, vl);     \
        return result_f16;                                                     \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mod_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v2, const size_t vl) {       \
        float s_f32 = (float)s;                                                \
        auto v1_f32 = CALL_DBL_LMUL(__riscv_vfmv_v_f_f32m, lmul)(s_f32, vl);   \
        auto v2_f32 = CALL_DBL_LMUL(__riscv_vfwcvt_f_f_v_f32m, lmul)(v2, vl);  \
        auto division_f32 =                                                    \
            CALL_DBL_LMUL(__riscv_vfrdiv_vf_f32m, lmul)(v2_f32, s_f32, vl);    \
        auto quotient_int = CALL_DBL_LMUL(__riscv_vfcvt_rtz_x_f_v_i32m,        \
                                          lmul)(division_f32, vl);             \
        auto quotient_f32 =                                                    \
            CALL_DBL_LMUL(__riscv_vfcvt_f_x_v_f32m, lmul)(quotient_int, vl);   \
        auto result_f32 = CALL_DBL_LMUL(__riscv_vfnmsub_vv_f32m, lmul)(        \
            quotient_f32, v2_f32, v1_f32, vl);                                 \
        auto result_f16 = __riscv_vfncvt_f_f_w_f16m##lmul(result_f32, vl);     \
        return result_f16;                                                     \
    }

REGISTER_RVV_FP16_KERNEL_FP32_IM(MOD_FLOAT16)
REGISTER_RVV_BINARY_FP16_OPS_FP32_IM(mod, half, mod_float16)

// min
// template <> struct min<half, half> {
//     auto operator()(const half &s1, const half &s2) const noexcept {
//         half ret;
//         __asm("fmin.s %[ret], %[s1], %[s2];"
//               : [ret] "=f"(ret)
//               : [s1] "f"(s1), [s2] "f"(s2));
//         return ret;
//     }
// };

#define MIN_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t min_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return __riscv_vfmin_vv_f16m##lmul(v1, v2, vl);                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t min_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        return __riscv_vfmin_vf_f16m##lmul(v, s, vl);                          \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t min_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v, const size_t vl) {        \
        return __riscv_vfmin_vf_f16m##lmul(v, s, vl);                          \
    }

REGISTER_RVV_FP16_KERNEL(MIN_FLOAT16)
REGISTER_RVV_BINARY_FP16_OP(min, half, min_float16)

// max
// template <> struct max<half, half> {
//     auto operator()(const half &s1, const half &s2) const noexcept {
//         half ret;
//         __asm("fmax.s %[ret], %[s1], %[s2];"
//               : [ret] "=f"(ret)
//               : [s1] "f"(s1), [s2] "f"(s2));
//         return ret;
//     }
// };

#define MAX_FLOAT16(lmul, mlen)                                                \
    inline vfloat16m##lmul##_t max_float16(const vfloat16m##lmul##_t &v1,      \
                                           const vfloat16m##lmul##_t &v2,      \
                                           const size_t vl) {                  \
        return __riscv_vfmax_vv_f16m##lmul(v1, v2, vl);                        \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t max_float16(const vfloat16m##lmul##_t &v,       \
                                           const half &s, const size_t vl) {   \
        return __riscv_vfmax_vf_f16m##lmul(v, s, vl);                          \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t max_float16(                                    \
        const half &s, const vfloat16m##lmul##_t &v, const size_t vl) {        \
        return __riscv_vfmax_vf_f16m##lmul(v, s, vl);                          \
    }

REGISTER_RVV_FP16_KERNEL(MAX_FLOAT16)
REGISTER_RVV_BINARY_FP16_OP(max, half, max_float16)

// floor_mod
#define FLOOR_MOD_INT16(lmul, mlen)                                            \
    inline vint16m##lmul##_t floor_mod_int16(const vint16m##lmul##_t &v1,      \
                                             const vint16m##lmul##_t &v2,      \
                                             const size_t vl) {                \
        auto remainder = __riscv_vrem_vv_i16m##lmul(v1, v2, vl);               \
        auto tmp = __riscv_vxor_vv_i16m##lmul(v1, v2, vl);                     \
        auto mask1 = __riscv_vmsne_vx_i16m##lmul##_b##mlen(remainder, 0, vl);  \
        auto mask2 = __riscv_vmslt_vx_i16m##lmul##_b##mlen(tmp, 0, vl);        \
        mask1 = __riscv_vmand_mm_b##mlen(mask1, mask2, vl);                    \
        __asm__ volatile("" ::: "memory");                                     \
        /*        remainder = __riscv_vadd_vv_i16m##lmul##_m(mask1, remainder, \
         * v2, vl);  \ */                                                      \
        asm volatile("vmv.v.v v0, %[mask]\n\t"                                 \
                     "vadd.vv %[rem], %[rem], %[val], v0.t"                    \
                     : [rem] "+vr"(remainder)                                  \
                     : [mask] "vr"(mask1), [val] "vr"(v2)                      \
                     : "v0");                                                  \
        return remainder;                                                      \
    }                                                                          \
                                                                               \
    inline vint16m##lmul##_t floor_mod_int16(                                  \
        const vint16m##lmul##_t &v1, const int16_t &s, const size_t vl) {      \
        auto remainder = __riscv_vrem_vx_i16m##lmul(v1, s, vl);                \
        auto tmp = __riscv_vxor_vx_i16m##lmul(v1, s, vl);                      \
        auto mask1 = __riscv_vmsne_vx_i16m##lmul##_b##mlen(remainder, 0, vl);  \
        auto mask2 = __riscv_vmslt_vx_i16m##lmul##_b##mlen(tmp, 0, vl);        \
        mask1 = __riscv_vmand_mm_b##mlen(mask1, mask2, vl);                    \
        /*        remainder = __riscv_vadd_vv_i16m##lmul##_m(mask1, remainder, \
         * v2, vl);  \ */                                                      \
        asm volatile("vmv.v.v v0, %[mask]\n\t"                                 \
                     "vadd.vx %[rem], %[rem], %[val], v0.t"                    \
                     : [rem] "+vr"(remainder)                                  \
                     : [mask] "vr"(mask1), [val] "r"(s)                        \
                     : "v0");                                                  \
        return remainder;                                                      \
    }                                                                          \
                                                                               \
    inline vint16m##lmul##_t floor_mod_int16(                                  \
        const int16_t &s, const vint16m##lmul##_t &v2, const size_t vl) {      \
        auto v1 = __riscv_vmv_v_x_i16m##lmul(s, vl);                           \
        auto remainder = __riscv_vrem_vv_i16m##lmul(v1, v2, vl);               \
        auto tmp = __riscv_vxor_vv_i16m##lmul(v1, v2, vl);                     \
        auto mask1 = __riscv_vmsne_vx_i16m##lmul##_b##mlen(remainder, 0, vl);  \
        auto mask2 = __riscv_vmslt_vx_i16m##lmul##_b##mlen(tmp, 0, vl);        \
        mask1 = __riscv_vmand_mm_b##mlen(mask1, mask2, vl);                    \
        /*        remainder = __riscv_vadd_vv_i16m##lmul##_m(mask1, remainder, \
         * v2, vl);  \ */                                                      \
        asm volatile("vmv.v.v v0, %[mask]\n\t"                                 \
                     "vadd.vv %[rem], %[rem], %[val], v0.t"                    \
                     : [rem] "+vr"(remainder)                                  \
                     : [mask] "vr"(mask1), [val] "vr"(v2)                      \
                     : "v0");                                                  \
        return remainder;                                                      \
    }

REGISTER_RVV_FP16_KERNEL(FLOOR_MOD_INT16)
REGISTER_RVV_BINARY_FP16_OP(floor_mod, int16_t, floor_mod_int16)

// register cast kernel
#define CAST_FLOAT16_FLOAT32(lmul, lmulx2, mlen)                               \
    inline vfloat32m##lmulx2##_t cast_float16_float32(                         \
        const vfloat16m##lmul##_t &v, const size_t vl) {                       \
        return __riscv_vfwcvt_f_f_v_f32m##lmulx2(v, vl);                       \
    }

#define CAST_FLOAT32_FLOAT16(lmul, lmuld2, mlen)                               \
    inline vfloat16m##lmuld2##_t cast_float32_float16(                         \
        const vfloat32m##lmul##_t &v, const size_t vl) {                       \
        return __riscv_vfncvt_f_f_w_f16m##lmuld2(v, vl);                       \
    }

REGISTER_RVV_FP16_x2_KERNEL(CAST_FLOAT16_FLOAT32);
REGISTER_RVV_FP16_d2_KERNEL(CAST_FLOAT32_FLOAT16);

REGISTER_RVV_CAST_ELEM_OP_1_2(half, float, cast_float16_float32)
REGISTER_RVV_CAST_ELEM_OP_2_1(float, half, cast_float32_float16)

#if defined(NNCASE_XPU_MODULE)
// f16 -> f8, lmul=1
template <>
struct cast_elem<ntt::vector<half, 2, NTT_VL(sizeof(half) * 8, *, 1)>,
                 float_e4m3_t> {
    ntt::vector<float_e4m3_t, NTT_VL(sizeof(float_e4m3_t) * 8, *, 1)>
    operator()(const ntt::vector<half, 2, NTT_VL(sizeof(half) * 8, *, 1)> &v)
        const noexcept {
        return __riscv_th_vfncvt_e4_h_f8e4m1(
            v, 0, NTT_VL(sizeof(half) * 8, *, 1) * 2);
    }
};

// f16 -> f8, lmul=4
template <>
struct cast_elem<ntt::vector<half, 2, NTT_VL(sizeof(half) * 8, *, 1) * 4>,
                 float_e4m3_t> {
    ntt::vector<float_e4m3_t, NTT_VL(sizeof(float_e4m3_t) * 8, *, 1) * 4>
    operator()(const ntt::vector<half, 2, NTT_VL(sizeof(half) * 8, *, 1) * 4>
                   &v) const noexcept {
        return __riscv_th_vfncvt_e4_h_f8e4m4(
            v, 0, NTT_VL(sizeof(half) * 8, *, 1) * 2 * 4);
    }
};

#endif

// mul_add
#define MUL_ADD_FLOAT16(lmul, mlen)                                            \
    inline vfloat16m##lmul##_t mul_add_float16(                                \
        const vfloat16m##lmul##_t &v1, const vfloat16m##lmul##_t &v2,          \
        const vfloat16m##lmul##_t &v3, const size_t vl) {                      \
        return __riscv_vfmacc_vv_f16m##lmul(v3, v1, v2, vl);                   \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mul_add_float16(                                \
        const vfloat16m##lmul##_t &v1, const half &s2,                         \
        const vfloat16m##lmul##_t &v3, const size_t vl) {                      \
        return __riscv_vfmacc_vf_f16m##lmul(v3, s2, v1, vl);                   \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mul_add_float16(                                \
        const half &s1, const vfloat16m##lmul##_t &v2,                         \
        const vfloat16m##lmul##_t &v3, const size_t vl) {                      \
        return __riscv_vfmacc_vf_f16m##lmul(v3, s1, v2, vl);                   \
    }

REGISTER_RVV_KERNEL(MUL_ADD_FLOAT16)
REGISTER_RVV_MUL_ADD_OP(half, mul_add_float16)

#define MUL_ADD_FLOAT32_FLOAT16(lmul, lmulx2, mlen)                            \
    inline vfloat32m##lmulx2##_t mul_add_float16(                              \
        const vfloat16m##lmul##_t &v1, const vfloat16m##lmul##_t &v2,          \
        const vfloat32m##lmulx2##_t &v3, const size_t vl) {                    \
        return __riscv_vfwmacc_vv_f32m##lmulx2(v3, v1, v2, vl);                \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t mul_add_float16(                              \
        const vfloat16m##lmul##_t &v, const half &s,                           \
        const vfloat32m##lmulx2##_t &v3, const size_t vl) {                    \
        return __riscv_vfwmacc_vf_f32m##lmulx2(v3, s, v, vl);                  \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t mul_add_float16(                              \
        const half &s, const vfloat16m##lmul##_t &v,                           \
        const vfloat32m##lmulx2##_t &v3, const size_t vl) {                    \
        return __riscv_vfwmacc_vf_f32m##lmulx2(v3, s, v, vl);                  \
    }

REGISTER_RVV_FP16_x2_KERNEL(MUL_ADD_FLOAT32_FLOAT16);

#define RVV_FP32_FP16_MUL_ADD_OP(vl, kernel)                                   \
    template <>                                                                \
    struct mul_add<ntt::vector<half, vl>, ntt::vector<half, vl>,               \
                   ntt::vector<float, 2, vl / 2>> {                            \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<half, vl> &v1,                            \
                   const ntt::vector<half, vl> &v2,                            \
                   const ntt::vector<float, 2, vl / 2> &v3) const noexcept {   \
            return kernel(v1, v2, v3, vl);                                     \
        }                                                                      \
    };                                                                         \
                                                                               \
    template <>                                                                \
    struct mul_add<ntt::vector<half, vl>, half,                                \
                   ntt::vector<float, 2, vl / 2>> {                            \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<half, vl> &v1, const half &s2,            \
                   const ntt::vector<float, 2, vl / 2> &v3) const noexcept {   \
            return kernel(v1, s2, v3, vl);                                     \
        }                                                                      \
    };                                                                         \
                                                                               \
    template <>                                                                \
    struct mul_add<half, ntt::vector<half, vl>,                                \
                   ntt::vector<float, 2, vl / 2>> {                            \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const half &s1, const ntt::vector<half, vl> &v2,            \
                   const ntt::vector<float, 2, vl / 2> &v3) const noexcept {   \
            return kernel(s1, v2, v3, vl);                                     \
        }                                                                      \
    };

#define REGISTER_RVV_FP32_FP16_MUL_ADD_OP(kernel)                              \
    RVV_FP32_FP16_MUL_ADD_OP(NTT_VL(sizeof(half) * 8, *, 1), kernel)           \
    RVV_FP32_FP16_MUL_ADD_OP(NTT_VL(sizeof(half) * 8, *, 2), kernel)           \
    RVV_FP32_FP16_MUL_ADD_OP(NTT_VL(sizeof(half) * 8, *, 4), kernel)

REGISTER_RVV_FP32_FP16_MUL_ADD_OP(mul_add_float16)

// mul_sub
#define MUL_SUB_FLOAT16(lmul, mlen)                                            \
    inline vfloat16m##lmul##_t mul_sub_float16(                                \
        const vfloat16m##lmul##_t &v1, const vfloat16m##lmul##_t &v2,          \
        const vfloat16m##lmul##_t &v3, const size_t vl) {                      \
        return __riscv_vfmsac_vv_f16m##lmul(v3, v1, v2, vl);                   \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mul_sub_float16(                                \
        const vfloat16m##lmul##_t &v1, const half &s2,                         \
        const vfloat16m##lmul##_t &v3, const size_t vl) {                      \
        return __riscv_vfmsac_vf_f16m##lmul(v3, s2, v1, vl);                   \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul##_t mul_sub_float16(                                \
        const half &s1, const vfloat16m##lmul##_t &v2,                         \
        const vfloat16m##lmul##_t &v3, const size_t vl) {                      \
        return __riscv_vfmsac_vf_f16m##lmul(v3, s1, v2, vl);                   \
    }

REGISTER_RVV_KERNEL(MUL_SUB_FLOAT16)
REGISTER_RVV_MUL_SUB_OP(half, mul_sub_float16)

#define MUL_SUB_FLOAT32_FLOAT16(lmul, lmulx2, mlen)                            \
    inline vfloat32m##lmulx2##_t mul_sub_float16(                              \
        const vfloat16m##lmul##_t &v1, const vfloat16m##lmul##_t &v2,          \
        const vfloat32m##lmulx2##_t &v3, const size_t vl) {                    \
        return __riscv_vfwmsac_vv_f32m##lmulx2(v3, v1, v2, vl);                \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t mul_sub_float16(                              \
        const vfloat16m##lmul##_t &v, const half &s,                           \
        const vfloat32m##lmulx2##_t &v3, const size_t vl) {                    \
        return __riscv_vfwmsac_vf_f32m##lmulx2(v3, s, v, vl);                  \
    }                                                                          \
                                                                               \
    inline vfloat32m##lmulx2##_t mul_sub_float16(                              \
        const half &s, const vfloat16m##lmul##_t &v,                           \
        const vfloat32m##lmulx2##_t &v3, const size_t vl) {                    \
        return __riscv_vfwmsac_vf_f32m##lmulx2(v3, s, v, vl);                  \
    }

REGISTER_RVV_FP16_x2_KERNEL(MUL_SUB_FLOAT32_FLOAT16);

#define RVV_FP32_FP16_MUL_SUB_OP(vl, kernel)                                   \
    template <>                                                                \
    struct mul_sub<ntt::vector<half, vl>, ntt::vector<half, vl>,               \
                   ntt::vector<float, 2, vl / 2>> {                            \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<half, vl> &v1,                            \
                   const ntt::vector<half, vl> &v2,                            \
                   const ntt::vector<float, 2, vl / 2> &v3) const noexcept {   \
            return kernel(v1, v2, v3, vl);                                     \
        }                                                                      \
    };                                                                         \
                                                                               \
    template <>                                                                \
    struct mul_sub<ntt::vector<half, vl>, half,                                \
                   ntt::vector<float, 2, vl / 2>> {                            \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const ntt::vector<half, vl> &v1, const half &s2,            \
                   const ntt::vector<float, 2, vl / 2> &v3) const noexcept {   \
            return kernel(v1, s2, v3, vl);                                     \
        }                                                                      \
    };                                                                         \
                                                                               \
    template <>                                                                \
    struct mul_sub<half, ntt::vector<half, vl>,                                \
                   ntt::vector<float, 2, vl / 2>> {                            \
        ntt::vector<float, 2, vl / 2>                                          \
        operator()(const half &s1, const ntt::vector<half, vl> &v2,            \
                   const ntt::vector<float, 2, vl / 2> &v3) const noexcept {   \
            return kernel(s1, v2, v3, vl);                                     \
        }                                                                      \
    };

#define REGISTER_RVV_FP32_FP16_MUL_SUB_OP(kernel)                              \
    RVV_FP32_FP16_MUL_SUB_OP(NTT_VL(sizeof(half) * 8, *, 1), kernel)           \
    RVV_FP32_FP16_MUL_SUB_OP(NTT_VL(sizeof(half) * 8, *, 2), kernel)           \
    RVV_FP32_FP16_MUL_SUB_OP(NTT_VL(sizeof(half) * 8, *, 4), kernel)

REGISTER_RVV_FP32_FP16_MUL_SUB_OP(mul_sub_float16)

// where
#define WHERE_FLOAT16(lmul1, lmul2, mlen)                                      \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const vbool##mlen##_t &condition, const half &x, const half &y,        \
        const size_t vl) {                                                     \
        auto y_broadcast = __riscv_vfmv_v_f_f16m##lmul1(y, vl);                \
        return __riscv_vfmerge_vfm_f16m##lmul1(y_broadcast, x, condition, vl); \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const bool &condition, const half &x, const vfloat16m##lmul1##_t &y,   \
        const size_t vl) {                                                     \
        auto cond_brct = __riscv_vmv_v_x_i16m##lmul1(condition, vl);           \
        vbool##mlen##_t mask =                                                 \
            __riscv_vmsne_vx_i16m##lmul1##_b##mlen(cond_brct, 0, vl);          \
        return __riscv_vfmerge_vfm_f16m##lmul1(y, x, mask, vl);                \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const bool &condition, const vfloat16m##lmul1##_t &x, const half &y,   \
        const size_t vl) {                                                     \
        auto cond_brct = __riscv_vmv_v_x_i16m##lmul1(condition, vl);           \
        vbool##mlen##_t mask =                                                 \
            __riscv_vmsne_vx_i16m##lmul1##_b##mlen(cond_brct, 0, vl);          \
        auto y_broadcast = __riscv_vfmv_v_f_f16m##lmul1(y, vl);                \
        return __riscv_vmerge_vvm_f16m##lmul1(y_broadcast, x, mask, vl);       \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const vbool##mlen##_t &condition, const vfloat16m##lmul1##_t &x,       \
        const vfloat16m##lmul1##_t &y, const size_t vl) {                      \
        return __riscv_vmerge_vvm_f16m##lmul1(y, x, condition, vl);            \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const vbool##mlen##_t &condition, const half &x,                       \
        const vfloat16m##lmul1##_t &y, const size_t vl) {                      \
        return __riscv_vfmerge_vfm_f16m##lmul1(y, x, condition, vl);           \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const vbool##mlen##_t &condition, const vfloat16m##lmul1##_t &x,       \
        const half &y, const size_t vl) {                                      \
        auto y_broadcast = __riscv_vfmv_v_f_f16m##lmul1(y, vl);                \
        return __riscv_vmerge_vvm_f16m##lmul1(y_broadcast, x, condition, vl);  \
    }                                                                          \
                                                                               \
    inline vfloat16m##lmul1##_t where_float16(                                 \
        const bool &condition, const vfloat16m##lmul1##_t &x,                  \
        const vfloat16m##lmul1##_t &y, const size_t vl) {                      \
        auto cond_brct = __riscv_vmv_v_x_i16m##lmul1(condition, vl);           \
        vbool##mlen##_t mask =                                                 \
            __riscv_vmsne_vx_i16m##lmul1##_b##mlen(cond_brct, 0, vl);          \
        return __riscv_vmerge_vvm_f16m##lmul1(y, x, mask, vl);                 \
    }

REGISTER_RVV_KERNEL_2_1(WHERE_FLOAT16)
REGISTER_RVV_WHERE_OP(half, where_float16)
} // namespace nncase::ntt::ops
