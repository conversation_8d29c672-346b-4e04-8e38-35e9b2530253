<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    <TargetFramework>net8.0</TargetFramework>
    <CodeAnalysisRuleSet>$(MSBuildThisFileDirectory)/tools/StyleCopAnalyzers.ruleset</CodeAnalysisRuleSet>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <Nullable>enable</Nullable>
    <NoWarn>$(NoWarn);MSB3270;CS0659;CS0661</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)' == 'Release'">
    <TreatWarningsAsErrors>true</TreatWarningsAsErrors>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="ISLSharp" Version="1.0.5" />
    <PackageVersion Include="Avalonia" Version="11.0.2" />
    <PackageVersion Include="Avalonia.Desktop" Version="11.0.2" />
    <PackageVersion Include="Avalonia.Themes.Fluent" Version="11.0.2" />
    <PackageVersion Include="Avalonia.Fonts.Inter" Version="11.0.2" />
    <PackageVersion Include="Avalonia.ReactiveUI" Version="11.0.2" />
    <PackageVersion Include="Clawfoot.Extensions.Newtonsoft" Version="0.1.0" />
    <PackageVersion Include="MessageBox.Avalonia" Version="3.1.2" />
    <PackageVersion Include="CommunityToolkit.Mvvm" Version="8.2.1" />
    <PackageVersion Include="Google.OrTools" Version="9.14.6206" />
    <PackageVersion Include="AnyTensorFlow.NET" Version="0.70.1" />
    <PackageVersion Include="BitFields" Version="0.1.0" />
    <PackageVersion Include="CommunityToolkit.HighPerformance" Version="8.2.2" />
    <PackageVersion Include="DryIoc.dll" Version="5.4.3" />
    <PackageVersion Include="DryIoc.Microsoft.DependencyInjection" Version="6.2.0" />
    <PackageVersion Include="Extension.Mathematics" Version="1.2.12" />
    <PackageVersion Include="Fody" Version="6.8.1" />
    <PackageVersion Include="GiGraph.Dot" Version="3.0.1" />
    <PackageVersion Include="Google.Protobuf" Version="3.27.3" />
    <PackageVersion Include="Grpc.Tools" Version="2.65.0" />
    <PackageVersion Include="Humanizer.Core" Version="2.14.1" />
    <PackageVersion Include="LanguageExt.Core" Version="4.4.9" />
    <PackageVersion Include="MagicalTensorflowLib" Version="0.0.2" />
    <PackageVersion Include="MagicalTensorflowLibOSX-ARM64" Version="0.0.4" />
    <PackageVersion Include="MethodBoundaryAspect.Fody" Version="2.0.149" />
    <PackageVersion Include="Microsoft.AspNetCore.Components" Version="8.0.7" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Razor.Extensions" Version="6.0.32" />
    <PackageVersion Include="Microsoft.CodeAnalysis.CSharp" Version="4.7.0" />
    <PackageVersion Include="Microsoft.CodeAnalysis.Analyzers" Version="3.3.4" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Logging.Abstractions" Version="8.0.1" />
    <PackageVersion Include="Microsoft.Extensions.Options" Version="8.0.2" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageVersion Include="Microsoft.TestPlatform.ObjectModel" Version="17.10.0" />
    <PackageVersion Include="NetFabric.Hyperlinq" Version="3.0.0-beta48" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Nncase.FlatBuffers" Version="2.0.0" />
    <PackageVersion Include="NumSharp" Version="0.30.0" />
    <PackageVersion Include="OrtKISharp" Version="0.0.2" />
    <PackageVersion Include="QuikGraph" Version="2.5.0" />
    <PackageVersion Include="QuikGraph.Graphviz" Version="2.5.0" />
    <PackageVersion Include="Razor.Templating.Core" Version="2.0.0" />
    <PackageVersion Include="RazorLight" Version="2.3.0" />
    <PackageVersion Include="StyleCop.Analyzers" Version="1.2.0-beta.556" />
    <PackageVersion Include="System.CommandLine.Hosting" Version="0.4.0-alpha.22272.1" />
    <PackageVersion Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
    <PackageVersion Include="System.Linq.Async" Version="6.0.1" />
    <PackageVersion Include="System.Reactive" Version="6.0.0" />
    <PackageVersion Include="xunit" Version="2.9.0" />
    <PackageVersion Include="xunit.analyzers" Version="1.15.0" />
    <PackageVersion Include="xunit.assert" Version="2.9.0" />
    <PackageVersion Include="Xunit.Combinatorial" Version="1.6.24" />
    <PackageVersion Include="xunit.core" Version="2.9.0" />
    <PackageVersion Include="Xunit.DependencyInjection" Version="9.3.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageVersion Include="xunit.v3.assert" Version="0.2.0-pre.69" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="StyleCop.Analyzers">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>
  <ItemGroup>
    <AdditionalFiles Include="$(MSBuildThisFileDirectory)/tools/stylecop.json" />
  </ItemGroup>
</Project>