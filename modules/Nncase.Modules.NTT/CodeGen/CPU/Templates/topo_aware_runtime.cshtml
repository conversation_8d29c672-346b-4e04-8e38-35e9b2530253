@using System.Linq
@using NetFabric.Hyperlinq
@using Nncase
@model Nncase.CodeGen.NTT.NTTTargetOptionsModel
@{
  var hierarchy = Model.Options.Hierarchies[0];
  var hierarchyNames = Model.Options.HierarchyNames;
  var worldSize = (int)TensorUtilities.GetProduct(hierarchy);
  var combinations = Nncase.Utilities.LinqUtility.Combination(hierarchy.Length).Select(i => i.ToArray()).ToArray();

  string GetName(IEnumerable<int> axes, string prefix = "group_") {
    return prefix + string.Join("_", Enumerable.Range(0, hierarchy.Length).Select(i => (axes.Contains(i) ? "r" : string.Empty) + Model.Options.HierarchyNames[i]));
  }
}

#pragma once
#include <nncase/ntt/ntt.h>
#include <thread>
#include <barrier>

/**
 * @@brief topology aware runtime
 * 
 */
namespace tar {
  @foreach (var comb in combinations)
  {
    var groupSize = (int)TensorUtilities.GetProduct(comb.Select(i => hierarchy[i]).ToArray());
    var groups = worldSize / groupSize;
    
    var shape = hierarchy.ToArray();
    var groupName = GetName(comb);
    foreach (var i in comb) {
      shape[i] = 1;
    }
    var groupRawName = groupName + "_raw";

@:std::barrier<> @(groupRawName)[@(groups)] {
    @for (int i = 0; i < groups; i++) 
    {
  @:std::barrier(@(groupSize)), 
    }
@:};
@:auto @(groupName) = nncase::ntt::make_tensor_view_from_address<std::barrier<>>(@(groupRawName), nncase::ntt::fixed_shape_v<@(string.Join(",", shape))>);
@:
  }

@if (Model.CollectivePoolSize > 0) {
@:alignas(@Model.Alignment) uint8_t collective_pool_ptr[@Model.CollectivePoolSize];
} else {
@:alignas(@Model.Alignment) uint8_t collective_pool_ptr[1];
}

enum reduce_kind {
@foreach(var comb in combinations) {
@:  @(GetName(comb, string.Empty)) = @Html.Raw(string.Join(" | ", comb.Select(axis => $"(1 << {hierarchy.Length - axis})"))),
}
};

constexpr std::array<size_t, @(hierarchy.Length)> Hierarchy = {@(string.Join(", ", hierarchy))};
auto src_ptr_tensor = nncase::ntt::make_tensor<void *>(nncase::ntt::fixed_shape_v<@(string.Join(",", hierarchy))>);
auto dest_ptr_tensor = nncase::ntt::make_tensor<void *>(nncase::ntt::fixed_shape_v<@(string.Join(",", hierarchy))>);
}

/**
 * @@brief topology aware collective
 * 
 */
namespace tac {

using namespace nncase;

template <ntt::Shape GlobalShape, ntt::Shape Index, ntt::Tensor TDst>
void tensor_boxing_load_sync(const GlobalShape &global_shape, const Index &index, TDst &dest)
{
    using TOutBase = std::decay_t<TDst>;
    using TElem = typename TOutBase::element_type;
    auto gtensor = ntt::make_tensor_view<TElem>((TElem *)tar::collective_pool_ptr, global_shape);
    ntt::tensor_copy_sync(gtensor.view(index, dest.shape()), dest);
    tar::@(GetName(Enumerable.Range(0, hierarchy.Length)))(@(string.Join(",", Enumerable.Repeat("0", hierarchy.Length)))).arrive_and_wait();
}

template <ntt::Shape GlobalShape, ntt::Shape Index, ntt::Tensor TSrc>
void tensor_boxing_store_sync(const GlobalShape &global_shape, const Index &index, TSrc &src)
{
    using TSrcBase = std::decay_t<TSrc>;
    using TElem = typename TSrcBase::element_type;
    auto gtensor = ntt::make_tensor_view<TElem>((TElem *)tar::collective_pool_ptr, global_shape);
    ntt::tensor_copy_sync(src, gtensor.view(index, src.shape()));
    tar::@(GetName(Enumerable.Range(0, hierarchy.Length)))(@(string.Join(",", Enumerable.Repeat("0", hierarchy.Length)))).arrive_and_wait();
}

namespace detail {
template <tar::reduce_kind Kind> class group_hierarchy_getter;

@foreach(var comb in combinations) {
@:template <> class group_hierarchy_getter<tar::reduce_kind::@(GetName(comb, string.Empty))> {
  var shape = Enumerable.Range(0, hierarchy.Length).Select(i => comb.Contains(i) ?  hierarchy[i] : 1).ToArray();
@:public:
@:    static constexpr auto group_hierarchy = ntt::fixed_shape_v<@(string.Join(", ", shape))>;
@:};
}

template <ntt::reduce_op Op, tar::reduce_kind Kind>
class tensor_reduce_sync_impl {
  public:
    void reduce_group_sync() const noexcept {
        @foreach(var comb in combinations) {
          var reduce_group_index = string.Join(", ", Enumerable.Range(0, hierarchy.Length).Select(i => comb.Contains(i) ? "0" : "ntt::distributed::" + hierarchyNames[i] + "id()"));
        @:if constexpr (Kind == tar::reduce_kind::@(GetName(comb, string.Empty))) {
        @:    tar::@(GetName(comb))(@(reduce_group_index)).arrive_and_wait();
        @:} 
        @:else
        }
        {
            static_assert(Kind == -1, "not support this Kind!");
        }
    }

    template <ntt::Shape TIndexInGroup, ntt::Shape TIndexInGlobal>
    constexpr auto index_group2global(const TIndexInGroup &index_in_group, const TIndexInGlobal &index_in_global) const noexcept {
        return ntt::generate_shape<TIndexInGlobal::rank()>([&](auto axis) {
            if constexpr (Kind & (1 << (TIndexInGlobal::rank() - axis))) {
                return index_in_group[axis];
            } else {
                return index_in_global[axis];
            }
        });
    }

    template <ntt::Shape TIndexInGlobal>
    constexpr auto index_global2group(const TIndexInGlobal &index_in_global) const noexcept {
        return ntt::generate_shape<TIndexInGlobal::rank()>([&](auto axis) {
            if constexpr (Kind & (1 << (TIndexInGlobal::rank() - axis))) {
                return index_in_global[axis];
            } else {
                return dim_zero;
            }
        });
    }

    static constexpr auto get_group_size() {
        size_t group_size = 1;
        for (size_t i = 1; i <= tar::Hierarchy.size(); i++) {
            if (Kind & (1 << i)) {
                group_size *= tar::Hierarchy[tar::Hierarchy.size() - i];
            }
        }
        return group_size;
    }

    @{
      var cur_index = string.Join(", ", Enumerable.Range(0, hierarchy.Length).Select(i => "ntt::distributed::" + hierarchyNames[i] + "id()"));
    }

    template <class TSliceIn, class TSliceOut>
    void reduce_impl(TSliceIn &local, TSliceIn &remote, TSliceOut &dest) {
        if constexpr (Op == ntt::reduce_op::max) {
            ntt::binary<ntt::ops::max>(local, remote, dest);
        } else if constexpr (Op == ntt::reduce_op::sum ||
                             Op == ntt::reduce_op::mean) {
            ntt::binary<ntt::ops::add>(local, remote, dest);
        } else if constexpr (Op == ntt::reduce_op::min) {
            ntt::binary<ntt::ops::min>(local, remote, dest);
        } else if constexpr (Op == ntt::reduce_op::prod) {
            ntt::binary<ntt::ops::mul>(local, remote, dest);
        }
    }

    template <class TIn, class TOut> void operator()(TIn &src, TOut &&dest) {
        // collect all tensors pointer for access tensor from other nodes.
        using TElem = typename TIn::element_type;
        using TOutBase = std::decay_t<TOut>;
        constexpr size_t Rank = TIn::rank();
        constexpr auto group_hierarchy = group_hierarchy_getter<Kind>::group_hierarchy;
        auto cur_index = ntt::make_shape(@(cur_index));
        auto cur_index_g = index_global2group(cur_index);
        tar::src_ptr_tensor(cur_index) =
            reinterpret_cast<void *>(src.elements().data());
        tar::dest_ptr_tensor(cur_index) =
            reinterpret_cast<void *>(dest.elements().data());
        reduce_group_sync();

        // according to the group size split the tensor.
        // todo should using better split strategy.
        constexpr auto group_size = ntt::fixed_dim_v<get_group_size()>;
        const auto axis = [&] {
            dim_t axis = -1;
            loop<Rank>([&](auto i) {
                if (axis == -1 && src.shape()[i] >= group_size) {
                    axis = i;
                }
            });
            if (axis == -1) {
                axis = 0;
            }
            return axis;
        }();

        auto remain = src.shape()[axis] % (group_size);
        auto frac = src.shape()[axis] / (group_size);

        auto node_number_g = ntt::linear_offset(cur_index_g, group_hierarchy);

        // reduce-scatter, communicate (group_size - 1) times
        for (auto i = 0; i < group_size - 1; i++) 
        {
            auto new_shape = ntt::generate_shape<Rank>([&](auto j) {
                if (j == axis) {
                    return ntt::where(node_number_g == group_size - 1, frac + remain, frac);
                } else {
                    return (dim_t)src.shape()[j];
                }
            });
            auto starts = ntt::generate_shape<Rank>([&](auto j) {
                if (j == axis) {
                    return node_number_g * frac;
                } else {
                    return (dim_t)0;
                }
            });
            auto viewed_src1_tensor = src.view(starts, new_shape);
            auto viewed_dest_tensor = dest.view(starts, new_shape);

            auto next_index_g = ntt::unravel_index((node_number_g + i + 1) % group_size, group_hierarchy);

            // keep the non-reduce axis invariant.
            auto next_index = index_group2global(next_index_g, cur_index);

            auto src2_tensor = ntt::make_tensor_view_from_address<TElem>(
                (TElem *)tar::src_ptr_tensor(next_index), src.shape(),
                src.strides());
            auto viewed_src2_tensor = src2_tensor.view(starts, new_shape);

            if (i == 0) {
                reduce_impl(viewed_src1_tensor, viewed_src2_tensor,
                            viewed_dest_tensor);
            } else {
                reduce_impl(viewed_dest_tensor, viewed_src2_tensor,
                            viewed_dest_tensor);
            }
        }

        reduce_group_sync();

        // all gather
        for (size_t i = 0; i < group_size - 1; i++) {
            auto offset = (node_number_g + i + 1) % (group_size);
            auto src_index_g = ntt::unravel_index(offset % group_size, group_hierarchy);
            auto src_index = index_group2global(src_index_g, cur_index);

            auto src_tensor = ntt::make_tensor_view_from_address<TElem>(
                (TElem *)tar::dest_ptr_tensor(src_index), dest.shape(),
                dest.strides());
            auto starts = ntt::generate_shape<Rank>([&](auto j) {
                if (j == axis) {
                    return offset * frac;
                } else {
                    return (dim_t)0;
                }
            });
            auto new_shape = ntt::generate_shape<Rank>([&](auto j) {
                if (j == axis) {
                    return ntt::where(offset == group_size - 1, frac + remain, frac);
                } else {
                    return (dim_t)src.shape()[j];
                }
            });
            auto viewed_src_tensor = src_tensor.view(starts, new_shape);
            auto viewed_dest_tensor = dest.view(starts, new_shape);
            ntt::tensor_copy_async(viewed_src_tensor, viewed_dest_tensor);
        }

        ntt::tensor_copy_wait<void>();
        reduce_group_sync();

        if (Op == ntt::reduce_op::mean) {
            auto numerator = (element_or_scalar_t<TElem>)(size_t)group_size;
            ntt::binary<ntt::ops::div>(dest, ntt::make_tensor_view_from_address(&numerator, ntt::fixed_shape_v<>), dest);
        }
    }
};
} // namespace detail

template <ntt::reduce_op Op, tar::reduce_kind Kind, class TIn, class TOut>
void tensor_reduce_sync(TIn &input, TOut &&output) {
    detail::tensor_reduce_sync_impl<Op, Kind> impl;
    impl(input, output);
}
} // namespace tac
