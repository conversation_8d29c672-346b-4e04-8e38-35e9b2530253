﻿@model Nncase.CodeGen.NTT.BinaryKernelTemplateModel
@{
    string BinaryToCFunction(BinaryOp op) =>
                op switch
                {
                    BinaryOp.Add => "ops::add",
                    BinaryOp.Sub => "ops::sub",
                    BinaryOp.Mul => "ops::mul",
                    BinaryOp.Div => "ops::div",
                    BinaryOp.Mod => "ops::mod",
                    BinaryOp.Min => "ops::min",
                    BinaryOp.Max => "ops::max",
                    BinaryOp.Pow => "ops::pow",
                    _ => throw new NotSupportedException($"Unsupported binary: {op}."),
                };
    string postOps = Model.Arguments[3].Symbol.Name;
    postOps = postOps == "nullptr" ? "" :  ("," + postOps);
}
binary<@BinaryToCFunction(Model.BinaryOp)@(postOps)>(@Html.Raw(Model.Arguments[0].Symbol.Name), @Html.Raw(Model.Arguments[1].Symbol.Name), @Html.Raw(Model.Arguments[2].Symbol.Name));
