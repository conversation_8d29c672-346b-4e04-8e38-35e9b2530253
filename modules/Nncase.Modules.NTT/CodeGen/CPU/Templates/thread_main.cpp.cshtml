@using System.Linq
@using NetFabric.Hyperlinq
@using Nncase
@using Nncase.IR
@model Nncase.CodeGen.NTT.KernelMainModel
@{
  var inputCount = Model.PrimFunction.Parameters.Length;
}

@foreach(var (s,i) in Model.Options.MemoryCapacities.Select((s,i) => (s,i)).SkipLast(1)){
@:uint8_t L@(i+1)Data[@(s)];
}

extern "C" void thread_main(const nncase::ntt::runtime::thread_inout_desc *input_descs,
  nncase::ntt::runtime::thread_inout_desc *const output_descs,
  const std::byte *rdata,
  const std::byte *thread_local_rdata,
  const std::byte *block_local_rdata,
  std::byte *thread_local_data,
  std::byte *block_local_data,
  std::byte *output) {
  /* prepare inputs */
  @{
    var names = new List<string>();
  }
  @foreach(var (input,i) in Model.PrimFunction.Parameters.ToArray().Select((input,i)=>(input,i)))
  {
    var name = IRHelpers.GetIdentityName(input.Name);
    if (input is DimVar)
    {
      names.Add(name);
  @:int64_t @Html.Raw(name) = *(int64_t *)input_descs[@i].data;
    }
    else
    {
      names.Add(name);
      var shape = input.CheckedShape;
      var rank = shape.Rank;
      var sizeStr = $"input_descs[{i}].size / {input.CheckedDataType.SizeInBytes}";
      var elemType = input.CheckedDataType.ToC();
      var dimStr = $"make_shape({string.Join(", ", Enumerable.Range(0, rank).Select(d => shape[d].IsFixed ? $"{shape[d].FixedValue}_dim" : $"input_descs[{i}].shape[{d}]"))})";
      var strideStr = $"make_strides({string.Join(", ", Enumerable.Range(0, rank).Select(d => $"input_descs[{i}].strides[{d}]"))})";
      if (input.CheckedDataType is ReferenceType referenceType) {
        if (referenceType.ElemType is Nncase.IR.NN.PagedAttentionKVCacheType kvcacheType) {
          var size = TensorUtilities.GetProduct(shape.ToValueArray()); // todo: support dynamic shape
          var kv_tensor_shape = kvcacheType.Config.AxisPolicies.Select(p => p.Axes.Select(i => Model.Options.Hierarchies[0][i]).Product()).ToArray();
          var kv_tensor_size = kv_tensor_shape.Product();
          if (kv_tensor_size > 128) {
            throw new NotSupportedException("KVCache tensor size is too large!");
          }
          var placement = new Placement(Model.Options.Hierarchies[0], Model.Options.HierarchyNames, Model.Options.HierarchyKind);

  @:std::span<runtime::thread_paged_attention_kv_cache_desc> p@(name)_descs((runtime::thread_paged_attention_kv_cache_desc*)input_descs[@i].data, input_descs[@i].size / sizeof(runtime::thread_paged_attention_kv_cache_desc));
  @:using paged_attention_kv_cache_mesh_t = @Html.Raw(KernelUtility.PlacementToC(placement));
  @:const auto paged_attention_kv_cache_config = @Html.Raw(kvcacheType.Config.ToC());
  @:using paged_attention_kv_cache_config_t = std::decay_t<decltype(paged_attention_kv_cache_config)>;
  @:using paged_attention_kv_cache_t = caching::paged_attention_kv_cache<paged_attention_kv_cache_mesh_t, paged_attention_kv_cache_config_t>;
  @:using kv_storage_type_t = typename paged_attention_kv_cache_t::kv_storage_type_t;
  @:alignas(paged_attention_kv_cache_t) std::byte p@(name)[sizeof(paged_attention_kv_cache_t) * @size];
  @:for (size_t i = 0; i < @size; ++i) {
    @:auto desc = p@(name)_descs[i];
    @:auto kv_addrs = make_tensor_view_from_address((kv_storage_type_t **)desc.kv_cache_addrs.data(), paged_attention_kv_cache_t::kv_addrs_shape);
    @:auto ptr = (paged_attention_kv_cache_t*)p@(name) + i;
    @:new (ptr) paged_attention_kv_cache_t(desc.num_seqs, desc.num_tokens, make_tensor_view_from_address(desc.context_lens, make_shape(desc.context_lens_size)), make_tensor_view_from_address(desc.seq_lens, make_shape(desc.seq_lens_size)), make_tensor_view_from_address(desc.block_table, make_shape(desc.block_table_shape[0], desc.block_table_shape[1], paged_attention_kv_cache_t::id_length)), make_tensor_view_from_address(desc.slot_mapping, make_shape(desc.slot_mapping_shape[0], paged_attention_kv_cache_t::id_length)), kv_addrs);
  @:}
  @:auto @(name) = make_tensor_view_from_address((paged_attention_kv_cache_t *)p@(name), @Html.Raw(dimStr), @Html.Raw(strideStr));
        }
      } else {
  @:auto @(name) = make_tensor_view_from_address((const @Html.Raw(elemType) *)input_descs[@i].data, @Html.Raw(dimStr), @Html.Raw(strideStr));
      }
    }
  @:
  }

  @if (Model.Options.Hierarchies.Length > 1) {
    throw new NotSupportedException($"not support multi form topology!");
  }
  
  @(Model.PrimFunction.Name)(@(string.Concat(names.Select(x => $"{x}, ")))rdata, thread_local_rdata, block_local_rdata, thread_local_data, block_local_data, output, output_descs);
}

#ifdef NNCASE_STANDALONE
int main([[maybe_unused]] int argc, [[maybe_unused]] char** argv) {
  std::byte *inputs[@inputCount];
  size_t align = @(Model.Alignment);
  @foreach(var (b,i) in Model.PrimFunction.Parameters.ToArray().OfType<Nncase.TIR.Buffer>().Select((b,i)=>(Model.GetInfo(b),i)))
  {
  @:inputs[@i] = (std::byte *)nncase::ntt::runtime::thread_alloc(sizeof(@Html.Raw(b.ElemType)) * @b.Size, align);
  }

  std::byte* rdata = (std::byte *)nncase::ntt::runtime::thread_alloc(@Model.RDataSize, align);
  std::byte* thread_local_rdata = (std::byte *)nncase::ntt::runtime::thread_alloc(@Model.ThreadLocalRdataPoolSize, align);
  uint64_t thread_local_rdata_header[@Model.Options.Hierarchies[0][^1] * 2];
  for (size_t tid = 0; tid < tdim(); tid++) {
    thread_local_rdata_header[tid * 2] = tid * ( @Model.ThreadLocalRdataPoolSize / tdim());
  }
  std::byte* block_local_rdata = (std::byte *)nncase::ntt::runtime::thread_alloc(@Model.BlockLocalRdataPoolSize, align);

#ifdef __APPLE__
  pthread_key_t cpu_thread_context_key_ = {};
  pthread_key_create(&cpu_thread_context_key_, [](void *ptr) { delete (nncase::ntt::runtime::cpu_thread_context_t *)ptr; });
#endif

  std::vector<std::thread> blocks;
  for (size_t cid = 0; cid < cdim(); cid++) {
    for (size_t bid = 0; bid < bdim(); bid++) {
      blocks.emplace_back([cid, bid, inputs, rdata, thread_local_rdata_header, thread_local_rdata, block_local_rdata
#ifdef __APPLE__
      , &cpu_thread_context_key_
#endif
      ] {
        nncase::ntt::runtime::cpu_block_entry_params_t block_entry_params{
            .tdim = tdim(),
            .bdim = bdim(),
            .cdim = cdim(),
            .bid = bid,
            .cid = cid,
            .cpu_id_offset = (cid * bdim() + bid) * tdim(),
            .input_descs = nullptr,
            .output_descs = nullptr,
            .rdata = rdata,
            .thread_local_rdata_header = thread_local_rdata_header,
            .thread_local_rdata = thread_local_rdata,
            .block_local_rdata = block_local_rdata,
#ifdef __APPLE__
            .cpu_thread_context_key = cpu_thread_context_key_,
#endif
        };

        block_entry(block_entry_params);
      });
    }
  }

  for (auto &block : blocks) {
    block.join();
  }
    
    
#ifdef __APPLE__
  pthread_key_delete(cpu_thread_context_key_);
#endif

  for (size_t i = 0; i < @inputCount; i++) {
    nncase::ntt::runtime::thread_free(inputs[i]);
  }
  nncase::ntt::runtime::thread_free(rdata);
  return 0;
}
#endif
