#!/bin/bash
rm -rf runtime
# conan install . -of runtime --build=missing -s build_type=Release -pr:a=../nncase/toolchains/riscv64-linux.profile.jinja -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"

# export WORK_ROOT=/data/huochenghai/k800/k800-software_v5/k800-software/
# export PATH=$PATH:/data/huochenghai/k800/riscv-gnu-toolchain/_install/bin/
# export RISCV_ROOT_PATH=/data/huochenghai/k800/riscv-gnu-toolchain/_install/

# nncase_rtt_runime=${WORK_ROOT}/work_cache/install/nncase
# # export K800_LINUX_SDK_DIR=${WORK_ROOT}/k800_sdk
# # K800_LINUX_SDK_DIR=/data/huochenghai/k800/test/output/qemu/
# # sdk v0.1
# K800_LINUX_SDK_DIR=/data/huochenghai/k800/k800-software_v5/k800-software/duca/output/qemu/
# # K800_LINUX_SDK_DIR=/data/huochenghai/k800/k800-software_v5/k800-software/new_duca/duca/output/qemu/

# pushd runtime/build/Release || exit
# cmake  -DCMAKE_BUILD_TYPE=Release \
#        -DCMAKE_SYSTEM_PROCESSOR=riscv64 \
#        -DCMAKE_SYSTEM_NAME=Linux \
#        -DCMAKE_C_COMPILER=riscv64-unknown-linux-gnu-gcc \
#        -DCMAKE_CXX_COMPILER=riscv64-unknown-linux-gnu-g++ \
#        -DBUILDING_RUNTIME=1 \
#        -DENABLE_K80_RUNTIME=1 \
#        -DENABLE_OPENMP=0 \
#        -DCMAKE_TOOLCHAIN_FILE=generators/conan_toolchain.cmake \
#        -DDEFAULT_BUILTIN_RUNTIMES=off \
#        -DBUILD_PYTHON_BINDING=off \
#        -DENABLE_OP_PROFILE=0 \
#        -DENABLE_XPU_RUNTIME=1 \
#        -DDISABLE_RVV=0 \
#        -Dnncaseruntime_DIR=${nncase_rtt_runime}/lib/cmake/nncaseruntime/ \
#        -DCMAKE_INSTALL_PREFIX=${WORK_ROOT}/nncase-k80/runtime/install \
#        -DK800_LINUX_SDK_DIR=${K800_LINUX_SDK_DIR} \
#        -S${WORK_ROOT}/nncase-k80 \
#        -B${WORK_ROOT}/nncase-k80/runtime/build/Release
       
conan install . -of runtime --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"

export WORK_ROOT=/data/huochenghai/k800/k800-software_v5/k800-software/

nncase_rtt_runime=${WORK_ROOT}/work_cache/install/nncase
# export K800_LINUX_SDK_DIR=${WORK_ROOT}/k800_sdk
DUCA_PATH=/data/huochenghai/k800/test/duca_041/output/qemu/duca/
# sdk v0.1
# K800_LINUX_SDK_DIR=/data/huochenghai/k800/k800-software_v5/k800-software/duca/output/qemu/
# K800_LINUX_SDK_DIR=/data/huochenghai/k800/k800-software_v5/k800-software/new_duca/duca/output/qemu/

pushd runtime/build/Release || exit
cmake  -DCMAKE_BUILD_TYPE=Release \
       -DCMAKE_C_COMPILER=gcc \
       -DCMAKE_CXX_COMPILER=g++ \
       -DBUILDING_RUNTIME=1 \
       -DENABLE_K80_RUNTIME=1 \
       -DENABLE_OPENMP=0 \
       -DCMAKE_TOOLCHAIN_FILE=generators/conan_toolchain.cmake \
       -DDEFAULT_BUILTIN_RUNTIMES=0 \
       -DBUILD_PYTHON_BINDING=off \
       -DENABLE_OP_PROFILE=0 \
       -DENABLE_XPU_RUNTIME=1 \
       -DDISABLE_RVV=0 \
       -Dnncaseruntime_DIR=${nncase_rtt_runime}/lib/cmake/nncaseruntime/ \
       -DCMAKE_INSTALL_PREFIX=${WORK_ROOT}/work_cache/install/nncase \
       -DDUCA_PATH=${DUCA_PATH} \
       -DSYS_MODE=1 \
       -S${WORK_ROOT}/nncase-k80 \
       -B${WORK_ROOT}/nncase-k80/runtime/build/Release

cmake --build . -j64
# rm -rf ${nncase_rtt_runime:?}/*
cmake --install . --prefix ${nncase_rtt_runime}
# cmake --install .
popd || exit 
