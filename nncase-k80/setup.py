from distutils.command.install_data import install_data
import imp
from posixpath import dirname, join
from setuptools import setup
from setuptools.command.install import install
from setuptools.command.install_scripts import install_scripts
from distutils.command.build import build as DistutilsBuild
from wheel.bdist_wheel import bdist_wheel
from distutils.spawn import find_executable
import shutil
import os
import platform
import sys
import io
import re
import time
import subprocess
from git.repo import Repo
# See ref: https://stackoverflow.com/a/51575996


class CMakeBuild(DistutilsBuild):
    def run(self):
        DistutilsBuild.run(self)

        self.announce("Preparing the build environment", level=3)

        sourcedir = '.'

        # cmake_args = ['-G', 'Ninja']
        # cmake_args += ['-DPython3_ROOT_DIR=' + os.path.dirname(sys.executable)]
        # if platform.system() == 'Windows':
        #     cmake_args += ['-DCMAKE_C_COMPILER=clang-cl']
        #     cmake_args += ['-DCMAKE_CXX_COMPILER=clang-cl']

        # cfg = 'Debug' if self.debug else 'Release'
        # build_args = ['--config', cfg]
        # cmake_args += ['-DCMAKE_BUILD_TYPE=' + cfg]
        # bin_dir = os.path.abspath(os.path.join(self.build_temp, 'install'))


        toolchain_arch = ""
        if platform.machine() == "AMD64" or platform.machine() == "x86_64":
            toolchain_arch = "x86_64"
        elif platform.machine() == "arm64":
            toolchain_arch = "aarch64"
        elif platform.machine() == "riscv64":
            toolchain_arch = "aarch64"

        toolchain_os = ""
        if platform.system() == "Windows":
            toolchain_os = "windows"
        elif platform.system() == "Linux":
            toolchain_os = "linux"
        elif platform.system() == "Darwin":
            toolchain_os = "macos"

        bin_dir = os.path.abspath(os.path.join(self.build_temp, 'install'))
        install_args = ['--prefix', bin_dir, '--component', 'nncase-runtime']
        host_toolchain_path = os.path.join(sourcedir, "toolchains", f"{toolchain_arch}-{toolchain_os}.profile.jinja")

        build_type = 'Debug' if self.debug else 'Release'
        build_dir = os.path.join(self.build_temp, build_type)

        if not os.path.exists(self.build_temp):
            os.makedirs(self.build_temp)

        # Now that the necessary directories are created, build

        self.announce("Configuring cmake project", level=3)

        # Change your cmake arguments below as necessary
        # Below is just an example set of arguments for building Blender as a Python module
        self.spawn(["conan", "remote", "add", "sunnycase", "https://conan.sunnycase.moe", "--index", "0", "--force"])
        self.spawn(["conan", "install", sourcedir, "--build=missing", "-s",
            "build_type="+build_type, f"-pr:a={host_toolchain_path}",
            "-o", "&:nncase_dir=./nncase/build/lib/cmake/nncase",
            "-o", "&:runtime=False", "-o", "&:python=True", "-o", "&:tests=False", "-o", f"&:python_root={os.path.dirname(sys.executable)}",
            "-c", f"tools.cmake.cmake_layout:build_folder={self.build_temp}"])
        self.spawn(['cmake', '-S', sourcedir, '-B', build_dir, "--preset", "conan-release"] )

        self.announce("Building binaries", level=3)

        self.spawn(["cmake", "--build", build_dir])
        self.spawn(["cmake", "--install", build_dir] + install_args)
        self.distribution.bin_dir = bin_dir

        # find nncase publish
        nncase_pub_dir = os.path.join(sourcedir, 'nncase', 'src', 'Nncase.Compiler', 'bin', 'Release', 'net8.0')
        nncase_libs = [os.path.basename(_lib) for root, _, files in
                       os.walk(nncase_pub_dir) for _lib in files if
                os.path.isfile(os.path.join(root, _lib)) and
                (os.path.splitext(_lib)[-1] in [".dll", ".so", ".dylib", ".json"] or
                _lib.startswith("lib"))]

        k230_pub_dir = os.path.join(sourcedir, 'modules', 'Nncase.Modules.XPU', 'bin', 'Release', 'net8.0')
        k230_libs = [os.path.join(root, _lib) for root, _, files in
                     os.walk(k230_pub_dir) for _lib in files if
                os.path.isfile(os.path.join(root, _lib)) and
                os.path.basename(_lib) not in nncase_libs and
                (os.path.splitext(_lib)[-1] in [".dll", ".so", ".dylib", ".json"] or
                _lib.startswith("lib"))]

        sharp_libs_dir = os.path.join(bin_dir, 'sharplibs')
        os.makedirs(sharp_libs_dir)
        for lib in k230_libs:
            shutil.copy(lib, os.path.join(sharp_libs_dir, os.path.basename(lib)))


class InstallCMakeLibsData(install_data):
    """
    Just a wrapper to get the install data into the egg-info
    Listing the installed files in the egg-info guarantees that
    all of the package files will be uninstalled when the user
    uninstalls your package through pip
    """

    def run(self):
        """
        Outfiles are the libraries that were built using cmake
        """

        # There seems to be no other way to do this; I tried listing the
        # libraries during the execution of the InstallCMakeLibs.run() but
        # setuptools never tracked them, seems like setuptools wants to
        # track the libraries through package data more than anything...
        # help would be appriciated

        self.outfiles = self.distribution.data_files


class InstallCMakeLibs(install):
    """
    Get the libraries from the parent distribution, use those as the outfiles
    Skip building anything; everything is already built, forward libraries to
    the installation step
    """

    def run(self):
        """
        Copy libraries from the bin directory and place them as appropriate
        """

        self.announce("Moving library files", level=3)

        # We have already built the libraries in the previous build_ext step

        self.skip_build = True

        bin_dir = self.distribution.bin_dir

        # Depending on the files that are generated from your cmake
        # build chain, you may need to change the below code, such that
        # your files are moved to the appropriate location when the installation
        # is run

        sharp_libs = [os.path.join(root, _lib) for root, _, files in
                os.walk(os.path.join(bin_dir, 'sharplibs')) for _lib in files if
                os.path.isfile(os.path.join(root, _lib)) and
                (os.path.splitext(_lib)[-1] in [".dll", ".so", ".dylib", ".json"]
                or _lib.startswith("lib"))
                and not _lib.endswith(".deps.json")]

        sharplibs_dir = os.path.join(self.install_platlib, 'nncase', 'modules', 'kpu')
        os.makedirs(sharplibs_dir)

        for lib in sharp_libs:
            shutil.move(lib, os.path.join(sharplibs_dir,
                                          os.path.basename(lib)))

        libs = [os.path.join(root, _lib) for root, _, files in
                os.walk(bin_dir) for _lib in files if
                os.path.isfile(os.path.join(root, _lib)) and
                os.path.splitext(_lib)[1] in [".dll", ".so", ".dylib", ".exe", ".sc", ".c"]
                and not (_lib.startswith("python") or _lib.startswith("_nncase"))]

        if not os.path.exists(self.install_platlib):
            os.makedirs(self.install_platlib)

        for lib in libs:
            shutil.move(lib, os.path.join(self.install_platlib,
                                          os.path.basename(lib)))

        # copy Runtime into modules/kpu
        runtime_dir = os.path.join('./', 'modules/Nncase.Modules.XPU/bin/Release/net8.0/linux-x64/Runtime')
        shutil.copytree(runtime_dir, os.path.join(sharplibs_dir, 'Runtime'))

        # Mark the libs for installation, adding them to
        # distribution.data_files seems to ensure that setuptools' record
        # writer appends them to installed-files.txt in the package's egg-info
        #
        # Also tried adding the libraries to the distribution.libraries list,
        # but that never seemed to add them to the installed-files.txt in the
        # egg-info, and the online recommendation seems to be adding libraries
        # into eager_resources in the call to setup(), which I think puts them
        # in data_files anyways.
        #
        # What is the best way?

        # These are the additional installation files that should be
        # included in the package, but are resultant of the cmake build
        # step; depending on the files that are generated from your cmake
        # build chain, you may need to modify the below code

        data_files = [os.path.join(self.install_platlib,
                                   os.path.basename(lib))
                                   for lib in libs]
        data_files += [os.path.join(sharplibs_dir,
                                   os.path.basename(lib))
                                   for lib in sharp_libs]

        self.distribution.data_files = data_files
        # Must be forced to run after adding the libs to data_files

        self.distribution.run_command("install_data")

        super().run()


class CMakeBdistWheel(bdist_wheel):
    def run(self):
        self.root_is_pure = False
        super().run()


class InstallCMakeScripts(install_scripts):
    """
    Install the scripts in the build dir
    """

    def run(self):
        """
        Copy the required directory to the build directory and super().run()
        """

        self.announce("Moving scripts files", level=3)

        # Scripts were already built in a previous step

        self.skip_build = True

        bin_dir = self.distribution.bin_dir

        scripts_dirs = [os.path.join(bin_dir, _dir) for _dir in
                        os.listdir(bin_dir) if
                        os.path.isdir(os.path.join(bin_dir, _dir))]

        for scripts_dir in scripts_dirs:

            shutil.move(scripts_dir,
                        os.path.join(self.build_dir,
                                     os.path.basename(scripts_dir)))

        # Mark the scripts for installation, adding them to
        # distribution.scripts seems to ensure that the setuptools' record
        # writer appends them to installed-files.txt in the package's egg-info

        self.distribution.scripts = scripts_dirs

        super().run()

def get_latest_tag_by_date(repo):
    # 获取所有标签及其创建时间
    tags_with_dates = []
    for tag in repo.tags:
        try:
            # 附注标签：使用标签对象的创建时间
            tag_date = tag.tag.tagged_date
        except (AttributeError, TypeError):
            # 轻量标签：使用标签指向的提交时间
            tag_date = tag.commit.committed_date

        tags_with_dates.append((tag_date, tag.name))

    # 如果没有标签，返回 None
    if not tags_with_dates:
        return None

    # 按时间降序排序（最新的在前）
    tags_with_dates.sort(key=lambda x: x[0], reverse=True)

    # 返回最新标签的名称
    return tags_with_dates[0][1]

def find_version():
    with io.open("CMakeLists.txt", encoding="utf8") as f:
        version_file = f.read()

    version_prefix = re.findall(r"NNCASE_K510_VERSION \"(.+)\"", version_file)

    if version_prefix:
        repo_path = os.getcwd()
        repo = Repo(repo_path)
        if repo.tags:
            latest_commit = subprocess.check_output(
                ['git', 'rev-parse', 'HEAD']).decode('utf-8').strip()
            latest_tag = get_latest_tag_by_date(repo)
            tagged_commit = subprocess.check_output(
                ['git', 'rev-list', '-n', '1', latest_tag]).decode('utf-8').strip()
            if latest_commit == tagged_commit:
                return latest_tag[1:]

        version_suffix = time.strftime("%Y%m%d", time.localtime())
        return version_prefix[0] + "." + version_suffix

    raise RuntimeError("Unable to find version string.")

setup(name='nncase-kpu',
      version=find_version(),
      packages=['nncase_kpu'],
      package_dir={'': 'python'},
      cmdclass={
          'build': CMakeBuild,
          'install_data': InstallCMakeLibsData,
          'install': InstallCMakeLibs,
          'bdist_wheel': CMakeBdistWheel
      }
      )
