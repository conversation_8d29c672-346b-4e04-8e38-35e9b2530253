﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.2.32602.215
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Cli", "..\nncase\src\Nncase.Cli\Nncase.Cli.csproj", "{48BE0B1E-C707-4B64-B148-3A5E90075132}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.CodeGen", "..\nncase\src\Nncase.CodeGen\Nncase.CodeGen.csproj", "{E1AA426A-BBBC-4F25-BCDC-3CB6CBC475E3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Core", "..\nncase\src\Nncase.Core\Nncase.Core.csproj", "{04E20AD1-FF08-4F76-8E8D-78577CF0B507}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Evaluator", "..\nncase\src\Nncase.Evaluator\Nncase.Evaluator.csproj", "{531731D9-DD9F-4997-BF26-DC80FE169914}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Graph", "..\nncase\src\Nncase.Graph\Nncase.Graph.csproj", "{E03D79CF-BD61-496B-A672-CB47AB0D3AA9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.IO", "..\nncase\src\Nncase.IO\Nncase.IO.csproj", "{91D2D961-F304-4F84-85BB-59164B2B2C0B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Importer", "..\nncase\src\Nncase.Importer\Nncase.Importer.csproj", "{144B6106-312B-493E-A719-38BE556B8166}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Schedule", "..\nncase\src\Nncase.Schedule\Nncase.Schedule.csproj", "{F234EF47-F96E-44DF-8160-0B760945618F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Simulator", "..\nncase\src\Nncase.Simulator\Nncase.Simulator.csproj", "{DBECD47F-31D3-4928-8D52-D760C7EC62BF}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Targets", "..\nncase\src\Nncase.Targets\Nncase.Targets.csproj", "{807F3D96-2DB5-416E-A321-74ACECFB919D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.EGraph", "..\nncase\src\Nncase.EGraph\Nncase.EGraph.csproj", "{9C4D3EFB-606D-4A96-AD31-5D02AB7C401F}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.SourceGenerator", "..\nncase\tools\Nncase.SourceGenerator\Nncase.SourceGenerator.csproj", "{7606A83A-4AD0-4392-A511-B8A12EA3C300}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Quantization", "..\nncase\src\Nncase.Quantization\Nncase.Quantization.csproj", "{0E14CC12-7662-49A2-AD2B-3E1A51CB764D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "TFLite.Schema", "..\nncase\third_party\tflite\TFLite.Schema.csproj", "{B27E50A1-A5BB-4A5C-994E-4F403DB35AB5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Onnx.Protobuf", "..\nncase\third_party\onnx\Onnx.Protobuf.csproj", "{8E8C5474-3735-4266-B602-1AB36FC959EC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Tests.TestFixture", "..\nncase\src\Nncase.Tests.TestFixture\Nncase.Tests.TestFixture.csproj", "{FCC7EFBC-DF13-4E6E-9546-EDA36EDA1EA2}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "nncase-public", "nncase-public", "{40978ECB-AE00-460F-85A1-0E01255FD961}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "third_party", "third_party", "{0F46D6BB-7831-4147-AE08-E50EC06C4FD8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tools", "tools", "{53A6F94D-AD9C-4315-B0E9-FCD3AA65C683}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{9C8F8393-462C-418B-87A8-45158B682964}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Compiler", "..\nncase\src\Nncase.Compiler\Nncase.Compiler.csproj", "{47F6C8B0-7569-4CA3-8055-EFBC792153E7}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Diagnostics", "..\nncase\src\Nncase.Diagnostics\Nncase.Diagnostics.csproj", "{6DC5ADC5-6DE2-478F-B8A0-487A78D51672}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "root", "root", "{01D0B014-1CCF-4D64-B643-2FEAD025D5CA}"
	ProjectSection(SolutionItems) = preProject
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Nncase.Passes", "..\nncase\src\Nncase.Passes\Nncase.Passes.csproj", "{A0EEEF6F-9EF5-4401-91A2-9DAD24662AA8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "modules", "modules", "{42F034AB-1247-465B-86FD-A330761E08D3}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "tests", "tests", "{CC866CA7-00E6-46C3-9BA1-2C33A9A58E5E}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Nncase.Modules.XPU", "modules\Nncase.Modules.XPU\Nncase.Modules.XPU.csproj", "{076560D1-A87D-421D-BC9A-50B2CCDFA759}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Nncase.Tests.XPU", "tests\Nncase.Tests.XPU\Nncase.Tests.XPU.csproj", "{D92ECC74-630C-43B3-8231-9FA078743701}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{48BE0B1E-C707-4B64-B148-3A5E90075132}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{48BE0B1E-C707-4B64-B148-3A5E90075132}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{48BE0B1E-C707-4B64-B148-3A5E90075132}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{48BE0B1E-C707-4B64-B148-3A5E90075132}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1AA426A-BBBC-4F25-BCDC-3CB6CBC475E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1AA426A-BBBC-4F25-BCDC-3CB6CBC475E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1AA426A-BBBC-4F25-BCDC-3CB6CBC475E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1AA426A-BBBC-4F25-BCDC-3CB6CBC475E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{04E20AD1-FF08-4F76-8E8D-78577CF0B507}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{04E20AD1-FF08-4F76-8E8D-78577CF0B507}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{04E20AD1-FF08-4F76-8E8D-78577CF0B507}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{04E20AD1-FF08-4F76-8E8D-78577CF0B507}.Release|Any CPU.Build.0 = Release|Any CPU
		{531731D9-DD9F-4997-BF26-DC80FE169914}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{531731D9-DD9F-4997-BF26-DC80FE169914}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{531731D9-DD9F-4997-BF26-DC80FE169914}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{531731D9-DD9F-4997-BF26-DC80FE169914}.Release|Any CPU.Build.0 = Release|Any CPU
		{E03D79CF-BD61-496B-A672-CB47AB0D3AA9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E03D79CF-BD61-496B-A672-CB47AB0D3AA9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E03D79CF-BD61-496B-A672-CB47AB0D3AA9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E03D79CF-BD61-496B-A672-CB47AB0D3AA9}.Release|Any CPU.Build.0 = Release|Any CPU
		{91D2D961-F304-4F84-85BB-59164B2B2C0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{91D2D961-F304-4F84-85BB-59164B2B2C0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{91D2D961-F304-4F84-85BB-59164B2B2C0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{91D2D961-F304-4F84-85BB-59164B2B2C0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{144B6106-312B-493E-A719-38BE556B8166}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{144B6106-312B-493E-A719-38BE556B8166}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{144B6106-312B-493E-A719-38BE556B8166}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{144B6106-312B-493E-A719-38BE556B8166}.Release|Any CPU.Build.0 = Release|Any CPU
		{F234EF47-F96E-44DF-8160-0B760945618F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F234EF47-F96E-44DF-8160-0B760945618F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F234EF47-F96E-44DF-8160-0B760945618F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F234EF47-F96E-44DF-8160-0B760945618F}.Release|Any CPU.Build.0 = Release|Any CPU
		{DBECD47F-31D3-4928-8D52-D760C7EC62BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DBECD47F-31D3-4928-8D52-D760C7EC62BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DBECD47F-31D3-4928-8D52-D760C7EC62BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DBECD47F-31D3-4928-8D52-D760C7EC62BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{807F3D96-2DB5-416E-A321-74ACECFB919D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{807F3D96-2DB5-416E-A321-74ACECFB919D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{807F3D96-2DB5-416E-A321-74ACECFB919D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{807F3D96-2DB5-416E-A321-74ACECFB919D}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C4D3EFB-606D-4A96-AD31-5D02AB7C401F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C4D3EFB-606D-4A96-AD31-5D02AB7C401F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C4D3EFB-606D-4A96-AD31-5D02AB7C401F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C4D3EFB-606D-4A96-AD31-5D02AB7C401F}.Release|Any CPU.Build.0 = Release|Any CPU
		{7606A83A-4AD0-4392-A511-B8A12EA3C300}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7606A83A-4AD0-4392-A511-B8A12EA3C300}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7606A83A-4AD0-4392-A511-B8A12EA3C300}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7606A83A-4AD0-4392-A511-B8A12EA3C300}.Release|Any CPU.Build.0 = Release|Any CPU
		{0E14CC12-7662-49A2-AD2B-3E1A51CB764D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0E14CC12-7662-49A2-AD2B-3E1A51CB764D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0E14CC12-7662-49A2-AD2B-3E1A51CB764D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0E14CC12-7662-49A2-AD2B-3E1A51CB764D}.Release|Any CPU.Build.0 = Release|Any CPU
		{B27E50A1-A5BB-4A5C-994E-4F403DB35AB5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B27E50A1-A5BB-4A5C-994E-4F403DB35AB5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B27E50A1-A5BB-4A5C-994E-4F403DB35AB5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B27E50A1-A5BB-4A5C-994E-4F403DB35AB5}.Release|Any CPU.Build.0 = Release|Any CPU
		{8E8C5474-3735-4266-B602-1AB36FC959EC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8E8C5474-3735-4266-B602-1AB36FC959EC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8E8C5474-3735-4266-B602-1AB36FC959EC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8E8C5474-3735-4266-B602-1AB36FC959EC}.Release|Any CPU.Build.0 = Release|Any CPU
		{FCC7EFBC-DF13-4E6E-9546-EDA36EDA1EA2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FCC7EFBC-DF13-4E6E-9546-EDA36EDA1EA2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FCC7EFBC-DF13-4E6E-9546-EDA36EDA1EA2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FCC7EFBC-DF13-4E6E-9546-EDA36EDA1EA2}.Release|Any CPU.Build.0 = Release|Any CPU
		{47F6C8B0-7569-4CA3-8055-EFBC792153E7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{47F6C8B0-7569-4CA3-8055-EFBC792153E7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{47F6C8B0-7569-4CA3-8055-EFBC792153E7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{47F6C8B0-7569-4CA3-8055-EFBC792153E7}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DC5ADC5-6DE2-478F-B8A0-487A78D51672}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DC5ADC5-6DE2-478F-B8A0-487A78D51672}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DC5ADC5-6DE2-478F-B8A0-487A78D51672}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DC5ADC5-6DE2-478F-B8A0-487A78D51672}.Release|Any CPU.Build.0 = Release|Any CPU
		{A0EEEF6F-9EF5-4401-91A2-9DAD24662AA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A0EEEF6F-9EF5-4401-91A2-9DAD24662AA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A0EEEF6F-9EF5-4401-91A2-9DAD24662AA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A0EEEF6F-9EF5-4401-91A2-9DAD24662AA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{076560D1-A87D-421D-BC9A-50B2CCDFA759}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{076560D1-A87D-421D-BC9A-50B2CCDFA759}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{076560D1-A87D-421D-BC9A-50B2CCDFA759}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{076560D1-A87D-421D-BC9A-50B2CCDFA759}.Release|Any CPU.Build.0 = Release|Any CPU
		{D92ECC74-630C-43B3-8231-9FA078743701}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D92ECC74-630C-43B3-8231-9FA078743701}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D92ECC74-630C-43B3-8231-9FA078743701}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D92ECC74-630C-43B3-8231-9FA078743701}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{48BE0B1E-C707-4B64-B148-3A5E90075132} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{E1AA426A-BBBC-4F25-BCDC-3CB6CBC475E3} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{04E20AD1-FF08-4F76-8E8D-78577CF0B507} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{531731D9-DD9F-4997-BF26-DC80FE169914} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{E03D79CF-BD61-496B-A672-CB47AB0D3AA9} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{91D2D961-F304-4F84-85BB-59164B2B2C0B} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{144B6106-312B-493E-A719-38BE556B8166} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{F234EF47-F96E-44DF-8160-0B760945618F} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{DBECD47F-31D3-4928-8D52-D760C7EC62BF} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{807F3D96-2DB5-416E-A321-74ACECFB919D} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{9C4D3EFB-606D-4A96-AD31-5D02AB7C401F} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{7606A83A-4AD0-4392-A511-B8A12EA3C300} = {53A6F94D-AD9C-4315-B0E9-FCD3AA65C683}
		{0E14CC12-7662-49A2-AD2B-3E1A51CB764D} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{B27E50A1-A5BB-4A5C-994E-4F403DB35AB5} = {0F46D6BB-7831-4147-AE08-E50EC06C4FD8}
		{8E8C5474-3735-4266-B602-1AB36FC959EC} = {0F46D6BB-7831-4147-AE08-E50EC06C4FD8}
		{FCC7EFBC-DF13-4E6E-9546-EDA36EDA1EA2} = {9C8F8393-462C-418B-87A8-45158B682964}
		{0F46D6BB-7831-4147-AE08-E50EC06C4FD8} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{53A6F94D-AD9C-4315-B0E9-FCD3AA65C683} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{9C8F8393-462C-418B-87A8-45158B682964} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{47F6C8B0-7569-4CA3-8055-EFBC792153E7} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{6DC5ADC5-6DE2-478F-B8A0-487A78D51672} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{A0EEEF6F-9EF5-4401-91A2-9DAD24662AA8} = {40978ECB-AE00-460F-85A1-0E01255FD961}
		{076560D1-A87D-421D-BC9A-50B2CCDFA759} = {42F034AB-1247-465B-86FD-A330761E08D3}
		{D92ECC74-630C-43B3-8231-9FA078743701} = {CC866CA7-00E6-46C3-9BA1-2C33A9A58E5E}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9255DE70-8312-4F59-8AA5-0EB0F11E0C3C}
	EndGlobalSection
EndGlobal
