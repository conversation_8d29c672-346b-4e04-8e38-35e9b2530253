﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#pragma warning disable
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Nncase.Evaluator;
using Nncase.IR.K230;
using Nncase.Passes;
using Nncase.Passes.Rules;
using Nncase.Passes.Rules.K230;
using Nncase.TIR.Instructions;
using Xunit;

namespace Nncase.Tests.K230.CodeGenTest;

internal class SpecInputDynamicGNNEMatMul : IcSourceTestFixture
{
    public string Name => "dynamic_gnne_matmul";

    public IEnumerable<CSourceTestCase> Cases => new[] { MakeMatmulCase() };

    public CSourceTestCase MakeMatmulCase()
    {
        var root = "/Users/<USER>/Downloads/Runtime/37K230DynamicGNNEMatMularg_";
        var inputs = Enumerable.Range(0, 8).Select(x => DataGenerator.FromTextFile(root + x).AsTensor()).ToArray();
        var ddrWeight = inputs[DynamicGNNEMatMul.InputA.Index];
        var ddrInput = inputs[DynamicGNNEMatMul.InputB.Index];
        var ddrAct = inputs[DynamicGNNEMatMul.Act.Index];
        var ddrActData = ddrAct.ToArray<Half>();
        var shiftBits = inputs[DynamicGNNEMatMul.ShiftBits.Index].ToScalar<sbyte>();

        var ddrWeightQarg = inputs[DynamicGNNEMatMul.InputABias.Index];
        var ddrInputQarg = inputs[DynamicGNNEMatMul.InputBBias.Index].ToScalar<byte>();
        var dynamicChannel = inputs[DynamicGNNEMatMul.DynamicChannel.Index].ToScalar<int>();

        var ddrShiftBits = Tensor.FromScalar(shiftBits);

        var aBatch0 = (int)ddrWeight.Shape[0].FixedValue;
        var aBatch1 = (int)ddrWeight.Shape[1].FixedValue;
        var bBatch0 = (int)ddrInput.Shape[0].FixedValue;
        var bBatch1 = (int)ddrInput.Shape[1].FixedValue;

        // M x K * K x N
        var m = (int)ddrWeight.Shape[2].FixedValue;
        var k = (int)ddrWeight.Shape[3].FixedValue;
        var n = (int)ddrInput.Shape[3].FixedValue;
        var ddrOutputHalf = new Tensor<Half>(Math.Max(aBatch0, bBatch0) * Math.Max(aBatch1, bBatch1) * m * n).Reshape(new long[] { Math.Max(aBatch0, bBatch0), Math.Max(aBatch1, bBatch1), m, n });
        var quantType = QUANT_TYPE.u8;
        if (quantType == QUANT_TYPE.u8)
        {
            K230Kernels.DynamicGnneMatmul<byte, byte, Half>(
                ddrWeight.Cast<byte>().Buffer.Span,
                ddrInput.Cast<byte>().Buffer.Span,
                ddrOutputHalf.Buffer.Span,
                ddrActData,
                ddrWeightQarg.BytesBuffer,
                aBatch0,
                aBatch1,
                m,
                k,
                bBatch0,
                bBatch1,
                n,
                ddrInputQarg,
                0,
                0,
                shiftBits,
                Convert.ToBoolean(dynamicChannel));
        }
        else if (quantType == QUANT_TYPE.i8)
        {
            K230Kernels.DynamicGnneMatmul<sbyte, sbyte, Half>(
                ddrWeight.Cast<sbyte>().Buffer.Span,
                ddrInput.Cast<sbyte>().Buffer.Span,
                ddrOutputHalf.Buffer.Span,
                ddrActData,
                ddrWeightQarg.BytesBuffer,
                aBatch0,
                aBatch1,
                m,
                k,
                bBatch0,
                bBatch1,
                n,
                ddrInputQarg,
                0,
                0,
                shiftBits,
                Convert.ToBoolean(dynamicChannel));
        }

        var ddr_output = ddrOutputHalf.Cast<float>(CastMode.KDefault);
        var inputs1 = new[]
        {
            ddrWeight, ddrInput, ddrWeightQarg, (int)ddrInputQarg, ddrAct, ddrShiftBits, dynamicChannel,
        };
        var expected = new Tensor[] { ddr_output };
        string dy = Convert.ToBoolean(dynamicChannel) ? "dy_ch" : "dy_bc";
        string name = $"spec_{dy}_{aBatch0}_{aBatch1}_{m}_{k}_{n}_{bBatch0}_{bBatch1}";
        if (quantType != QUANT_TYPE.u8)
        {
            name += $"_{quantType}";
        }

        return new(name, inputs1, expected);
    }
}

internal class DynamicGnneMatmul : IcSourceTestFixture
{
    /// <summary>
    /// channel 是否是动态的.
    /// </summary>
    public bool DynamicChannel;

    public DynamicGnneMatmul(bool dynamic_channel)
    {
        DynamicChannel = dynamic_channel;
    }

    public string Name => "dynamic_gnne_matmul";

    public static (int In_a_B0, int In_a_B1, int M, int K, int N, int In_b_B0, int In_b_B1)[] MatmulParams = {
        (1, 1, 7, 2048, 1024, 1, 1),

        // (1, 1, 7, 2048, 512, 1, 1),
        (1, 1, 7, 2048, 384, 1, 1),

        // (1, 1, 7, 1024, 2048, 1, 1),
        (1, 1, 7, 512, 2048, 1, 1),

        // (1, 1, 7, 512, 1024, 1, 1),
        // (1, 1, 7, 512, 512, 1, 1),
        // (1, 1, 7, 384, 2048, 1, 1),
        (1, 1, 7, 256, 512, 1, 1),

        // (1, 1, 7, 256, 256, 1, 1),
        (1, 1, 7, 192, 384, 1, 1),

        // (1, 1, 7, 192, 192, 1, 1),
        // (1, 1, 7, 192, 124, 1, 1),
        // (1, 1, 2, 2048, 1024, 1, 1),
        (1, 1, 2, 2048, 512, 1, 1),

        // (1, 1, 2, 2048, 256, 1, 1),
        (1, 1, 2, 1024, 5000, 1, 1),

        // (1, 1, 2, 512, 5000, 1, 1),
        (1, 1, 2, 256, 5000, 1, 1),

        // (1, 1, 2, 1024, 2048, 1, 1),
        (1, 1, 2, 512, 2048, 1, 1),

        // (1, 1, 2, 256, 2048, 1, 1),
        (1, 1, 2, 1024, 1024, 1, 1),

        // (1, 1, 2, 512, 512, 1, 1),
        (1, 1, 2, 256, 256, 1, 1),

        // (1, 1, 2, 128, 256, 1, 1),
        (1, 1, 2, 512, 1024, 1, 1),

        // (1, 1, 2, 256, 512, 1, 1),
        (1, 1, 2, 128, 256, 1, 1),

        // (1, 1, 2, 512, 512, 1, 1),
        (1, 1, 2, 256, 256, 1, 1),

        // (1, 1, 2, 128, 128, 1, 1),
        // custom test
        (1, 1, 24, 16, 64, 1, 1),
        (10, 1, 1, 3, 1, 1, 1),
        (1, 2, 3, 4, 2, 1, 1),
        (3, 1, 3, 4, 2, 1, 2),
        (3, 1, 12, 56, 2, 1, 2),
        (1, 2, 128, 256, 64, 1, 1),
    };

    public static (Tensor<float>, Tensor, ValueRange<float>) MakeInputTensor<T>(int batch0, int batch1, int dim1, int dim2)
      where T : unmanaged, IEquatable<T>
    {
        var orginal = new Tensor<float>(new long[] { batch0, batch1, dim1, dim2 });
        ValueRange<float> range = new(float.MaxValue, float.MinValue);
        for (int b0 = 0; b0 < batch0; b0++)
        {
            for (int b1 = 0; b1 < batch1; b1++)
            {
                for (int i = 0; i < dim1; i++)
                {
                    for (int j = 0; j < dim2; j++)
                    {
                        float value;
#if DEBUG
                        float channelOffset = dim2 < 64 ? b0 + b1 + 1 : (Testing.RandGenerator.NextSingle() - 0.5f) * 2; // b0 + b1 + 1;
                        value = (dim2 < 64 ? i : i / dim2) + channelOffset;
#else
                        var channel_offset = Testing.RandGenerator.Next(1, 10);
                        value = Testing.RandGenerator.Next(-10, 10) + channel_offset;
#endif
                        orginal[b0, b1, i, j] = value;
                        range.Min = MathF.Min(range.Min, value);
                        range.Max = MathF.Max(range.Max, value);
                    }
                }
            }
        }

        var quantType = DataType.FromType<T>();
        var iqP = NonConstQuantManager.GetInADeqQuantParam(quantType, range);
        var quantTensor = IR.F.Math.Quantize(orginal, Tensor.FromScalar<QuantParam>(new(iqP.ZeroPoint, iqP.Scale)), quantType).Evaluate().AsTensor().Cast<T>();
        return (orginal, quantTensor, range);
    }

    public CSourceTestCase MakeMatmulCase(int inAB0, int inAB1, int inBB0, int inBB1, int m, int k, int n, int i, QUANT_TYPE quant_type = QUANT_TYPE.u8)
    {
        var quan_dtype = quant_type == QUANT_TYPE.u8 ? DataTypes.UInt8 : DataTypes.Int8;

        Tensor ddrWeight, ddrInput;
        ValueRange<float> inARange, inBRange;
        if (quant_type == QUANT_TYPE.u8)
        {
            (_, ddrWeight, inARange) = MakeInputTensor<byte>(inAB0, inAB1, m, k);
            (_, ddrInput, inBRange) = MakeInputTensor<byte>(inBB0, inBB1, k, n);
        }
        else if (quant_type == QUANT_TYPE.i8)
        {
            (_, ddrWeight, inARange) = MakeInputTensor<sbyte>(inAB0, inAB1, m, k);
            (_, ddrInput, inBRange) = MakeInputTensor<sbyte>(inBB0, inBB1, k, n);
        }
        else
        {
            throw new ArgumentOutOfRangeException();
        }

        var inADeqParams = NonConstQuantManager.GetInADeqQuantParam(quan_dtype, inARange);
        var inBDeqParams = NonConstQuantManager.GetInBDeqQuantParam(quan_dtype, inBRange);
        var ddrWeightQarg = Tensor.From(Enumerable.Repeat(checked((byte)inADeqParams.ZeroPoint), m).ToArray(), new long[] { 1, 1, 1, m });

        byte ddrInputQarg = checked((byte)inBDeqParams.ZeroPoint);

        var ap = new ActivationParameter<float>(new[] { 1, 1, DynamicChannel ? 1 : m }, ValueRange<float>.Full);
        ap.FusedScale(inADeqParams.Scale);
        ap.FusedScale(inBDeqParams.Scale);
        var shiftBits = ap.ShiftBits;
        ap.FusedShiftBits(shiftBits);
        var ddrShiftBits = Tensor.FromScalar((int)shiftBits);

        var ddrAct = ap.ToAct0Data<Half>();

        var ddrOutputHalf = new Tensor<Half>(new long[] { Math.Max(inAB0, inBB0), Math.Max(inAB1, inBB1), m, n });
        if (quant_type == QUANT_TYPE.u8)
        {
            K230Kernels.DynamicGnneMatmul<byte, byte, Half>(
              ddrWeight.Cast<byte>().Buffer.Span,
              ddrInput.Cast<byte>().Buffer.Span,
              ddrOutputHalf.Buffer.Span,
              ddrAct.Buffer.Span,
              ddrWeightQarg.Buffer.Span,
              inAB0,
              inAB1,
              m,
              k,
              inBB0,
              inBB1,
              n,
              ddrInputQarg,
              0,
              0,
              shiftBits,
              DynamicChannel);
        }
        else if (quant_type == QUANT_TYPE.i8)
        {
            K230Kernels.DynamicGnneMatmul<sbyte, sbyte, Half>(
              ddrWeight.Cast<sbyte>().Buffer.Span,
              ddrInput.Cast<sbyte>().Buffer.Span,
              ddrOutputHalf.Buffer.Span,
              ddrAct.Buffer.Span,
              ddrWeightQarg.Buffer.Span,
              inAB0,
              inAB1,
              m,
              k,
              inBB0,
              inBB1,
              n,
              ddrInputQarg,
              0,
              0,
              shiftBits,
              DynamicChannel);
        }

        var ddrOutput = ddrOutputHalf.Cast<float>(CastMode.KDefault);
        var inputs = new[] { ddrWeight, ddrInput, ddrWeightQarg, (int)ddrInputQarg, ddrAct, ddrShiftBits, Convert.ToInt32(DynamicChannel) };
        var expected = new Tensor[] { ddrOutput };
        string dy = DynamicChannel ? "dy_ch" : "dy_bc";
        string name = $"{i}_{dy}_{inAB0}_{inAB1}_{m}_{k}_{n}_{inBB0}_{inBB1}";
        if (quant_type != QUANT_TYPE.u8)
        {
            name += $"_{quant_type}";
        }

        return new(name, inputs, expected);
    }

    public IEnumerable<CSourceTestCase> Cases => MatmulParams.Select((p, i) =>
      {
          (int in_a_B0, int in_a_B1, int m, int k, int n, int inBB0, int inBB1) = p;
          return new[] { MakeMatmulCase(in_a_B0, in_a_B1, inBB0, inBB1, m, k, n, i), MakeMatmulCase(in_a_B0, in_a_B1, inBB0, inBB1, m, k, n, i, QUANT_TYPE.i8) };
      }).SelectMany(i => i);
}

internal sealed record GnneMatmulFileParam(string Root, int Number);

internal sealed class DynamicGnneMatmulFromFile : IcSourceTestFixture
{
    private List<GnneMatmulFileParam> _file_params;

    public DynamicGnneMatmulFromFile(List<GnneMatmulFileParam> file_params)
    {
        _file_params = file_params;
    }

    public string Name => "dynamic_gnne_matmul";

    private static CSourceTestCase MakeMatmulCase(GnneMatmulFileParam param)
    {
        var (root, number) = param;

        var inputA = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_1")).AsTensor();
        var inputB = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_2")).AsTensor();
        var inputABias = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_3")).AsTensor().Cast<byte>();
        int inputBBias = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_4")).AsTensor().ToScalar<int>();
        var act = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_5")).AsTensor().Cast<Half>();
        int shiftBits = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_6")).AsTensor().ToScalar<int>();
        int dynamicChannel = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul$arg_7")).AsTensor().ToScalar<int>();
        var output = DataGenerator.FromTextFile(Path.Join(root, $"{number}$K230DynamicGNNEMatMul")).AsTensor().Cast<float>();
        var outputHalf = output.Cast<Half>(CastMode.KDefault);
        outputHalf.Fill((Half)0.0f);

        var (inAb0, inAb1, m, k, inBb0, inBb1, n) = ((int)inputA.Shape[0].FixedValue, (int)inputA.Shape[1].FixedValue,
                  (int)inputA.Shape[2].FixedValue, (int)inputA.Shape[3].FixedValue, (int)inputB.Shape[0].FixedValue, (int)inputB.Shape[1].FixedValue, (int)inputB.Shape[3].FixedValue);

        if (inputA.ElementType == DataTypes.UInt8 && inputB.ElementType == DataTypes.UInt8)
        {
            K230Kernels.DynamicGnneMatmul<byte, byte, Half>(
                  inputA.Cast<byte>().Buffer.Span,
                  inputB.Cast<byte>().Buffer.Span,
                  outputHalf.Buffer.Span,
                  act.Buffer.Span,
                  inputABias.Buffer.Span,
                  inAb0,
                  inAb1,
                  m,
                  k,
                  inBb0,
                  inBb1,
                  n,
                  (byte)inputBBias,
                  0,
                  0,
                  (sbyte)shiftBits,
                  dynamicChannel == 1);
        }
        else if (inputA.ElementType == DataTypes.Int8 && inputB.ElementType == DataTypes.Int8)
        {
            K230Kernels.DynamicGnneMatmul<sbyte, sbyte, Half>(
                  inputA.Cast<sbyte>().Buffer.Span,
                  inputB.Cast<sbyte>().Buffer.Span,
                  outputHalf.Buffer.Span,
                  act.Buffer.Span,
                  inputABias.Buffer.Span,
                  inAb0,
                  inAb1,
                  m,
                  k,
                  inBb0,
                  inBb1,
                  n,
                  (byte)inputBBias,
                  0,
                  0,
                  (sbyte)shiftBits,
                  dynamicChannel == 1);
        }
        else
        {
            throw new NotSupportedException();
        }

        var ddrOutput = outputHalf.Cast<float>(CastMode.KDefault);
        var inputs = new[] { inputA, inputB, inputABias, inputBBias, act, shiftBits, dynamicChannel };
        var expected = new Tensor[] { ddrOutput };
        string dy = dynamicChannel == 1 ? "dy_ch" : "dy_bc";
        string name = $"{dy}_{inAb0}_{inAb1}_{m}_{k}_{n}_{inBb0}_{inBb1}";
        return new(name, inputs, expected);
    }

    public IEnumerable<CSourceTestCase> Cases => _file_params.Select(root => MakeMatmulCase(root));
}
