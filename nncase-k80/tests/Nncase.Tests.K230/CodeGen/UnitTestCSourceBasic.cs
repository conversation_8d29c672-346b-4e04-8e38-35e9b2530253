﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using Nncase.CodeGen.K230;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Passes;
using Nncase.Tests.TestFixture;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using Xunit;

namespace Nncase.Tests.K230.CodeGenTest;

[AutoSetupTestMethod(InitSession = true)]
public class UnitTestCSourceBasic : TestClassBase
{
    private readonly Tensor<Half> _excepted = Tensor.From(new[] { (Half)1 }, new long[] { 1, 1, 1, 1 });

    /// <summary>
    /// Gets dataOne.
    /// </summary>
    public static IEnumerable<object[]> DataOne => Data.Take(1);

    public static IEnumerable<object[]> DataAll => Data.Skip(1);

    private static TheoryData<string> Data => new()
    {
        "asm_for_loop",
        "asm_local",
        "matmul_test",
        "mmu_conf_1",
        "fence",
        "ccr_clr_2",
        "linked_list_test_1",
        "ret_buffer",
        "more_params",
        "global",
        "ccr_clr_1",
        "math_test_1",
        "alloca_test_1",
        "alloca_test_3",
        "cond_2",
        "cond_1",
        "and_1",
        "or_2",
        "or_1",
        "do_while_1",
        "struct_3",
        "while_1",
        "for_1",
        "struct_2",
        "struct_1",
        "if_1",
        "if_2",
        "assert_1",
    };

    [Theory(Skip = "Never Used")]
    [MemberData(nameof(DataAll))]
    public void RunAll(string @case) => RunCore(@case);

    [Theory(Skip = "Never Used")]
    [MemberData(nameof(DataOne))]
    public void RunOne(string @case) => RunCore(@case);

    /// <summary>
    /// basic test要求正确的返回值为float16类型的1.
    /// </summary>
    /// <param name="case">.</param>
    private void RunCore(string @case)
    {
        using var dumpScope = new DumpScope($"{@case}");
        string csource = $"Functional/{@case}.c";
        var gmodel = new CSourceGModelBuilder(
            csource,
            new(
                new TupleType(ImmutableArray.Create<IRType>(
              new TensorType(DataTypes.Float16, new[] { 1, 1, 1, 1 }))),
                ImmutableArray.Create<IRType>()));
        gmodel.Dump("gmodel", DumpScope.Current.Directory);

        var outputs = gmodel.Invoke();
        Assert.Equal(new Tensor[] { _excepted }, outputs);
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            gmodel.Backend = "sc_model";
            outputs = gmodel.Invoke();
            try
            {
                Assert.Equal(new Tensor[] { _excepted }, outputs);
            }
            catch (Exception)
            {
                Directory.CreateDirectory(Path.Combine(DumpScope.Current.Directory, "ckp_fold"));
                using (var writer = new StreamWriter(File.OpenWrite(Path.Combine(DumpScope.Current.Directory, "debug_args.txt"))))
                {
                    writer.WriteLine($"var asm_out_path = \"{DumpScope.Current.Directory}/gmodel.o\";");
                    writer.WriteLine($"var spu_chk_path = \"{DumpScope.Current.Directory}/ckp_fold/spu_write.chk\";");
                    writer.WriteLine($"var pc_addr_ctrl = \"{DumpScope.Current.Directory}/gmodel.pc_addr_ctrl\";");
                    writer.WriteLine($"var new_asm_out_path = \"{DumpScope.Current.Directory}/gmodel_new.o\";");
                }

                throw;
            }
        }
    }
}
