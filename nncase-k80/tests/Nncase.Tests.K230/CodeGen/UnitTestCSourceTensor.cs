﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#pragma warning disable
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using Nncase.CodeGen.K230;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Passes;
using Nncase.Tests.TestFixture;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using Xunit;

namespace Nncase.Tests.K230.CodeGenTest;

internal sealed record CSourceTestCase(string ParamDesc, Tensor[] Inputs, Tensor[] Expected);

internal interface IcSourceTestFixture
{
    public string SourceFile => $"Functional/{Name}.c";

    public string Name { get; }

    public IEnumerable<CSourceTestCase> Cases { get; }
}

internal class LoadCase1 : IcSourceTestFixture
{
    public string Name => "Load_1";

    public IEnumerable<CSourceTestCase> Cases
    {
        get
        {
            return new CSourceTestCase[] {
              new(
                  "Default",
                  Inputs: new Tensor[] { Tensor.From(new[] { 1, 2, 3, 4 }, new long[] { 1, 1, 2, 2 }) },
                  Expected: new Tensor[] { Tensor.FromScalar((Half)1).Reshape(new long[] { 1, 1, 1, 1 }) }),
            };
        }
    }
}

internal class LoadCase2 : IcSourceTestFixture
{
    public string Name => "Load_2";

    public IEnumerable<CSourceTestCase> Cases
    {
        get
        {
            var ts = Tensor.From(Enumerable.Range(0, 24).Select(i => (Half)i).ToArray(), new long[] { 1, 2, 3, 4 });
            return new CSourceTestCase[] {
              new(
                  "Default",
                  Inputs: new Tensor[] { ts },
                  Expected: new Tensor[] { ts }),
            };
        }
    }
}

internal class LoadCase3 : IcSourceTestFixture
{
    public string Name => "Load_3";

    public IEnumerable<CSourceTestCase> Cases
    {
        get
        {
            var ts = Tensor.From(Enumerable.Range(0, 24).Select(i => (Half)i).ToArray(), new long[] { 1, 2, 3, 4 });
            return new CSourceTestCase[] {
              new(
                  "Default",
                  Inputs: new Tensor[] { ts },
                  Expected: new Tensor[] { Tensor.FromScalar((Half)1).Reshape(new long[] { 1, 1, 1, 1 }) }),
            };
        }
    }
}

internal class Matmul1 : IcSourceTestFixture
{
    public string Name => "matmul_1";

    public IEnumerable<CSourceTestCase> Cases
    {
        get
        {
            var ddrWeight = Testing.Rand<byte>(1, 1, 7, 512);
            var ddrInput = Testing.Rand<byte>(1, 1, 512, 2048);
            var ddrWeightQarg = Testing.Rand<byte>(1, 1, 1, 7);
            var ddrInputQarg = Testing.Rand<byte>(1, 1, 1, 1);
            var ddrAct = Testing.Rand<Half>(1, 1, 7, 7);
            var ddrOutput = Tensor.FromScalar((Half)1).Reshape(new long[] { 1, 1, 1, 1 });
            return new CSourceTestCase[] {
              new(
                  "Default",
                  Inputs: new Tensor[] { ddrWeight, ddrInput, ddrWeightQarg, ddrInputQarg, ddrAct },
                  Expected: new Tensor[] { ddrOutput }),
            };
        }
    }
}

/// <inheritdoc />
[AutoSetupTestMethod(InitSession = true)]
public class UnitTestCSourceTensor : TestClassBase
{
    /// <summary>
    /// Gets dataOne.
    /// </summary>
    public static IEnumerable<object[]> DataOne => Data.Take(1);

    public static IEnumerable<object[]> DataAll => Data.Skip(1);

    private static TheoryData<IcSourceTestFixture> Data => new()
    {
        // new SpecInputDynamicGNNEMatMul(),
        //      new DynamicGnneMatmulFromFile(new (){
        //        new("/home/<USER>/Code/nncase/tests/11/tests_output/test_enc_nan/infer/k230/ptq/Runtime/",148)
        //      }),
        new DynamicGnneMatmul(dynamic_channel: true), // todo fix this
        new DynamicGnneMatmul(dynamic_channel: false),

        // new Matmul3(),
        // new Matmul1(),
        new LoadCase1(),
        new LoadCase2(),
        new LoadCase3(),
    };

    [Theory(Skip = "Never Used")]
    [MemberData(nameof(DataOne))]
    private void RunOne(IcSourceTestFixture fixture) => RunCore(fixture);

    private void RunCore(IcSourceTestFixture fixture)
    {
#if DEBUG
        foreach (var @case in fixture.Cases)
        {
            RunCase(fixture.Name, fixture.SourceFile, @case);
        }
#else
        System.Threading.Tasks.ParallelOptions poptions = new();
        poptions.MaxDegreeOfParallelism = 4;
        System.Threading.Tasks.Parallel.ForEach(fixture.Cases, poptions,
          (@case, state) => RunCase(fixture.Name, fixture.SourceFile, @case));
#endif
    }

    private void RunCase(string name, string source_file, CSourceTestCase @case)
    {
        using var _ = new DumpScope($"{name}/{@case.ParamDesc}");

        var csource = source_file;
        var gmodel = new CSourceGModelBuilder(csource, CodeGenUtil.ToSignature(@case.Inputs, @case.Expected));
        gmodel.Dump("gmodel", DumpScope.Current.Directory);

        var outputs = gmodel.Invoke(@case.Inputs);
        try
        {
            var sim = CosSimilarity(@case.Expected[0], outputs[0]);
            Assert.True(sim > 0.99f, "sim :" + sim);
        }
        catch (Exception)
        {
            File.WriteAllText(Path.Combine(DumpScope.Current.Directory, "expected.txt"), @case.Expected[0].GetArrayString());
            File.WriteAllText(Path.Combine(DumpScope.Current.Directory, "actual.txt"), outputs[0].GetArrayString());
            throw;
        }

        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
        {
            gmodel.Backend = "sc_model";
            outputs = gmodel.Invoke(@case.Inputs);
            try
            {
#if DEBUG
                Assert.Equal(@case.Expected, outputs);
#else
                var sim = CosSimilarity(@case.Expected[0], outputs[0]);
                Assert.True(sim > 0.99f, "sim :" + sim.ToString());
#endif
            }
            catch (Exception)
            {
                File.WriteAllText(Path.Combine(DumpScope.Current.Directory, "expected.txt"), @case.Expected[0].GetArrayString());
                File.WriteAllText(Path.Combine(DumpScope.Current.Directory, "sc_actual.txt"), outputs[0].GetArrayString());
                throw;
            }

            // log the final end time
            Count_total_runtime(@case.ParamDesc, DumpScope.Current.Directory);
        }
    }

    private void Count_total_runtime(string desc, string dump_dir)
    {
        var endTimeFinder = new Regex(@"end_time:(\d+)");

        var perfResult = File.ReadAllText(Path.Combine(dump_dir, "perf_trace.log"));
        using var totalRuntimeWriter = new StreamWriter(File.OpenWrite(Path.Combine(dump_dir, "total_runtime.log")));
        ulong totalRuntime = 0;
        foreach (var match in endTimeFinder.Matches(perfResult).OfType<Match>())
        {
            var endTime = ulong.Parse(match.Groups[1].Value);
            if (endTime > totalRuntime)
            {
                totalRuntime = endTime;
            }
        }

        if (totalRuntime == 0)
        {
            return;
        }

        totalRuntimeWriter.WriteLine(totalRuntime / Math.Pow(10, 9));
        Console.WriteLine($"{desc} {totalRuntime / Math.Pow(10, 9)}");
    }

    [Theory(Skip = "Never Used")]
    [MemberData(nameof(DataAll))]
    private void RunAll(IcSourceTestFixture fixture) => RunCore(fixture);

    private double Prod(double[] data1, double[] data2)
    {
        return data1.Zip(data2).Aggregate(0.0d, (f, tuple) => f + (tuple.First * tuple.Second));
    }

    private double CosSimilarity(Tensor a, Tensor b)
    {
        double[] va = a.ToArray<double>();
        double[] vb = b.ToArray<double>();
        double v1 = Math.Sqrt(Prod(va, va));
        double v2 = Math.Sqrt(Prod(vb, vb));
        double sum = Prod(va, vb);
        return sum / (v1 * v2);
    }
}
