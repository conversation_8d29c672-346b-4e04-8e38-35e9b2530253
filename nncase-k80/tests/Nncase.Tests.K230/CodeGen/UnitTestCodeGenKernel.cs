﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.IO;
using System.Threading.Tasks;
using Nncase.Passes;
using Nncase.Passes.Mutators;
using Nncase.Tests.TestFixture;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using Xunit;

namespace Nncase.Tests.K230.CodeGenTest;

[AutoSetupTestMethod(InitSession = true)]
public class UnitTestCodeGenKernel : TestClassBase
{
    [Fact]
    public async Task TestSimpleGmodel()
    {
        /*
          这里的内存模型统一前面空一个.
          | olb fp         | high (top)
          | arg 0          |
          | arg 1          | low
         */
        int stackSize = 256 * 1024; // 开256kb的栈空间
        int paramNum = (4 + 1 + 1) * 2; // 两个tensor
        int sizeofBuffer = (4 + 1 + 1) * 4;
        var main = T.PrimFunc(
            "main",
            "k230",
            T.<PERSON>reateBuffer(new(DataTypes.Int16, new[] { 1, 1, 1, 1 }), TIR.MemoryLocation.Input, out var input),
            T.CreateBuffer(new(DataTypes.Int16, new[] { 1, 1, 1, 1 }), TIR.MemoryLocation.Output, out var output)).Body(
                I.LoadImm(R.rax, 0), // 配置mmu0为 256kb
                I.LoadImm(R.rbx, stackSize / 32),
                I.MMU_CONF(R.rax, R.rbx, 0),
                I.LoadImm(R.rbp, stackSize), // rbp 指向glb的顶部. // 最顶上的那个拿来存rbp. 下面的则是提前写入的参数.
                I.LoadImm(R.rsp, stackSize - (paramNum * 4)), // rsp 指向栈顶.
                I.Push(R.r0),  // 压入一个return address
                I.Push(R.rbp), // rbp 压栈 // 序言部分
                I.Mov(R.rbp, R.rsp), // rbp 指向 栈顶 也就是 stack_size - ( param * 4 + 8)
                I.LW(R.rdi, R.rbp, 8 + sizeofBuffer), // output shape[0] // 下面本来应该是把参数加载到寄存器里面,但是main函数里面没有.
                I.LW(R.r7, R.rbp, 8 + sizeofBuffer + 4), // output shape[1]
                I.LW(R.r8, R.rbp, 8 + sizeofBuffer + 8), // output shape[2]
                I.LW(R.r9, R.rbp, 8 + sizeofBuffer + 12), // output shape[3]
                I.LW(R.r10, R.rbp, 8 + sizeofBuffer + 16), // output ddr addr
                I.LW(R.rax, R.rbp, 8 + 0), // [0:4]   input shape[0] // output dt_type
                I.LW(R.rbx, R.rbp, 8 + 4), // [4:8]   input shape[1]
                I.LW(R.rcx, R.rbp, 8 + 8), // [8:12]  input shape[2]
                I.LW(R.rdx, R.rbp, 8 + 12), // [12:16] input shape[3]
                I.LW(R.rsi, R.rbp, 8 + 16), // [16:20] input ddr addr
                I.SS_PACK_SHAPE(R.rdi, R.r7, R.r8, R.r9, SHAPE_REGISTER.ss0), // ss0 为input shape. // [20:24] input dt_type
                I.SS_PACK_STRIDE(R.r7, R.r8, R.r9, SHAPE_REGISTER.ss1), // ss1 为input stride
                I.L2_STORE_CONF(SHAPE_REGISTER.ss1, SHAPE_REGISTER.ss1, L2_DATATYPE.i16, DDR_DATATYPE.i16),
                I.LoadImm(R.rax, 1234), // 在源地址上写入1234,然后push
                I.Push(R.rax),
                I.LW(R.rax, R.r0, 8), // 加载output basement.
                I.ADD(R.r10, R.r10, R.rax),
                I.L2_STORE(R.r10, R.rsp, SHAPE_REGISTER.ss0), // 把栈上的1234 store 到 ddr上.
                I.FENCE(), // 最后结束
                I.END(R.r0))
                .Build();

        bool ret = CompilerServices.InferenceType(main);
        Assert.True(ret);

        var pass = new PrimFuncPass { Name = "InstFolding" };
        pass.Add<FoldConstCall>();

        var func = await pass.RunAsync(main, new());

        var gmodel = new CodeGen.K230.GModelBuilder(func);
        gmodel.Dump("gmodel", Dumpper.Directory);
        var result = gmodel.Invoke(Tensor.FromArray(new short[] { 123 }));
        Assert.Equal(1234, result[0].ToArray<short>()[0]);
    }

    [Fact]
    public void TestBinaryWriteInt()
    {
        int x = -12;
        int bits = 12;
        {
            int mask = ~((1 << bits) - 1);
            int newValue = (1 << 31) | (x & mask);
        }

        // x & (1 - (1 << 11))
        using (var writer = new BinaryWriter(Dumpper.OpenFile("int.bin")))
        {
            byte[] res = new byte[4];
            var bw = new IO.BitWriter(res);
            bw.Write(x, 12);
            bw.Flush();
            writer.Write(res);
        }

        uint y = 12;
        using (var writer = new BinaryWriter(Dumpper.OpenFile("uint.bin")))
        {
            byte[] res = new byte[4];
            var bw = new IO.BitWriter(res);
            bw.Write(y, 12);
            bw.Flush();
            writer.Write(res);
        }
    }
}
