﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.K230;
using Nncase.Quantization;
using Nncase.Tests.TestFixture;
using Xunit;
using static Nncase.IR.F.Random;

namespace Nncase.Tests.K230.TransformTest;

/// <inheritdoc />
[AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestGNNE : TestClassBase
{
    public UnitTestGNNE()
    {
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;
        CompileOptions.DumpFlags = DumpFlags.ImportOps | DumpFlags.EGraphCost | DumpFlags.Compile | DumpFlags.PassIR |
                                   DumpFlags.Rewrite;
        DefaultTargetName = "k230";
    }

    public IAnalyzerManager AnalyzerManager => CompileSession.GetRequiredService<IAnalyzerManager>();

    [Fact]
    public void TestToGNNETranspose()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var transposeTest = new TransposeCase();
        var cases = transposeTest.GetCaseParams();
        foreach (var c in cases)
        {
            var transpose = transposeTest.GetTransposeCall((TransposeParams)c);
            TestMatched<ToGNNETranspose>(transpose);
        }
    }

    [Fact]
    public void TestToGNNEPad()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var padTest = new PadCase();
        var cases = padTest.GetCaseParams();
        foreach (var c in cases)
        {
            var pad = padTest.GetOriginalFunsion((PadParams)c);
            TestMatched<ToGNNEPad>(pad);
        }
    }

    [Fact]
    public void TestToAi2dPad()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var padTest = new Ai2DPadCase();
        var cases = padTest.GetCaseParams();
        foreach (var c in cases)
        {
            var pad = padTest.GetOriginalFunsion((Ai2DPadParams)c);
            TestNotMatch<ToAi2dPad>(pad[3]);
            TestMatched<ToAi2dPad>(pad[4]);
            TestMatched<ToAi2dPad>(pad[5]);
        }
    }

    [Fact]
    public void TestToAi2dResize()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var resizeTest = new Ai2DResizeCase();
        var cases = resizeTest.GetCaseParams();
        foreach (var c in cases)
        {
            var validR = resizeTest.GetOriginalFunsion((Ai2DResizeParams)c);
            TestMatched<ToAi2dResize>(validR[4]);
            TestMatched<ToAi2dResize>(validR[5]);
        }
    }

    [Fact]
    public void TestToActivation()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        CompileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<ToGNNEActivation>(act[0]);
        }
    }

    [Fact]
    public void TestToGNNELSTM()
    {
        SetupTestMethod();
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var lstmTest = new GNNELSTMCase();
        var cases = lstmTest.GetCaseParams();
        foreach (var c in cases)
        {
            var rootPre = lstmTest.GetLSTMCall((GNNELSTMParams)c);
            rootPre.InferenceType();
            CompilerServices.DumpIR(rootPre, "pre", Dumpper.Directory);
            TestMatched<ToGNNELSTM>(rootPre);
        }
    }

    [Fact]
    public void TestToGNNEPdpReduce()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var pdpTest = new Pdp1Case();
        var cases = pdpTest.GetCaseParams();
        foreach (var c in cases)
        {
            var pdp = pdpTest.GetPdp1Call((Pdp1Params)c)[1];
            TestMatched<ToGNNEPdpReduce>(pdp);
        }
    }

    [Fact]
    public void TestToGNNEConv2DTranspose()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var conv2dTransposeTest = new Conv2dTransposeCase();
        var cases = conv2dTransposeTest.GetCaseParams();
        foreach (var c in cases)
        {
            var conv = conv2dTransposeTest.GetOriginalFunsion((Conv2dTransposeParams)c)[0];
            TestMatched<ToFakeConv2DTranspose>(conv);
        }
    }

    [Fact]
    public void TestToGNNEConv2D()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var conv2dTest = new Conv2dCase();
        var cases = conv2dTest.GetCaseParams();
        foreach (var c in cases)
        {
            var conv = conv2dTest.GetConv2DCase((Conv2dParams)c)[1];
            TestMatched<ToGNNEConv2D>(conv);
        }
    }

    [Fact]
    public void TestToGNNEMatMul()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var matmulTest = new MatMulCase();
        var cases = matmulTest.GetCaseParams();
        foreach (var c in cases)
        {
            var matmul = matmulTest.GetMatMulCall((MatMulParams)c)[1];
            TestMatched<ToGNNEMatMul>(matmul);
        }
    }

    [Fact]
    public void TestExpand1x1Conv2D()
    {
        CompileOptions.QuantizeOptions = new QuantizeOptions();
        CompileOptions.QuantizeOptions.BindQuantMethod = false;
        var input = Uniform(DataTypes.UInt8, 1, -1, 0, new[] { 1, 40, 16, 16 });
        var weight = Uniform(DataTypes.UInt8, 255, 0, 0, new[] { 16, 40, 1, 1 }).Evaluate().AsTensor();
        var weight_bias =
            Tensor.From(
                Enumerable.Range(0, 16).Select(_ => new[] { (byte)0 }).SelectMany(i => i).ToArray(),
                new long[] { 1, 1, 1, 16 });
        var act = Tensor.From(
            Enumerable.Range(0, 16)
                .Select(_ => new[]
                {
                    (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity,
                    Half.PositiveInfinity, (Half)0,
                }).SelectMany(i => i).ToArray(),
            new long[] { 1, 1, 16, 7 });

        var conv = IR.K230.F.Tensors.GNNEConv2D(
            DataTypes.Float16,
            IR.K230.F.Tensors.GNNELoad(DataTypes.UInt8, input),
            IR.K230.F.Tensors.GNNELoadW(DataTypes.UInt8, weight),
            IR.K230.F.Tensors.GNNELoadW(DataTypes.UInt8, weight),
            IR.K230.F.Tensors.GNNELoadW(DataTypes.UInt8, weight_bias),
            IR.K230.F.Tensors.GNNELoadW(DataTypes.UInt8, weight_bias),
            IR.K230.F.Tensors.GNNELoadW(DataTypes.Float16, act),
            IR.K230.F.Tensors.GNNELoadW(DataTypes.Float16, act),
            10,
            10,
            10,
            Tensor.FromScalar(new DeQuantizeParam(0, 255)),
            new[,] { { 0, 0 }, { 0, 0 } },
            new[] { 1, 1 },
            new[] { 1, 1 },
            1,
            false,
            0,
            null!,
            null!);

        TestMatched<Expand1x1Conv>(conv);
    }

    public Expr TestMatchedCore(Expr pre, params IRewriteRule[] rules)
    {
        Assert.True(pre.InferenceType(), "TestInferFailed:" + pre.CheckedType);
        if (rules.Length == 0)
        {
            throw new InvalidOperationException("Rules should not be empty");
        }

        var analysis = new Dictionary<Type, IAnalysisResult>
        {
            [typeof(IExprUserAnalysisResult)] = AnalyzerManager.GetAnaylsis<IExprUserAnalysisResult>(new Function(pre)),
        };

        var preHashCode = pre.GetHashCode();
        var v1 = pre.Evaluate();
        var post = CompilerServices.Rewrite(pre, rules, new() { AnalysisResults = analysis });
        Assert.NotEqual(preHashCode, post.GetHashCode());
        var v2 = post.Evaluate();

        Comparator.Compare(v1, v2);
        return post;
    }

    internal void TestMatched<T>(Expr pre)
        where T : IRewriteRule, new()
    {
        TestMatchedCore(pre, new T());
    }

    internal void TestNotMatch(Expr pre, params IRewriteRule[] rules)
    {
        pre.InferenceType();
        var post = CompilerServices.Rewrite(pre, rules, new RunPassContext());
        Assert.Equal(pre, post);
    }

    internal void TestNotMatch<T>(Expr pre)
        where T : IRewriteRule, new()
    {
        TestNotMatch(pre, new T());
    }
}
