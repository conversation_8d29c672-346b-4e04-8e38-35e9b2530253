﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Mutators.K230;
using Nncase.Passes;
using Nncase.Passes.Rules.Tile;
using Nncase.Runtime.Interop;
using Nncase.Targets;
using Nncase.Tests.TestFixture;
using Xunit;

namespace Nncase.Tests.K230.TransformTest;

[AutoSetupTestMethod(InitSession = true)]
public class UnitTestTileFactory : TestClassBase
{
    public UnitTestTileFactory()
    {
#if DEBUG
        CompileOptions.DumpFlags = DumpFlags.PassIR | DumpFlags.Tiling;
#endif
    }

    public static TheoryData<IUnitTestTileCase> Data =>
        new()
        {
            // todo
            // passed
            new TileConv2dCase(),
            new TileAct1Case(),
            new TilePdp1Case(),
            new TileTransposeCase(),
            new TileConv2dTransposeCase(),
            new TilePadCase(),
            new TileMatMulCase(),
            new TileLSTMCase(),
            new TileLoadStoreCase(),
            new TileResizeCase(),
        };

    public static IEnumerable<object[]> DataOne => Data.Take(1);

    public static IEnumerable<object[]> DataAll => Data;

    public override string DefaultTargetName => K230Target.Kind;

    public static StreamWriter MakeFile(string name, string dumpDir)
    {
        return new StreamWriter(
            File.Open(
                Path.Join(dumpDir, name),
                FileMode.Create),
            Encoding.ASCII);
    }

    public static BinaryWriter MakeBinFile(string name, string dumpDir)
    {
        return new BinaryWriter(
            File.Open(
                Path.Join(dumpDir, name),
                FileMode.Create));
    }

    [Theory]
    [MemberData(nameof(DataOne))]
    public async Task RunOne(IUnitTestTileCase @case)
    {
        foreach (var (caseParams, i) in @case.GetCaseParams().Select((c, i) => (c, i)))
        {
            await RunCoreAsync(@case, caseParams, i);
        }
    }

    [Theory]
    [MemberData(nameof(DataAll))]
    public async Task RunAll(IUnitTestTileCase @case)
    {
        foreach (var (caseParams, i) in @case.GetCaseParams().Select((c, i) => (c, i)))
        {
            await RunCoreAsync(@case, caseParams, i);
        }
    }

    public Tensor RunKModel(byte[] kmodel, string dumpPath, Tensor[] inputTensors)
    {
        using (var interp = RTInterpreter.Create())
        {
            interp.SetDumpRoot(dumpPath);
            interp.LoadModel(kmodel);
            var entry = interp.Entry!;
            var rtInputs = inputTensors.Select(RTTensor.FromTensor).ToArray();
            entry.Invoke(rtInputs);
            return rtInputs[^1].ToTensor();
        }
    }

    private async Task RunCoreAsync(IUnitTestTileCase @case, ITileCaseParams caseParams, long i)
    {
        using var dumpScope = new DumpScope($"{@case.GetType().Name}_{i}");

        var fusion = @case.GetOriginalFunsion(caseParams);

        var ret = fusion.InferenceType();
        Assert.True(ret);

        var module = new IRModule(fusion);

        // step 0. setup the input & output tensor
        var inputTensors = @case.GetInputs(fusion, caseParams).ToArray();
        var outputTensors = fusion.Body.Evaluate(fusion.Parameters.ToArray().Zip(inputTensors).ToDictionary(
                t => t.First,
                t => (IValue)Value.FromTensor(t.Second),
                (IEqualityComparer<Var>)ReferenceEqualityComparer.Instance));
        Tensor outputTensor;
        outputTensor = outputTensors is TensorValue ? outputTensors.AsTensor() : outputTensors[0].AsTensor();

        var outputTensorAsInput = Tensor.FromBytes(outputTensor.ElementType, new Memory<byte>(outputTensor.BytesBuffer.ToArray()), outputTensor.Dimensions);
        outputTensorAsInput.BytesBuffer.Fill(0);

        // NOTE 如果把primfunc作为 entry, 那么输出tensor应该作为输入.
        var givenInputTensors = inputTensors.Concat(new[] { outputTensorAsInput }).ToArray();

        var prmg = CompileSession.CreatePassManager("Passes");

        // step 1. lower to the tir.
        prmg.AddWithName<K230FusionToTirPass>("FusionToTirPass");
        prmg.AddWithName<DDrBufferSchdeulePass>("DDrBufferSchdeule");
        prmg.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<FoldBufferSlot>();
            p.Add<FoldConstCall>();
        });
        await prmg.RunAsync(module);

        // step 2. build module
        var (kmodelPath, kmodel) = Testing.BuildKModel("main", module, CompileSession);
#if DEBUG
        Testing.DumpInterpModel(kmodelPath, givenInputTensors, DumpScope.Current.Directory);
#endif

        // step 3. interp model
        var resultTensor = RunKModel(kmodel, DumpScope.Current.Directory, givenInputTensors);

        float cos = Comparator.CosSimilarity(outputTensor, resultTensor);

        // var err_count = Testing.AllClose(output_tensor, result_tensor, Case.GetTolerance(fusion, caseParams));
        // if (cos < 0.99 || float.IsNaN(cos))
        {
            // input tensor
            await using (var writer = MakeFile("input.txt", DumpScope.Current.Directory))
            {
                await writer.WriteAsync(inputTensors[0].GetArrayString());
            }

            // output_tensor
            await using (var writer = MakeFile("expected.txt", DumpScope.Current.Directory))
            {
                await writer.WriteAsync(outputTensor.GetArrayString());
            }

            // result
            await using (var writer = MakeFile("actual.txt", DumpScope.Current.Directory))
            {
                await writer.WriteAsync(resultTensor.GetArrayString());
            }
        }

        Assert.True(cos >= 0.99);
    }
}
