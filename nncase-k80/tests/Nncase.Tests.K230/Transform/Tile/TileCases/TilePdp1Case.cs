﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.TIR.Instructions;
using static Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

public struct Pdp1CaseParams : ITileCaseParams
{
    public int[] Shape;
    public MFU_PDP_OP PdpOp;
    public int[] Stride;
    public int[] Filter;
    public int[,] Padding;
}

public sealed class TilePdp1Case : IUnitTestTileCase<Pdp1CaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var shapes = new object[] { new[] { 1, 3, 60, 72 }, };

        var pdpOps = new object[]
        {
            MFU_PDP_OP.MAX,

            // MFU_PDP_OP.AVERAGE,
        };

        var strides = new object[] { new[] { 1, 1 }, new[] { 2, 2 }, new[] { 2, 1 }, };

        var filters = new object[] { new[] { 3, 3 }, };

        var paddings = new object[] { new[,] { { 0, 0 }, { 0, 0 } }, new[,] { { 0, 1 }, { 0, 1 } }, new[,] { { 1, 1 }, { 2, 1 } }, };

        return new[] { shapes, pdpOps, strides, filters, paddings }.CartesianProduct().Select(item => item.ToArray()).Select(item => new Pdp1CaseParams
        {
            Shape = (int[])item[0],
            PdpOp = (MFU_PDP_OP)item[1],
            Stride = (int[])item[2],
            Filter = (int[])item[3],
            Padding = (int[,])item[4],
        }).OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(Pdp1CaseParams caseParams)
    {
        var curCase = caseParams;
        var (shape, pdpOp, stride, filter, padding) = (curCase.Shape, curCase.PdpOp, curCase.Stride, curCase.Filter, curCase.Padding);
        var input = new Var("input", new TensorType(DataTypes.Float16, shape));

        return new Fusion(
            $"TilePdp1Case",
            "k230",
            GNNEStore(
                DataTypes.Float16,
                GNNEPdp1(
                    DataTypes.Float16,
                    pdpOp,
                    GNNELoad(DataTypes.Float16, input),
                    filter,
                    stride,
                    padding,
                    Tensor.FromArray(new[] { new QuantizeParam(0, 1) }),
                    Tensor.FromArray(new[] { new DeQuantizeParam(0, 1) }),
                    0F,
                    0,
                    false),
                new long[] { 1, 1, 1, 1 }),
            input);
    }

    public IEnumerable<Tensor> GetInputs(Fusion fusion, Pdp1CaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (TensorType)p.TypeAnnotation
                select IR.F.Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor()).ToArray();
    }
}
