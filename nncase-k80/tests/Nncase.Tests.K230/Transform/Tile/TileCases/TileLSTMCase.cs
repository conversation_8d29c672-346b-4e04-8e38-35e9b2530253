﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.IR.K230;
using Nncase.IR.Tensors;
using Nncase.Passes.Rules.K230;
using Xunit;
using static Nncase.IR.K230.F.Tensors;
using Random = Nncase.IR.F.Random;
using Tuple = Nncase.IR.Tuple;

namespace Nncase.Tests.K230.TransformTest;

public struct LSTMCaseParams : ITileCaseParams
{
    public int[] InShape;
    public int[] WXc;
    public int[] ActXc;
    public int[] WRc;
    public int[] ActRc0;
    public int[] ActRc1;
    public int[] InitH;
    public int[] InitC;
    public int[] SegFittingParamFt;
    public int[] SegFittingParamGt;
    public int[] WXcWeightBias;
    public int[] WRcWeightBias;
    public int[] BinAct;
    public int[] BinActQ;
    public LSTMDirection Direction;
}

public sealed class TileLSTMCase : IUnitTestTileCase<LSTMCaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var inShapes = new object[] { new[] { 1, 20, 1, 96 }, new[] { 1, 40, 1, 96 }, };

        var wXcs = new object[] { new[] { 1, 2, 96, 96 }, };

        var actXcs = new object[] { new[] { 1, 2, 96, 7 }, };

        var wRcs = new object[] { new[] { 1, 2, 96, 24 }, };

        var actRc0S = new object[] { new[] { 1, 2, 96, 7 }, };

        var actRc1S = new object[] { new[] { 1, 2, 96, 7 }, };

        var initHs = new object[] { new[] { 1, 2, 1, 24 }, };

        var initCs = new object[] { new[] { 1, 2, 1, 24 }, };

        var segFittingParamFts = new object[] { new[] { 1, 1, 1, 49 }, };

        var segFittingParamGts = new object[] { new[] { 1, 1, 1, 49 }, };

        var wXcWeightBiases = new object[] { new[] { 1, 1, 1, 192 }, };

        var wRcWeightBiases = new object[] { new[] { 1, 1, 1, 192 }, };

        var binActs = new object[] { new[] { 1, 1, 24, 7 }, };

        var binActQs = new object[] { new[] { 1, 1, 24, 7 }, };

        var directions = new object[] { LSTMDirection.Bidirectional, LSTMDirection.Forward, LSTMDirection.Reverse, };

        return new[]
        {
            inShapes, wXcs, actXcs, wRcs, actRc0S, actRc1S, initHs, initCs, segFittingParamFts,
            segFittingParamGts, wXcWeightBiases, wRcWeightBiases, binActs, binActQs, directions,
        }.CartesianProduct().Select(item => item.ToArray()).Select(item => new LSTMCaseParams
        {
            InShape = (int[])item[0],
            WXc = (int[])item[1],
            ActXc = (int[])item[2],
            WRc = (int[])item[3],
            ActRc0 = (int[])item[4],
            ActRc1 = (int[])item[5],
            InitH = (int[])item[6],
            InitC = (int[])item[7],
            SegFittingParamFt = (int[])item[8],
            SegFittingParamGt = (int[])item[9],
            WXcWeightBias = (int[])item[10],
            WRcWeightBias = (int[])item[11],
            BinAct = (int[])item[12],
            BinActQ = (int[])item[13],
            Direction = (LSTMDirection)item[14],
        }).OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(LSTMCaseParams caseParams)
    {
        var curCase = caseParams;
        var (inShape, wXcShape, actXcShape, wRcShape, actRc0Shape, actRc1Shape, initHShape, initCShape,
                segFittingParamFtShape, segFittingParamGtShape, wXcWeightBiasShape, wRcWeightBiasShape,
                binActShape, binActQShape, direction) =
            (curCase.InShape, curCase.WXc, curCase.ActXc, curCase.WRc,
                curCase.ActRc0, curCase.ActRc1, curCase.InitH, curCase.InitC,
                curCase.SegFittingParamFt, curCase.SegFittingParamGt,
                curCase.WXcWeightBias, curCase.WRcWeightBias,
                curCase.BinAct, curCase.BinActQ, curCase.Direction);
        var input = new Var("input", new TensorType(DataTypes.UInt8, inShape));
        var wXc = Random.Uniform(DataTypes.UInt8, 255, 0, 0, wXcShape).Evaluate().AsTensor();
        var actXc = Tensor.From(
            Enumerable.Range(0, actXcShape[1] * actXcShape[2]).Select(_ => new[]
            {
                (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity,
                (Half)0,
            }).SelectMany(i => i).ToArray(),
            actXcShape.Select(x => (long)x).ToArray());
        var wRc = Random.Uniform(DataTypes.UInt8, 255, 0, 0, wRcShape).Evaluate().AsTensor();
        var actRc0 = Tensor.From(
            Enumerable.Range(0, actRc0Shape[1] * actRc0Shape[2]).Select(_ => new[]
            {
                (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity,
                (Half)0,
            }).SelectMany(i => i).ToArray(),
            actRc0Shape.Select(x => (long)x).ToArray());
        var actRc1 = Tensor.From(
            Enumerable.Range(0, actRc1Shape[1] * actRc1Shape[2]).Select(_ => new[]
            {
                (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity,
                (Half)0,
            }).SelectMany(i => i).ToArray(),
            actRc1Shape.Select(x => (long)x).ToArray());
        var initH = Random.Uniform(DataTypes.UInt8, 255, 0, 0, initHShape).Evaluate().AsTensor();
        var initC = Random.Uniform(DataTypes.Float16, 0, 0, 0, initCShape).Evaluate().AsTensor();
        var segFittingParamFt = Tensor.From(
            Enumerable.Range(0, segFittingParamFtShape[1] * segFittingParamFtShape[2]).Select(_ => new[]
            {
                (Half)(-8), (Half)(-6.85546875), (Half)(-5.71484375), (Half)(-4.5703125), (Half)(-3.427734375),
                (Half)(-2.28515625), (Half)(-1.142578125), (Half)0, (Half)1.142578125, (Half)2.28515625,
                (Half)3.427734375, (Half)4.5703125, (Half)5.71484375, (Half)6.85546875, (Half)8, (Half)0,
                (Half)0.0006260871887207031, (Half)0.001956939697265625, (Half)0.00608062744140625,
                (Half)0.018524169921875, (Half)0.05328369140625, (Half)0.1307373046875, (Half)0.2259521484375,
                (Half)0.2259521484375, (Half)0.1307373046875, (Half)0.05328369140625, (Half)0.018524169921875,
                (Half)0.00608062744140625, (Half)0.001956939697265625, (Half)0.0006260871887207031, (Half)0,
                (Half)0, (Half)0.005344390869140625, (Half)0.01447296142578125, (Half)0.03802490234375,
                (Half)0.094970703125, (Half)0.214111328125, (Half)0.391357421875, (Half)0.5, (Half)0.5,
                (Half)0.60888671875, (Half)0.7861328125, (Half)0.9052734375, (Half)0.9619140625, (Half)0.9853515625,
                (Half)0.99462890625, (Half)1, Half.NegativeInfinity, Half.PositiveInfinity,
            }).SelectMany(i => i).ToArray(),
            segFittingParamFtShape.Select(x => (long)x).ToArray());
        var segFittingParamGt = Tensor.From(
            Enumerable.Range(0, segFittingParamGtShape[1] * segFittingParamGtShape[2]).Select(_ => new[]
            {
                (Half)(-4), (Half)(-3.427734375), (Half)(-2.857421875), (Half)(-2.28515625), (Half)(-1.7138671875),
                (Half)(-1.142578125), (Half)(-0.5712890625), (Half)0, (Half)0.5712890625, (Half)1.142578125,
                (Half)1.7138671875, (Half)2.28515625, (Half)2.857421875, (Half)3.427734375, (Half)4, (Half)0,
                (Half)0.0025043487548828125, (Half)0.0078277587890625, (Half)0.024322509765625,
                (Half)0.0740966796875, (Half)0.213134765625, (Half)0.52294921875, (Half)0.90380859375,
                (Half)0.90380859375, (Half)0.52294921875, (Half)0.213134765625, (Half)0.0740966796875,
                (Half)0.024322509765625, (Half)0.0078277587890625, (Half)0.0025043487548828125, (Half)0, (Half)(-1),
                (Half)(-0.9892578125), (Half)(-0.97119140625), (Half)(-0.923828125), (Half)(-0.81005859375),
                (Half)(-0.57177734375), (Half)(-0.2174072265625), (Half)0, (Half)0, (Half)0.2174072265625,
                (Half)0.57177734375, (Half)0.81005859375, (Half)0.923828125, (Half)0.97119140625,
                (Half)0.9892578125, (Half)1, Half.NegativeInfinity, Half.PositiveInfinity,
            }).SelectMany(i => i).ToArray(),
            segFittingParamGtShape.Select(x => (long)x).ToArray());
        var wXcWeightBias =
            Tensor.From(
                Enumerable.Range(0, wXcWeightBiasShape[3]).Select(_ => new[] { (byte)0 }).SelectMany(i => i)
                    .ToArray(),
                wXcWeightBiasShape.Select(x => (long)x).ToArray());
        var wRcWeightBias =
            Tensor.From(
                Enumerable.Range(0, wRcWeightBiasShape[3]).Select(_ => new[] { (byte)0 }).SelectMany(i => i)
                    .ToArray(),
                wRcWeightBiasShape.Select(x => (long)x).ToArray());
        var binAct =
            Tensor.From(
                Enumerable.Range(0, binActShape[1] * binActShape[2]).Select(_ => new[]
                {
                    (Half)0, (Half)1, (Half)1, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity,
                }).SelectMany(i => i).ToArray(),
                binActShape.Select(x => (long)x).ToArray());
        var binActQ =
            Tensor.From(
                Enumerable.Range(0, binActQShape[1] * binActQShape[2]).Select(_ =>
                        new[]
                            {
                                (Half)0, (Half)8, (Half)8, (Half)0, (Half)0, Half.NegativeInfinity,
                                Half.PositiveInfinity,
                            })
                    .SelectMany(i => i).ToArray(),
                binActQShape.Select(x => (long)x).ToArray());

        Tuple WrapOutput(Call call)
        {
            var outputs = Enumerable.Range(0, 1).Select(i => Tensors.GetItem(call, i)).ToArray();
            var exprs = outputs.Select(f =>
                (Expr)GNNEStore(
                    f[GetItem.Index] != 2 ? DataTypes.UInt8 : DataTypes.Float16,
                    f)).ToArray();
            return new Tuple(exprs);
        }

        var lstm = GNNELSTM(
            DataTypes.UInt8,
            DataTypes.UInt8,
            GNNELoad(DataTypes.UInt8, input),
            GNNELoadW(DataTypes.UInt8, wXc),
            GNNELoadW(DataTypes.Float16, actXc),
            GNNELoadW(DataTypes.UInt8, wRc),
            GNNELoadW(DataTypes.Float16, actRc0),
            GNNELoadW(DataTypes.Float16, actRc1),
            GNNELoad(DataTypes.UInt8, initH),
            GNNELoad(DataTypes.Float16, initC),
            GNNELoadW(DataTypes.Float16, segFittingParamFt),
            GNNELoadW(DataTypes.Float16, segFittingParamGt),
            GNNELoadW(DataTypes.UInt8, wXcWeightBias),
            GNNELoadW(DataTypes.UInt8, wRcWeightBias),
            GNNELoadW(DataTypes.Float16, binAct),
            GNNELoadW(DataTypes.Float16, binActQ),
            ActParam2.GetDefaultConvActParam(1, 0), // activation_param_xc, no use for tiling
            ActParam2.GetDefaultConvActParam(1, 0), // activation_param_rc_0,
            ActParam2.GetDefaultConvActParam(1, 0), // activation_param_rc_1,
            22, // if_deq_bias,
            12, // xc_shift_bits,
            40, // h_deq_bias_0,
            35, // h_deq_bias_1,
            1, // c_shift_bits,
            12, // rc_shift_bits_0,
            12, // rc_shift_bits_1,
            1, // out_h_shift_bits,
            1, // out_c_shift_bits,
            ActParam2.GetDefaultConvActParam(1, 0), // activation_param_bin,
            ActParam2.GetDefaultConvActParam(1, 0), // activation_param_bin_q,
            direction, // GNNETypePatternUtility.lstm_direction.kReverse,
            false,
            1);

        var oldBody = WrapOutput(lstm);
        Assert.True(oldBody.InferenceType());
        var f = new Function("main", oldBody, input);
        var post = (Call)((Function)CompilerServices.Rewrite(f, new[] { new GNNELSTMFusion() }, new())).Body;
        return (Fusion)post.Target;
    }

    // public IEnumerable<Tensor> GetInputs(IR.Fusion fusion, LSTMCaseParams caseParams)
    // {
    //     return (from p in fusion.Parameters
    //             let ttype = (IR.TensorType)p.TypeAnnotation
    //             select IR.F.Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor());
    // }
    public IEnumerable<Tensor> GetInputs(Fusion fusion, LSTMCaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (TensorType)p.TypeAnnotation
                select Random.Uniform(DataTypes.UInt8, 255, 0, 0, ttype.Shape.ToValueArray()).Evaluate()
                    .AsTensor())
            .ToArray();
    }
}
