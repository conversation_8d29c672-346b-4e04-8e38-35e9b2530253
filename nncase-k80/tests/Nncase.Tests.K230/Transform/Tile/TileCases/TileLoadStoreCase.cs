﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using static Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

public struct LoadStoreCaseParams : ITileCaseParams
{
    public int[] Shape;
}

public sealed class TileLoadStoreCase : IUnitTestTileCase<LoadStoreCaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var shapes = new object[] {
              new[] { 1, 3, 64, 3 },
              new[] { 1, 32, 224, 224 },
              new[] { 4, 32, 224, 224 },
            };

        return new[] { shapes }.
          CartesianProduct().
          Select(item => item.ToArray()).
          Select(item => new LoadStoreCaseParams()
          {
              Shape = (int[])item[0],
          }).OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(LoadStoreCaseParams caseParams)
    {
        var curCase = (LoadStoreCaseParams)caseParams;
        var shape = curCase.Shape;

        var input = new Var("input", new TensorType(DataTypes.Float16, shape));

        var ret = new Fusion(
            $"TileLoadStoreCase",
            "k230",
            GNNEStore(
              DataTypes.Float16,
              GNNELoad(
                DataTypes.Float16,
                input),
              new long[] { 1, 1, 1, 1 }),
            input);
        return ret;
    }

    public IEnumerable<Tensor> GetInputs(IR.Fusion fusion, LoadStoreCaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (IR.TensorType)p.TypeAnnotation
                select IR.F.Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor()).ToArray();
    }
}
