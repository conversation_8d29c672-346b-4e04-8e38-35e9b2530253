// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using static Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

public struct ResizeCaseParams : ITileCaseParams
{
    public int[] InputShape;
    public int[] NewSize;
    public bool AlignCorner;
    public Nncase.TIR.Instructions.MFU_CROP_RESIZE ResizeMethod;
    public bool HalfPixelCenter;
}

public sealed class TileResizeCase : IUnitTestTileCase<ResizeCaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var inputShapes = new object[] {
            new[] { 2, 3, 32, 32 },
            };

        var newSizes = new object[] {
            new[] { 16, 16 },
            new[] { 64, 64 },
            new[] { 11, 11 },
            new[] { 37, 37 },
            new[] { 37, 41 },
            };

        var alignCorners = new object[] {
            true,
            false,
            };

        var resizeMethods = new object[]
        {
            Nncase.TIR.Instructions.MFU_CROP_RESIZE.BILINER,
            Nncase.TIR.Instructions.MFU_CROP_RESIZE.NEAREST,
        };

        var halfPixelCenters = new object[]
        {
            true,
            false,
        };

        // As per the definition of ONNX, half_pixel and align_corners are two modes of resize, both used to describe the coordinate_transformation_mode. They are mutually exclusive and cannot be applied simultaneously.
        var ret = new[] { inputShapes, newSizes, alignCorners, resizeMethods, halfPixelCenters }.CartesianProduct().Select(item => item.ToArray())
            .Select(item => new ResizeCaseParams() { InputShape = (int[])item[0], NewSize = (int[])item[1], AlignCorner = (bool)item[2], ResizeMethod = (Nncase.TIR.Instructions.MFU_CROP_RESIZE)item[3], HalfPixelCenter = (bool)item[4] })
            .OfType<ITileCaseParams>();

        ret = ret.Where((testCase, index) => !(((ResizeCaseParams)testCase).AlignCorner && ((ResizeCaseParams)testCase).HalfPixelCenter)).ToArray();

        return ret;
    }

    public Fusion GetOriginalFunsion(ResizeCaseParams caseParams)
    {
        var curCase = caseParams;
        var (inputShape, newSize, alignCorner, resizeMethod, halfPixelCenter) = (curCase.InputShape, curCase.NewSize, curCase.AlignCorner, curCase.ResizeMethod, curCase.HalfPixelCenter);

        var input = new Var("input", new TensorType(DataTypes.UInt8, inputShape));

        return new Fusion(
            $"TileResizeCase",
            "k230",
            GNNEStore(
                DataTypes.UInt8,
                IR.K230.F.Tensors.Ai2dResize(
                    (PrimType)DataTypes.UInt8,
                    resizeMethod,
                    alignCorner,
                    halfPixelCenter,
                    GNNELoad(DataTypes.UInt8, input),
                    newSize,
                    1,
                    new[] { new QuantParam(0, 1) }),
                new long[] { 1, 1, 1, 1 }),
            input);
    }

    public IEnumerable<Tensor> GetInputs(Fusion fusion, ResizeCaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (TensorType)p.TypeAnnotation
                select IR.F.Random.Uniform(DataTypes.UInt8, 255, 100, 0, ttype.Shape.ToValueArray()).Evaluate()
                    .AsTensor())
            .ToArray();
    }
}
