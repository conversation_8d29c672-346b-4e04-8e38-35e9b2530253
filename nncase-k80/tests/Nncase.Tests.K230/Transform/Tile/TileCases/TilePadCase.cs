﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using static Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

public struct PadCaseParams : ITileCaseParams
{
    public int[] Shape;
    public int[] Paddings;
}

public sealed class TilePadCase : IUnitTestTileCase<PadCaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var shapes = new object[] { new[] { 1, 3, 64, 3 }, new[] { 8, 3, 64, 1 }, new[] { 8, 3, 1, 3 }, };

        var pads = new object[]
        {
            new[] { 0, 0, 0, 0, 1, 0, 1, 0 }, new[] { 0, 0, 0, 0, 1, 3, 1, 2 }, new[] { 0, 0, 2, 0, 1, 1, 1, 0 },
            new[] { 9, 1, 2, 0, 1, 1, 1, 0 },
        };

        return new[] { shapes, pads }.CartesianProduct().Select(item => item.ToArray())
            .Select(item => new PadCaseParams() { Shape = (int[])item[0], Paddings = (int[])item[1] })
            .OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(PadCaseParams caseParams)
    {
        var curCase = caseParams;
        var (shape, paddings) = (curCase.Shape, curCase.Paddings);

        var input = new Var("input", new TensorType(DataTypes.Float16, shape));

        return new Fusion(
            $"TilePadCase",
            "k230",
            GNNEStore(
                DataTypes.Float16,
                GNNEPad(
                    GNNELoad(
                        DataTypes.Float16,
                        input),
                    Tensor.From(
                        paddings,
                        new long[] { 4, 2 }),
                    (Half)3),
                new long[] { 1, 1, 1, 1 }),
            input);
    }

    public IEnumerable<Tensor> GetInputs(Fusion fusion, PadCaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (TensorType)p.TypeAnnotation
                select IR.F.Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor())
            .ToArray();
    }
}
