﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.K230;
using static Nncase.IR.K230.F.Tensors;
using Random = Nncase.IR.F.Random;

namespace Nncase.Tests.K230.TransformTest;

public struct Act1CaseParams : ITileCaseParams
{
    public int[] Shape;
    public GnneActivationType ActType;
    public bool Is16Segments;
}

public sealed class TileAct1Case : IUnitTestTileCase<Act1CaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var shapes = new object[]
        {
            new[] { 1, 3, 60, 72 },
        };

        var actTypes = new object[]
        {
            GnneActivationType.Uninitialized,

            // gnne_activation_type.add,
            // gnne_activation_type.mul
        };

        var is16Segments = new object[]
        {
            false,

            // true
        };

        return new[] { shapes, actTypes, is16Segments }.CartesianProduct().Select(item => item.ToArray()).Select(item => new Act1CaseParams
        {
            Shape = (int[])item[0],
            ActType = (GnneActivationType)item[1],
            Is16Segments = (bool)item[2],
        }).OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(Act1CaseParams caseParams)
    {
        var curCase = caseParams;
        var (shape, actType, is16Segments) = (curCase.Shape, curCase.ActType, curCase.Is16Segments);
        var input = new Var("input", new TensorType(DataTypes.Float16, shape));
        var act = Tensor.From(
            Enumerable.Range(0, shape[1]).Select(i => new[]
                    { (Half)0, (Half)0.5, (Half)0.8, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity })
                .SelectMany(i => i).ToArray(),
            new long[] { 1, 1, shape[1], 7 });

        return new Fusion(
            "TileAct1Case",
            "k230",
            GNNEStore(
                DataTypes.Float16,
                GNNEActivation(
                    GNNELoad(DataTypes.Float16, input),
                    None.Default,
                    GNNELoadW(DataTypes.Float16, act),
                    0,
                    0,
                    0,
                    None.Default,
                    None.Default,
                    shape[1],
                    actType,
                    is16Segments,
                    DataTypes.Float16,
                    new ActParam2(shape[1]),
                    shape),
                new long[] { 1, 1, 1, 1 }),
            input);
    }

    public IEnumerable<Tensor> GetInputs(Fusion fusion, Act1CaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (TensorType)p.TypeAnnotation
                select Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor()).ToArray();
    }
}
