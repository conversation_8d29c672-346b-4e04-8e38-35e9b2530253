﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using NetFabric.Hyperlinq;
using Nncase.IR;
using static Nncase.IR.K230.F.Tensors;
using Random = Nncase.IR.F.Random;

namespace Nncase.Tests.K230.TransformTest;

public struct Conv2dTransposeCaseParams : ITileCaseParams
{
    public int[] InShape;
    public int OutputChannel;
    public int[] KernelSize;
    public int[,] Padding;
    public int[] Stride;
    public int[] Dilation;
    public int Groups;
    public int[] OutPadding;

    public int[] OutShape => new[] { InShape[0], OutputChannel, (Stride[0] * (InShape[2] - 1)) + OutPadding[0] + ((KernelSize[0] - 1) * Dilation[0]) + 1 - Padding[0, 0] - Padding[0, 1],
                                                                   (Stride[1] * (InShape[3] - 1)) + OutPadding[1] + ((KernelSize[1] - 1) * Dilation[1]) + 1 - Padding[1, 0] - Padding[1, 1], };
}

public sealed class TileConv2dTransposeCase : IUnitTestTileCase<Conv2dTransposeCaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var inShapes = new object[]
        {
            new[] { 1, 3, 10, 10 },
        };

        var outputChannels = new object[]
        {
            3,
            32,
        };

        var strides = new object[]
        {
            new[] { 1, 1 },
            new[] { 2, 2 },
        };

        var kernelSizes = new object[]
        {
            new[] { 1, 1 },
            new[] { 3, 3 },
        };

        var paddings = new object[]
        {
            new[,] { { 0, 0 }, { 0, 0 } },
            new[,] { { 0, 1 }, { 0, 1 } },
            new[,] { { 1, 1 }, { 1, 1 } },
        };

        var dilations = new object[]
        {
            new[] { 1, 1 },

            // new[] { 2, 2 },
        };

        var groups = new object[]
        {
            1,

            // 3,
        };

        var outPaddings = new object[]
        {
            new[] { 0, 0 },

            // new[] { 1, 0 },
            // new[] { 0, 1 },
        };

        return new[] { inShapes, outputChannels, strides, kernelSizes, paddings, dilations, groups, outPaddings }.CartesianProduct().Select(item => item.ToArray()).Select(item => new Conv2dTransposeCaseParams
        {
            InShape = (int[])item[0],
            OutputChannel = (int)item[1],
            Stride = (int[])item[2],
            KernelSize = (int[])item[3],
            Padding = (int[,])item[4],
            Dilation = (int[])item[5],
            Groups = (int)item[6],
            OutPadding = (int[])item[7],
        }).OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(Conv2dTransposeCaseParams caseParams)
    {
        var curCase = caseParams;
        var (shape, outputChannel, stride, kernelSize, padding, dilation, groups, outPadding, outShape) = (curCase.InShape, curCase.OutputChannel,
            curCase.Stride, curCase.KernelSize, curCase.Padding, curCase.Dilation, curCase.Groups, curCase.OutPadding, curCase.OutShape);
        var input = new Var("input", new TensorType(DataTypes.UInt8, shape));
        var weight = Random.Uniform(DataTypes.UInt8, 255, 0, 0, new[] { outputChannel / groups, shape[1], kernelSize[0], kernelSize[1] }).Evaluate().AsTensor();

        // var weights_data = new byte [] {85,170,255,85,170,255,85,170,255};
        // var weight = Tensor<byte>.From(weights_data, new[] { 3, 3, 1, 1 });
        // var bias_data = new byte [] {0,0,0};
        // var weight_bias = Tensor<byte>.From(bias_data, new[] { 1, 1, 1, output_channel });
        var weightBias = Tensor.From(Enumerable.Range(0, outputChannel).Select(i => new[] { (byte)0 }).SelectMany(i => i).ToArray(), new long[] { 1, 1, 1, outputChannel });
        var act = Tensor.From(Enumerable.Range(0, outputChannel).Select(i => new[] { (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity, (Half)0 }).SelectMany(i => i).ToArray(), new long[] { 1, 1, outputChannel, 7 });

        return new Fusion(
            "TileConv2dTransposeCase",
            "k230",
            GNNEStore(
                DataTypes.Float16,
                GNNEConv2DTranspose(
                    DataTypes.Float16,
                    GNNELoad(DataTypes.UInt8, input),
                    GNNELoadW(DataTypes.UInt8, weight),
                    GNNELoadW(DataTypes.UInt8, weight),
                    GNNELoadW(DataTypes.UInt8, weightBias),
                    GNNELoadW(DataTypes.UInt8, weightBias),
                    GNNELoadW(DataTypes.Float16, act),
                    GNNELoadW(DataTypes.Float16, act),
                    0,
                    10,
                    10,
                    Tensor.FromScalar(new DeQuantizeParam(0, 255)),
                    padding,
                    stride,
                    dilation,
                    groups,
                    false,
                    0,
                    null!,
                    null!,
                    outPadding,
                    outShape),
                new long[] { 1, 1, 1, 1 }),
            input);
    }

    public IEnumerable<Tensor> GetInputs(Fusion fusion, Conv2dTransposeCaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (TensorType)p.TypeAnnotation
                select Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor()).ToArray();
    }

    // public IEnumerable<Tensor> GetInputs(IR.Fusion fusion, Conv2dTransposeCaseParams caseParams)
    // {
    //     return (from p in fusion.Parameters
    //             let ttype = (IR.TensorType)p.TypeAnnotation
    //             select IR.F.Random.Uniform(DataTypes.UInt8, 255, 255, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor());
    // }
}
