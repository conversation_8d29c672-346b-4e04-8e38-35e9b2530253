﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Collections.Immutable;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using NetFabric.Hyperlinq;
using Nncase.Evaluator;
using Nncase.IR;
using Nncase.IR.Math;
using Nncase.Passes;
using Nncase.Passes.Rules.K230;
using Nncase.PatternMatch;
using Nncase.TIR.Instructions;
using Xunit;
using static Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

/// <inheritdoc />
public struct Conv2dCaseParams : ITileCaseParams
{
    public int[] InShape;
    public int OutputChannel;
    public int[] KernelSize;
    public int[,] Padding;
    public int[] Stride;
    public int[] Dilation;
    public int Groups;
}

public sealed class TileConv2dCase : IUnitTestTileCase<Conv2dCaseParams>
{
    /// <inheritdoc />
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var shapes = new object[]
        {
            new[] { 1, 4, 32, 32 },
        };

        var outputChannels = new object[]
        {
            16,
            32,
        };

        var strides = new object[]
        {
            new[] { 1, 1 },
            new[] { 2, 2 },
        };

        var kernelSizes = new object[]
        {
            new[] { 1, 1 },
            new[] { 3, 3 },
        };

        var paddings = new object[]
        {
            new[,] { { 0, 0 }, { 0, 0 } },
            new[,] { { 0, 1 }, { 0, 1 } },
            new[,] { { 1, 1 }, { 1, 1 } },
        };

        var dilations = new object[]
        {
            new[] { 1, 1 },
            new[] { 2, 2 },
        };

        var groups = new object[]
        {
            1,
            2,
        };

        return new[] { shapes, outputChannels, strides, kernelSizes, paddings, dilations, groups }.CartesianProduct().Select(item => item.ToArray()).Select(item => new Conv2dCaseParams
        {
            InShape = (int[])item[0],
            OutputChannel = (int)item[1],
            Stride = (int[])item[2],
            KernelSize = (int[])item[3],
            Padding = (int[,])item[4],
            Dilation = (int[])item[5],
            Groups = (int)item[6],
        }).OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(Conv2dCaseParams caseParams)
    {
        var curCase = caseParams;
        var (shape, outputChannel, stride, kernelSize, padding, dilation, groups) = (curCase.InShape, curCase.OutputChannel,
            curCase.Stride, curCase.KernelSize, curCase.Padding, curCase.Dilation, curCase.Groups);
        var input = new Var("input", new TensorType(DataTypes.UInt8, shape));
        var weight = IR.F.Random.Uniform(DataTypes.UInt8, 255, 0, 0, new[] { outputChannel, shape[1] / groups, kernelSize[0], kernelSize[1] }).Evaluate().AsTensor();
        var weightBias = Tensor.From(Enumerable.Range(0, outputChannel).Select(i => new[] { (byte)0 }).SelectMany(i => i).ToArray(), new long[] { 1, 1, 1, outputChannel });
        var act = Tensor.From(Enumerable.Range(0, outputChannel).Select(i => new[] { (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity, (Half)0 }).SelectMany(i => i).ToArray(), new long[] { 1, 1, outputChannel, 7 });

        return new Fusion(
            $"TileConv2dCase",
            "k230",
            GNNEStore(
                DataTypes.Float16,
                GNNEConv2D(
                    (PrimType)DataTypes.Float16,
                    GNNELoad(DataTypes.UInt8, input),
                    GNNELoadW(DataTypes.UInt8, weight),
                    GNNELoadW(DataTypes.UInt8, weight),
                    GNNELoadW(DataTypes.UInt8, weightBias),
                    GNNELoadW(DataTypes.UInt8, weightBias),
                    GNNELoadW(DataTypes.Float16, act),
                    GNNELoadW(DataTypes.Float16, act),
                    10,
                    10,
                    10,
                    Tensor.FromScalar(new DeQuantizeParam(0, 255)),
                    padding,
                    stride,
                    dilation,
                    groups,
                    false,
                    0,
                    null!,
                    null!),
                new long[] { 1, 1, 1, 1 }),
            input);
    }

    public IEnumerable<Tensor> GetInputs(IR.Fusion fusion, Conv2dCaseParams caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (IR.TensorType)p.TypeAnnotation
                select IR.F.Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor()).ToArray();
    }

    internal static int[] GetConv2DOutShape(int[] inputShape, int outChannel, int[] kernelSize, int[] stride, int[,] padding, int[] dilation)
    {
        var outShape = new int[inputShape.Length];
        inputShape.CopyTo(outShape, 0);
        outShape[1] = outChannel;

        outShape[2] = (int)TypeInference.GetWindowedOutputSize(inputShape[2] + padding[0, 0] + padding[0, 1], kernelSize[0], stride[0], dilation[0], false).FixedValue;
        outShape[3] = (int)TypeInference.GetWindowedOutputSize(inputShape[3] + padding[1, 0] + padding[1, 1], kernelSize[1], stride[1], dilation[1], false).FixedValue;

        return outShape;
    }
}
