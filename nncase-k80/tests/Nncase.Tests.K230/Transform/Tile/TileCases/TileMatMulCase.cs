﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using Nncase.IR;
using Nncase.IR.K230;
using static Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

public struct MatMulCaseParams : ITileCaseParams
{
    public int[] InAShape;
    public int[] InBShape;
}

public sealed class TileMatMulCase : IUnitTestTileCase<MatMulCaseParams>
{
    public IEnumerable<ITileCaseParams> GetCaseParams()
    {
        var inAShapes = new object[] { new[] { 1, 1, 8, 32 }, new[] { 1, 1, 24, 32 }, new[] { 1, 1, 100, 32 }, };

        var inBShapes = new object[] { new[] { 1, 1, 32, 3 }, new[] { 1, 1, 32, 15 }, new[] { 1, 1, 32, 5 }, };

        return new[] { inAShapes, inBShapes }.CartesianProduct().Select(item => item.ToArray())
            .Select(item => new MatMulCaseParams { InAShape = (int[])item[0], InBShape = (int[])item[1], })
            .OfType<ITileCaseParams>();
    }

    public Fusion GetOriginalFunsion(MatMulCaseParams caseParams)
    {
        var curCase = caseParams;
        var (inAShape, inBShape) = (curCase.InAShape, curCase.InBShape);
        var inputA = new Var("input_a", new TensorType(DataTypes.UInt8, inAShape));
        var inputB = new Var("input_b", new TensorType(DataTypes.UInt8, inBShape));

        // var input_b = IR.F.Random.Uniform(DataTypes.UInt8, 255, 255, 0, in_b_shape).Evaluate().AsTensor();
        var inputABias =
            Tensor.From(
                Enumerable.Range(0, inAShape[1] * inAShape[2]).Select(_ => new[] { (byte)0 }).SelectMany(i => i)
                    .ToArray(),
                new long[] { 1, 1, inAShape[1], inAShape[2] });
        var inputBBias =
            Tensor.From(
                Enumerable.Range(0, inBShape[2]).Select(_ => new[] { (byte)0 }).SelectMany(i => i).ToArray(),
                new long[] { 1, 1, 1, inBShape[2] });
        var deqBBias =
            Tensor.From(
                Enumerable.Range(0, inBShape[3]).Select(_ => new[] { (byte)0 }).SelectMany(i => i).ToArray(),
                new long[] { inBShape[3] });
        var act = new ActParam2(inAShape[1] * inAShape[2], new(0, 1));
        act.FusedChannelScale(Enumerable.Repeat(0.1f, inAShape[1] * inAShape[2]).ToArray());

        // = Tensor<Half>.From(Enumerable.Range(0, in_a_shape[2]).Select(i => new[] { (Half)0.1, (Half)0.1, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity, (Half)0 }).SelectMany(i => i).ToArray(), new[] { 1, 1, in_a_shape[2], 7 });
        return new Fusion(
            $"TileMatMulCase",
            "k230",
            GNNEStore(
                DataTypes.Float16,
                GNNEMatMul(
                    act,
                    DataTypes.Float16,
                    GNNELoad(DataTypes.UInt8, inputA),
                    GNNELoad(DataTypes.UInt8, inputB),
                    GNNELoadW(DataTypes.Float16, act.ToAct0Data()),
                    GNNELoadW(DataTypes.UInt8, inputABias),
                    0,
                    0,
                    10,
                    deqBBias),
                new long[] { 1, 1, 1, 1 }),
            inputA,
            inputB);
    }

    // public IEnumerable<Tensor> GetInputs(IR.Fusion fusion, MatMulCaseParams caseParams)
    // {
    //     return (from p in fusion.Parameters
    //             let ttype = (IR.TensorType)p.TypeAnnotation
    //             select IR.F.Random.Normal(ttype.DType, 1, 1, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor());
    // }
    public IEnumerable<Tensor> GetInputs(Fusion fusion, MatMulCaseParams caseParams)
    {
        return from p in fusion.Parameters.ToArray()
               let ttype = (TensorType)p.TypeAnnotation
               select IR.F.Random.Uniform(DataTypes.UInt8, 255, 0, 0, ttype.Shape.ToValueArray()).Evaluate().AsTensor();
    }
}
