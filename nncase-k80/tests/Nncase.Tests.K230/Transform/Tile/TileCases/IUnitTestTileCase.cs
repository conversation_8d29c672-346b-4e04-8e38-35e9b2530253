﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using NetFabric.Hyperlinq;
using Nncase.IR;

namespace Nncase.Tests.K230.TransformTest;

public interface ITileCaseParams
{
}

public interface IUnitTestTileCase
{
    /// <summary>
    /// the default Tolerance.
    /// </summary>
    float GetTolerance(IR.Fusion fusion, ITileCaseParams caseParams);

    /// <summary>
    /// get the case params.
    /// </summary>
    IEnumerable<ITileCaseParams> GetCaseParams();

    /// <summary>
    /// get original fusion expression.
    /// </summary>
    IR.Fusion GetOriginalFunsion(ITileCaseParams caseParams);

    /// <summary>
    /// get the tile size.
    /// </summary>
    int[] GetTargetTileSize(ITileCaseParams caseParams);

    /// <summary>
    /// get input data expression.
    /// </summary>
    IEnumerable<Tensor> GetInputs(IR.Fusion fusion, ITileCaseParams caseParams);
}

public interface IUnitTestTileCase<T> : IUnitTestTileCase
  where T : ITileCaseParams
{
    /// <summary>
    /// the default Tolerance.
    /// </summary>
    float GetTolerance(IR.Fusion fusion, T caseParams) => 0.003f;

    /// <inheritdoc/>
    float IUnitTestTileCase.GetTolerance(IR.Fusion fusion, ITileCaseParams caseParams)
    {
        return GetTolerance(fusion, (T)caseParams);
    }

    /// <summary>
    /// get original fusion expression.
    /// </summary>
    IR.Fusion GetOriginalFunsion(T caseParams);

    /// <inheritdoc/>
    IR.Fusion IUnitTestTileCase.GetOriginalFunsion(ITileCaseParams caseParams)
    {
        return GetOriginalFunsion((T)caseParams);
    }

    /// <summary>
    /// get the tile size.
    /// </summary>
    int[] GetTargetTileSize(T caseParams) => System.Array.Empty<int>();

    /// <inheritdoc/>
    int[] IUnitTestTileCase.GetTargetTileSize(ITileCaseParams caseParams)
    {
        return GetTargetTileSize((T)caseParams);
    }

    /// <summary>
    /// get input data expression.
    /// </summary>
    IEnumerable<Tensor> GetInputs(IR.Fusion fusion, T caseParams)
    {
        return (from p in fusion.Parameters.AsValueEnumerable()
                let ttype = (IR.TensorType)p.TypeAnnotation
                select Testing.Seq(ttype.DType, ttype.Shape.ToValueArray())).ToArray();
    }

    IEnumerable<Tensor> IUnitTestTileCase.GetInputs(IR.Fusion fusion, ITileCaseParams caseParams)
    {
        return GetInputs(fusion, (T)caseParams);
    }
}
