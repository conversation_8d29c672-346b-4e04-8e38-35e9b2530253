﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.IR.K230;
using Tensors = Nncase.IR.K230.F.Tensors;

namespace Nncase.Tests.K230.TransformTest;

public struct GNNELSTMParams : IUnitTestCaseParams
{
    public int[] InShape;
    public int[] WXcShape;
    public int[] WRcShape;
    public LSTMDirection Direction;
    public int[] ActXcShape;
    public int[] ActRcShape;
    public int[] InitHShape;
    public int[] InitCShape;
    public int[] SegFittingParamFtShape;
    public int[] SegFittingParamGtShape;
    public int OutputSize;
}

public sealed class GNNELSTMCase : IUnitTestCase<FakeLSTMParams>
{
    public IEnumerable<IUnitTestCaseParams> GetCaseParams()
    {
        var inShapes = new object[] { new[] { 1, 1, 1, 3 }, };
        var wXcShapes = new object[] { new[] { 1, 2, 8, 3 }, };
        var wRcShapes = new object[] { new[] { 1, 2, 8, 2 }, };
        var directions = new object[]
        {
            LSTMDirection.Bidirectional,
            LSTMDirection.Forward,
            LSTMDirection.Reverse,
        };
        var actXcShapes = new object[] { new[] { 1, 1, 16, 7 }, };
        var actRcShapes = new object[] { new[] { 1, 1, 16, 7 }, };
        var initHShapes = new object[] { new[] { 1, 2, 1, 2 }, };
        var initCShapes = new object[] { new[] { 1, 2, 1, 2 }, };
        var segFittingParamFtShapes = new object[] { new[] { 1, 1, 1, 49 }, };
        var segFittingParamGtShapes = new object[] { new[] { 1, 1, 1, 49 }, };
        var outputSize = new object[] { 1, 2, 3, };

        return new[]
        {
            inShapes, wXcShapes, wRcShapes, directions, actXcShapes, actRcShapes, initHShapes, initCShapes, segFittingParamFtShapes, segFittingParamGtShapes, outputSize,
        }.CartesianProduct().Select(item => item.ToArray()).Select(item => new GNNELSTMParams
        {
            InShape = (int[])item[0],
            WXcShape = (int[])item[1],
            WRcShape = (int[])item[2],
            Direction = (LSTMDirection)item[3],
            ActXcShape = (int[])item[4],
            ActRcShape = (int[])item[5],
            InitHShape = (int[])item[6],
            InitCShape = (int[])item[7],
            SegFittingParamFtShape = (int[])item[8],
            SegFittingParamGtShape = (int[])item[9],
            OutputSize = (int)item[10],
        }).OfType<IUnitTestCaseParams>();
    }

    public Expr GetLSTMCall(GNNELSTMParams caseParams)
    {
        var curCase = caseParams;
        (int[] inShape, int[] wXcShape, int[] wRcShape, var lstmDirection, int[] actXcShape, int[] actRcShape, int[] initHShape, int[] initCShape, int[] segFittingParamFtShape, int[] segFittingParamGtShape, int outputSize) =
            (curCase.InShape, curCase.WXcShape, curCase.WRcShape, curCase.Direction, curCase.ActXcShape, curCase.ActRcShape, curCase.InitHShape, curCase.InitCShape, curCase.SegFittingParamFtShape, curCase.SegFittingParamGtShape, curCase.OutputSize);
        if (lstmDirection != LSTMDirection.Bidirectional)
        {
            wRcShape[1] = 1;
            wXcShape[1] = 1;
            initHShape[1] = 1;
            initCShape[1] = 1;
            actXcShape[2] = 8;
            actRcShape[2] = 8;
        }

        var input = Random.Uniform(DataTypes.Float32, 1, 0, 0, inShape).Evaluate().AsTensor();
        var wXc = Random.Uniform(DataTypes.Float32, 1, 0, 0, wXcShape).Evaluate().AsTensor();
        var actParamXc = new ActParam2(actXcShape[2]);
        var actParamRc = new ActParam2(actRcShape[2]);
        var actXc = new Tensor<float>(actParamXc.GetAct0Data, actXcShape.Select(x => (long)x).ToArray());
        var actRc = new Tensor<float>(actParamRc.GetAct0Data, actRcShape.Select(x => (long)x).ToArray());
        var wRc = Random.Uniform(DataTypes.Float32, 1, 0, 0, wRcShape).Evaluate().AsTensor();
        var initH = Random.Uniform(DataTypes.Float32, 1, 0, 0, initHShape).Evaluate().AsTensor();
        var initC = Random.Uniform(DataTypes.Float32, 1, 0, 0, initCShape).Evaluate().AsTensor();
        var segFittingParamFt = new Tensor<float>(new ActParam16(1).GetAct1Data, segFittingParamFtShape.Select(x => (long)x).ToArray());
        var segFittingParamGt = new Tensor<float>(new ActParam16(1).GetAct1Data, segFittingParamGtShape.Select(x => (long)x).ToArray());
        var fakeLstm = Tensors.FakeLSTM(
            Math.RangeOfMarker(input, new[] { 0f, 1.0f }),
            Math.RangeOfMarker(wXc, new[] { 0f, 1.0f }),
            actXc,
            Math.RangeOfMarker(wRc, new[] { 0f, 1.0f }),
            actRc,
            Math.RangeOfMarker(initH, new[] { 0f, 1.0f }),
            Math.RangeOfMarker(initC, new[] { 0f, 1.0f }),
            segFittingParamFt,
            segFittingParamGt,
            false,
            lstmDirection,
            outputSize,
            actParamXc,
            actParamRc);
        var outputShapes = new Shape[outputSize];
        outputShapes[0] = new Shape(inShape[1], initHShape[1], initHShape[2], initHShape[3]);
        if (outputSize > 1)
        {
            outputShapes[1] = new Shape(initHShape[1], initHShape[2], initHShape[3]);
        }

        if (outputSize > 2)
        {
            outputShapes[2] = outputShapes[1];
        }

        var tuple = new Tuple(Enumerable.Range(0, outputSize).Select(i => Math.RangeOfMarker(IR.F.Tensors.Reshape(IR.F.Tensors.GetItem(fakeLstm, i), outputShapes[i]), new[] { 0f, 3.0f })).ToArray());

        return tuple;
    }
}
