﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using Nncase.IR;
using static Nncase.IR.F.Math;
using static Nncase.IR.F.Random;
using static Nncase.IR.F.Tensors;
using static Nncase.Tests.DataGenerator;

namespace Nncase.Tests.K230.TransformTest;

public struct FakeLSTMParams : IUnitTestCaseParams
{
    public int InputSize;
    public int HiddenSize;
    public int OutputSize;
    public LSTMDirection Direction;
    public int BatchSize;
    public int SeqLength;
}

public sealed class LSTMCase : IUnitTestCase<FakeLSTMParams>
{
    public IEnumerable<IUnitTestCaseParams> GetCaseParams()
    {
        var inputSizes = new object[] { 2, 3 };
        var hiddenSizes = new object[] { 1, 2 };
        var outputSizes = new object[] { 1, };
        var directions = new object[] { LSTMDirection.Bidirectional, LSTMDirection.Forward, LSTMDirection.Reverse, };
        var batchSizes = new object[] { 1, };
        var seqLength = new object[] { 3, 1 };

        return new[] { inputSizes, hiddenSizes, outputSizes, directions, batchSizes, seqLength, }.CartesianProduct().Select(item => item.ToArray()).Select(item => new FakeLSTMParams
        {
            InputSize = (int)item[0],
            HiddenSize = (int)item[1],
            OutputSize = (int)item[2],
            Direction = (LSTMDirection)item[3],
            BatchSize = (int)item[4],
            SeqLength = (int)item[5],
        }).OfType<IUnitTestCaseParams>();
    }

    public Expr GetLSTMCall(FakeLSTMParams caseParams)
    {
        var curCase = caseParams;
        var (inputSize, hiddenSize, outputSize, lstmDirection, batchSize, seqLength) =
            (curCase.InputSize, curCase.HiddenSize, curCase.OutputSize, curCase.Direction, curCase.BatchSize, curCase.SeqLength);

        var numberDirections = lstmDirection == LSTMDirection.Bidirectional ? 2 : 1;

        var x = RangeOfMarker(Normal(DataTypes.Float32, 0, 1, 0, new[] { seqLength, batchSize, inputSize }), new[] { -3f, 3f });
        var initC = RangeOfMarker(Normal(DataTypes.Float32, 0, 1, 1, new[] { numberDirections, batchSize, hiddenSize }), new[] { -1f, 1f });
        var initH = RangeOfMarker(Normal(DataTypes.Float32, 0, 1, 2, new[] { numberDirections, batchSize, hiddenSize }), new[] { -1f, 1f });
        var b = Normal(DataTypes.Float32, 0, 1, 3, new[] { numberDirections, 8 * hiddenSize }).Evaluate().AsTensor();
        var w = RangeOfMarker(Normal(DataTypes.Float32, 0, 1, 4, new[] { numberDirections, 4 * hiddenSize, inputSize }).Evaluate().AsTensor(), new[] { -1f, 1f });
        var r = RangeOfMarker(Normal(DataTypes.Float32, 0, 1, 5, new[] { numberDirections, 4 * hiddenSize, hiddenSize }).Evaluate().AsTensor(), new[] { -1f, 1f });
        var p = new float[numberDirections, 3 * hiddenSize];
        var acts = lstmDirection == LSTMDirection.Bidirectional
            ? new[] { "Sigmoid", "Tanh", "Tanh", "Sigmoid", "Tanh", "Tanh" }
            : new[] { "Sigmoid", "Tanh", "Tanh" };
        var lstm = IR.F.RNN.LSTM(lstmDirection, LSTMLayout.Zero, acts, x, w, r, b, new[] { seqLength }, initH, initC, p, 0, 0, float.NaN, hiddenSize, 0, outputSize);
        var outputs = Enumerable.Range(0, outputSize).Select(i => RangeOfMarker(GetItem(lstm, i), new[] { -1f, 1f })).ToArray();
        var tuple = new Tuple(outputs);

        return tuple;
    }
}
