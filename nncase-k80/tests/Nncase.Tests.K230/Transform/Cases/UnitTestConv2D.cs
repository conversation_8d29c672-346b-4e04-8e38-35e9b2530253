﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using Nncase.IR;
using Nncase.IR.K230;

namespace Nncase.Tests.K230.TransformTest;

public struct Conv2dParams : IUnitTestCaseParams
{
    public int[] InShape;
    public int OutputChannel;
    public int[] KernelSize;
    public int[,] Padding;
    public int[] Stride;
    public int[] Dilation;
    public int Groups;
}

public sealed class Conv2dCase : IUnitTestCase<Conv2dParams>
{
    public IEnumerable<IUnitTestCaseParams> GetCaseParams()
    {
        var shapes = new object[] { new[] { 1, 4, 24, 32 }, new[] { 1, 4, 16, 16 }, new[] { 1, 2, 12, 12 } };

        var outputChannels = new object[] { 16, 32, };

        var strides = new object[] { new[] { 1, 1 }, new[] { 2, 2 }, };

        var kernelSizes = new object[] { new[] { 1, 1 }, new[] { 3, 3 }, };

        var paddings = new object[] { new[,] { { 0, 0 }, { 0, 0 } }, new[,] { { 0, 1 }, { 0, 1 } }, new[,] { { 1, 1 }, { 1, 1 } }, };

        var dilations = new object[] { new[] { 1, 1 }, new[] { 2, 2 }, };

        var groups = new object[] { 1, 2, };

        return new[] { shapes, outputChannels, strides, kernelSizes, paddings, dilations, groups }.CartesianProduct()
            .Select(item => item.ToArray()).Select(item => new Conv2dParams
            {
                InShape = (int[])item[0],
                OutputChannel = (int)item[1],
                Stride = (int[])item[2],
                KernelSize = (int[])item[3],
                Padding = (int[,])item[4],
                Dilation = (int[])item[5],
                Groups = (int)item[6],
            }).OfType<IUnitTestCaseParams>();
    }

    public Expr[] GetConv2DCase(Conv2dParams cur_case)
    {
        var (shape, outputChannel, stride, kernelSize, padding, dilation, groups) = (cur_case.InShape, cur_case.OutputChannel, cur_case.Stride, cur_case.KernelSize, cur_case.Padding, cur_case.Dilation, cur_case.Groups);
        var input = IR.F.Random.Uniform(DataTypes.Float32, 1, -1, 0, shape).Evaluate().AsTensor();
        var weight = IR.F.Random.Uniform(DataTypes.Float32, 1, -1, 0, new[] { outputChannel, shape[1] / groups, kernelSize[0], kernelSize[1] }).Evaluate().AsTensor();
        var act = new ActParam2(outputChannel);
        act.FusedChannelScale(Enumerable.Repeat(0.00390625f, outputChannel).ToArray());
        var conv2d = (Expr)IR.F.Math.RangeOfMarker(IR.F.NN.Conv2D(IR.F.Math.RangeOfMarker(input, new[] { -1.0f, 1.0f }), IR.F.Math.RangeOfMarker(weight, new[] { -1.0f, 1.0f }), IR.F.Random.Normal(DataTypes.Float32, new[] { outputChannel }).Evaluate().AsTensor(), stride, padding, dilation, PadMode.Constant, groups), new[] { -1.0f, 1.0f });
        var fakeConv2d = (Expr)IR.F.Math.RangeOfMarker(
                            IR.K230.F.Tensors.FakeConv2D(
                                IR.F.Math.RangeOfMarker(input, new[] { -1.0f, 1.0f }),
                                IR.F.Math.RangeOfMarker(
                                    weight,
                                    Tensor.From(
                                        Enumerable.Repeat(new[] { -1.0f, 1.0f }, outputChannel).SelectMany(x => x).ToArray(),
                                        new long[] { outputChannel, 2 })),
                                act.ToFakeActData(),
                                padding,
                                stride,
                                dilation,
                                groups,
                                0f,
                                act),
                            new[] { -1.0f, 1.0f });
        return new[] { conv2d, fakeConv2d };
    }
}
