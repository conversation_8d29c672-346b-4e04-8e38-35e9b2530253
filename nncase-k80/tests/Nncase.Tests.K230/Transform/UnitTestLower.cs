﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.K230;
using Nncase.Passes.Rules.Lower;
using Nncase.Passes.Rules.Tile;
using Nncase.Quantization;
using Nncase.Tests.K230.TargetTest;
using Nncase.Tests.TestFixture;
using Xunit;
using Xunit.Abstractions;
using static Nncase.IR.F.NN;
using static Nncase.IR.F.Random;

namespace Nncase.Tests.K230.TransformTest;

/// <summary>
/// UnitTestLower.
/// </summary>
[AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestLower : TestClassBase
{
    private readonly ITestOutputHelper _testOutputHelper;

    public UnitTestLower(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;
        CompileOptions.DumpFlags = DumpFlags.ImportOps | DumpFlags.EGraphCost | DumpFlags.Compile | DumpFlags.PassIR |
                                   DumpFlags.Rewrite;
        DefaultTargetName = "k230";
    }

    [Fact]
    private async Task TestToGNNEPad()
    {
        CompileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 2, 4, 8 }));

        // var input = Normal(DataTypes.Float32, new Shape(1, 2, 4, 8));
        var output = IR.F.NN.Pad(input, new[,] { { 0, 0 }, { 0, 0 }, { 1, 1 }, { 2, 2 } }, PadMode.Constant, 1f);

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        CompileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new Var[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(_ => { });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEPad>();
            p.Add<Passes.Rules.Neutral.FoldConstCall>();
        });
        await pmgr.RunAsync(module);

        var samples = await CompileOptions.QuantizeOptions.CalibrationDataset.Samples.ToListAsync();
        var rangeMin = float.MaxValue;
        var rangeMax = float.MinValue;

        // if range
        foreach (var sample in samples)
        {
            var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
            var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
            if (sampleMin < rangeMin)
            {
                rangeMin = sampleMin;
            }

            if (sampleMax > rangeMax)
            {
                rangeMax = sampleMax;
            }
        }

        var quantMode = CompileOptions.QuantizeOptions.QuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedAsymmetricMode;
        var qpExpected = Utilities.QuantUtility.GetQuantParam(new ValueRange<float>(rangeMin, rangeMax), 8, quantMode);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
    }

    [Fact]
    private async Task TestToGNNEActivation()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassContext passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var add = IR.F.Math.Add(input, 1.5f);
        var neg = IR.F.Math.Neg(add);

        var output = neg;

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("LowerFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<AddToFakeActivation>();
            p.Add<SubToFakeActivation>();
            p.Add<MulToFakeActivation>();
            p.Add<DivToFakeActivation>();
            p.Add<MaxToFakeActivation>();
            p.Add<MinToFakeActivation>();
            p.Add<AbsToFakeActivation>();
            p.Add<NegToFakeActivation>();
            p.Add<GeneralAddSubMulDivToFakeActivation>();
        });
        pmgr.AddWithName<DataflowPass>("Fold").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldTwoFakeActivation>();
            p.Add<FoldMaxAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEActivation>();
            p.Add<Passes.Rules.Neutral.FoldConstCall>();
        });

        await pmgr.RunAsync(module);

        var samples = await CompileOptions.QuantizeOptions.CalibrationDataset!.Samples.ToListAsync();
        var rangeMin = float.MaxValue;
        var rangeMax = float.MinValue;

        // if range
        foreach (var sample in samples)
        {
            var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
            var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
            if (sampleMin < rangeMin)
            {
                rangeMin = sampleMin;
            }

            if (sampleMax > rangeMax)
            {
                rangeMax = sampleMax;
            }
        }

        var quantMode = CompileOptions.QuantizeOptions.QuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedAsymmetricMode;
        var qpExpected = Utilities.QuantUtility.GetQuantParam(new ValueRange<float>(rangeMin, rangeMax), 8, quantMode);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));

        // var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        // dumpVisitor.Visit(module.Functions[0]);
        //
        // Assert.Equal(1, dumpVisitor.FoundOpCount<Nncase.IR.K230.GNNEActivation>());
        // Assert.Equal(1, dumpVisitor.FoundOpCount<IR.Math.Quantize>());
        // for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        // {
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is Nncase.IR.K510.FakeActivation)
        //     {
        //         var actParam = ((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1])).Value.Cast<float>();
        //         var channels = ((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1])).Value.Cast<float>().Shape[0].Value;
        //         for (int j = 0; j < channels; j++)
        //         {
        //             Assert.Equal(0, actParam[j, 0]);
        //             Assert.Equal(-1, actParam[j, 1]);
        //             Assert.Equal(-1.5f, actParam[j, 2]);
        //             Assert.Equal(-1, actParam[j, 3]);
        //             Assert.Equal(-1.5f, actParam[j, 4]);
        //         }
        //         break;
        //     }
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is IR.Math.Quantize)
        //     {
        //         // if quant param
        //         Assert.Equal((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].Scale, qpExpected.Scale);
        //         Assert.Equal((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].ZeroPoint, qpExpected.ZeroPoint);
        //     }
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is Nncase.IR.K510.GNNELoad)
        //     {
        //         // if dequant param
        //         if (((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0] is TensorConst && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0].CheckedShape == new Shape((int)(input.CheckedShape[1].FixedValue)))
        //         {
        //             for (int j = 0; j < (int)(input.CheckedShape[1].FixedValue); j++)
        //             {
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Scale, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Scale);
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].ZeroPoint, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).ZeroPoint);
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Shift, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Shift);
        //             }
        //         }
        //     }
        // }

        // Transform.PassManager pmgr2 = new(module, passOptions);
        // pmgr2.Add(new Transform.DataflowPass("4_Fusion")
        // {
        //     // new Passes.Rules.Lower.RemoveMarker(),
        //     new ActSIFFusion(),
        //     new TransposeFusion(),
        //     new Passes.Rules.Neutral.FoldNopReshape(),
        // });
        // pmgr2.Add(new K230FusionToTirPass("5_ToTir"));
        // // Before CodeGen
        // pmgr2.Add(new PrimFuncPass("6_BufferStage")
        // {
        //     Transform.Mutator.UnRollLoop(), // 展开循环.
        //     Transform.K230Mutator.FoldConstCall(), // 常量折叠所有的指令参数.
        //     Transform.Mutator.FoldLet(), // 折叠let表达式.
        //     Transform.Mutator.FoldIfThen(), // 折叠let表达式.
        //     Transform.Mutator.FlattenSequential(), // 折叠sequential.
        // });
        // pmgr2.Add(new Passes.DDrBufferSchdeulePass("7_DDrBufferSchdeule"));
        // //pmgr2.Add(new Transform.Passes.AssignCcrPass("8_AssignCcr"));
        // pmgr2.Add(new PrimFuncPass("8_MiddleStage")
        // {
        //     //Transform.K230Mutator.UnFoldEActions(), // 展开自定义的op.
        //     Transform.K230Mutator.FoldConstCall(), // 常量折叠所有的指令参数.
        //     Transform.K230Mutator.FoldBufferSlot(), //
        //     Transform.Mutator.FlattenSequential(), // 折叠sequential.
        // });
        // pmgr2.Add(new Passes.DDrBufferSchdeulePass("8.5_AssginAddress"));
        // pmgr2.Add(new PrimFuncPass("9_InstStage")
        // {
        //     Transform.K230Mutator.FoldConstCall(),
        //     Transform.K230Mutator.FoldBufferSlot() // 折叠自定义op.
        // });
        // await pmgr2.RunAsync();
        // var (kmodel_path, kmodel) = TestFixture.Testing.BuildKModel("main", module, compileOptions);
        //
        // var input_gen = Normal(DataTypes.Float32, new Shape(1, 3, 24, 32));
        // var input_tensor = new Tensor[] { input_gen.Evaluate().AsTensor() };
        // TestFixture.Testing.DumpInterpModel(kmodel_path, input_tensor, passOptions);
        // var post = TestFixture.Testing.RunKModel(kmodel, passOptions.CompileOptions.DumpDir, input_tensor);
        // var pre = IR.F.Math.Neg(IR.F.Math.Add(input_gen, 1.5f));
    }

    [Fact]
    private async Task TestToGNNEPdp1Reduce()
    {
        // var passOptions = GetPassOptions();
        CompileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 2, 4, 8 }));

        // var input = Normal(DataTypes.Float32, new Shape(1, 2, 4, 8));
        var output = IR.F.Math.RangeOfMarker(
            ReduceWindow2D(ReduceOp.Max, input, 1f, new[] { 3, 3 }, new[] { 1, 1 }, new[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 }, false, false), new[] { -1.0f, 1.0f });

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        CompileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakePdpReduce>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<ToGNNEPdpReduce>();
            p.Add<Passes.Rules.Neutral.FoldConstCall>();
        });
        await pmgr.RunAsync(module);

        var samples = await CompileOptions.QuantizeOptions.CalibrationDataset.Samples.ToListAsync();
        var rangeMin = float.MaxValue;
        var rangeMax = float.MinValue;

        // if range
        foreach (var sample in samples)
        {
            var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
            var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
            if (sampleMin < rangeMin)
            {
                rangeMin = sampleMin;
            }

            if (sampleMax > rangeMax)
            {
                rangeMax = sampleMax;
            }
        }

        var quantMode = CompileOptions.QuantizeOptions.QuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedAsymmetricMode;
        var qpExpected = Utilities.QuantUtility.GetQuantParam(new ValueRange<float>(rangeMin, rangeMax), 8, quantMode);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));

        // var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        // dumpVisitor.Visit(module.Functions[0]);
        // Assert.Equal(1, dumpVisitor.FoundOpCount<IR.Math.Quantize>());
        //
        // for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        // {
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is IR.Math.Quantize)
        //     {
        //         // if quant param
        //         Assert.Equal((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].Scale, qpExpected.Scale);
        //         Assert.Equal((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].ZeroPoint, qpExpected.ZeroPoint);
        //     }
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is Nncase.IR.K510.GNNELoad)
        //     {
        //         // if dequant param
        //         if (((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0] is TensorConst && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0].CheckedShape == new Shape((int)(input.CheckedShape[1].FixedValue)))
        //         {
        //             for (int j = 0; j < (int)(input.CheckedShape[1].FixedValue); j++)
        //             {
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Scale, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Scale);
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].ZeroPoint, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).ZeroPoint);
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Shift, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Shift);
        //             }
        //         }
        //     }
        // }
        var pmgr2 = CompileSession.CreatePassManager("Passes");
        pmgr2.AddWithName<DataflowPass>("Fusion").Configure(p =>
        {
            p.Add<Pdp1Fusion>();
            p.Add<TransposeFusion>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
        });

        pmgr2.AddWithName<K230FusionToTirPass>("ToTir");

        // Before CodeGen
        pmgr2.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>(); // 常量折叠所有的指令参数.
        });
        pmgr2.AddWithName<DDrBufferSchdeulePass>("DDrBufferSchdeule");

        // pmgr2.Add(new Transform.Passes.AssignCcrPass("8_AssignCcr"));
        pmgr2.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>(); // 折叠自定义op.
        });
        pmgr2.AddWithName<DDrBufferSchdeulePass>("AssginAddress");
        await pmgr2.RunAsync(module);

        // var (kmodel_path, kmodel) = TestFixture.Testing.BuildKModel("main", module, passOptions.CompileOptions);
        // var input_gen = Normal(DataTypes.Float32, new Shape(1, 2, 4, 8));
        // var input_tensor = new Tensor[] { input_gen.Evaluate().AsTensor() };
        // TestFixture.Testing.DumpInterpModel(kmodel_path, input_tensor, passOptions);
        // var post = TestFixture.Testing.RunKModel(kmodel, passOptions.DumpDir, input_tensor);

        // var pre = IR.F.Math.RangeOfMarker(ReduceWindow2D(ReduceOp.Max,
        //     input_gen,
        //     1f, new[] { 3, 3 }, new[] { 1, 1 },
        //     new[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 },
        //     false, false),new float[]{-1.0f,1.0f});
    }

    [Fact]
    private async Task TestToGNNETranspose()
    {
        // var passOptions = GetPassOptions();
        CompileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 2, 4, 8 }));

        // var input = Normal(DataTypes.Float32, new Shape(1, 2, 4, 8));
        var output = IR.F.Tensors.Transpose(input, new[] { 3, 1, 0, 2 });

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        CompileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(_ => { });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("3_AfterQuant").Configure(p =>
        {
            p.Add<ToGNNETranspose>();
            p.Add<Passes.Rules.Neutral.FoldConstCall>();

            // new FoldQuantStore()
        });
        await pmgr.RunAsync(module);

        var samples = await CompileOptions.QuantizeOptions.CalibrationDataset.Samples.ToListAsync();
        var rangeMin = float.MaxValue;
        var rangeMax = float.MinValue;

        // if range
        foreach (var sample in samples)
        {
            var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
            var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
            if (sampleMin < rangeMin)
            {
                rangeMin = sampleMin;
            }

            if (sampleMax > rangeMax)
            {
                rangeMax = sampleMax;
            }
        }

        var quantMode = CompileOptions.QuantizeOptions.QuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedAsymmetricMode;
        var qpExpected = Utilities.QuantUtility.GetQuantParam(new ValueRange<float>(rangeMin, rangeMax), 8, quantMode);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));

        // var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        // dumpVisitor.Visit(module.Functions[0]);
        // Assert.Equal(1, dumpVisitor.FoundOpCount<IR.Math.Quantize>());

        // for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        // {
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is IR.Math.Quantize)
        //     {
        //         // if quant param
        //         Assert.Equal((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].Scale, qpExpected.Scale);
        //         Assert.Equal((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].ZeroPoint, qpExpected.ZeroPoint);
        //     }
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is Nncase.IR.K510.GNNELoad)
        //     {
        //         // if dequant param
        //         if (((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0] is TensorConst && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0].CheckedShape == new Shape((int)(input.CheckedShape[1].FixedValue)))
        //         {
        //             for (int j = 0; j < (int)(input.CheckedShape[1].FixedValue); j++)
        //             {
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Scale, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Scale);
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].ZeroPoint, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).ZeroPoint);
        //                 Assert.Equal(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Shift, IR.K510.GNNEUtil.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Shift);
        //             }
        //         }
        //     }
        // }
        var pmgr2 = CompileSession.CreatePassManager("Passes");
        pmgr2.AddWithName<DataflowPass>("Fusion").Configure(p =>
        {
            p.Add<ConvFusion>();
            p.Add<TransposeFusion>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
        });

        pmgr2.AddWithName<K230FusionToTirPass>("ToTir");

        // Before CodeGen
        pmgr2.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
        {
            // p.Add<Passes.Mutators.UnRollLoopSequential>();
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Passes.Mutators.FoldLet>();
            p.Add<Passes.Mutators.FoldIfThen>();

            // p.Add<Mutators.FlattenSequential>();
        });
        pmgr2.AddWithName<DDrBufferSchdeulePass>("DDrBufferSchdeule");

        // pmgr2.Add(new Transform.Passes.AssignCcrPass("8_AssignCcr"));
        pmgr2.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>(); // 折叠自定义op.
            p.Add<Passes.Mutators.FlattenSequential>();
        });
        pmgr2.AddWithName<DDrBufferSchdeulePass>("AssginAddress");
        pmgr2.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>();
            p.Add<Passes.Mutators.FlattenSequential>();
        });
        await pmgr2.RunAsync(module);

        // var (kmodel_path, kmodel) = TestFixture.Testing.BuildKModel("main", module, passOptions.CompileOptions);
        // var input_gen = Normal(DataTypes.Float32, new Shape(1, 2, 4, 8));
        // var input_tensor = new Tensor[] { input_gen.Evaluate().AsTensor() };
        // TestFixture.Testing.DumpInterpModel(kmodel_path, input_tensor, passOptions);
        // var post = TestFixture.Testing.RunKModel(kmodel, passOptions.DumpDir, input_tensor);
        // var pre = IR.F.Tensors.Transpose(input_gen, new[] { 3, 1, 0, 2 });

        // passOptions.CompileOptions.ModelQuantMode = ModelQuantMode.NoQuant;
        // TestMatched<ToGNNETranspose>(pre, passOptions);
        // passOptions.CompileOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        // TestMatched<ToGNNETranspose>(pre, passOptions);
    }

    [Fact]
    private async Task TestToGNNEConv2D()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var weights = Normal(DataTypes.Float32, new[] { 16, 3, 3, 3 }).Evaluate();
        var bias = Normal(DataTypes.Float32, new[] { 16 }).Evaluate();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 0, 1 }, { 0, 0 } };

        var conv = Conv2D(input, weights.AsTensor(), bias.AsTensor(), stride, padding, dilation, PadMode.Constant, 1);
        var neg = IR.F.Math.Neg(conv);

        var output = neg;

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new SolidCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<NegToFakeActivation>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
        });
        pmgr.AddWithName<DataflowPass>("FoldFakeConv2DAndFakeAct").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldFakeConv2DAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEPdpReduce>();
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNETranspose>();
            p.Add<EliminateFloat32>();
            p.Add<Passes.Rules.Neutral.FoldQuantDeQuant>();
            p.Add<Passes.Rules.Neutral.FoldDeQuantQuant>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
            p.Add<FuseQuantIntoConv>();
            p.Add<FuseQuantIntoPdp1>();
            p.Add<DWToPdp>();
        });

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor.FoundOpCount<Nncase.IR.K230.GNNEActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is IR.K230.GNNEConv2D)
            {
                var qp = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[9]).Value
                    .ToArray<DeQuantizeParam>()[0];
                Assert.Equal(128, qp.ZeroPoint);
                Assert.Equal(0.007839733F, qp.Scale);
                break;
            }
        }
    }

    [Fact]
    private async Task TestUnsupportedInt16()
    {
        var compileOptions = CompileOptions;

        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 224, 224 }));
        var weights = Normal(DataTypes.Float32, new[] { 32, 3, 3, 3 }).Evaluate();
        var bias = Normal(DataTypes.Float32, new[] { 32 }).Evaluate();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 0, 0 }, { 0, 0 } };

        var weightsDW = Normal(DataTypes.Float32, new[] { 32, 1, 3, 3 }).Evaluate();
        var biasDW = Normal(DataTypes.Float32, new[] { 32 }).Evaluate();
        var strideDW = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilationDW = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var paddingDW = new[,] { { 0, 0 }, { 0, 0 } };

        var conv = Conv2D(input, weights.AsTensor(), bias.AsTensor(), stride, padding, dilation, PadMode.Constant, 1);
        var dw = Conv2D(conv, weightsDW.AsTensor(), biasDW.AsTensor(), strideDW, paddingDW, dilationDW, PadMode.Constant, 32);

        var output = dw;

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        compileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            QuantType = DataTypes.Int16,
            WQuantType = DataTypes.UInt8,
            CalibrationDataset = new SolidCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<DisableConvDWPermitInt16Quant>();
        });

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();
        });
        pmgr.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<RestoreFakeConv2D>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
            p.Add<RemoveMarker>();
        });
        pmgr.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();
        });

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor.FoundOpCount<IR.NN.Conv2D>());
        Assert.Equal(2, dumpVisitor.FoundOpCount<Nncase.IR.K230.GNNEConv2D>());
    }

    [Fact]
    private async Task TestFoldGNNEPdp0ReduceAndGNNEActivationOpt()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var weights = Normal(DataTypes.Float32, new[] { 16, 3, 3, 3 }).Evaluate();
        var bias = Normal(DataTypes.Float32, new[] { 16 }).Evaluate();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 0, 1 }, { 0, 0 } };

        var conv = Conv2D(input, weights.AsTensor(), bias.AsTensor(), stride, padding, dilation, PadMode.Constant, 1);
        var reduce = ReduceWindow2D(ReduceOp.Max, conv, 1f, new[] { 3, 3 }, new[] { 1, 1 }, new[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 }, false, false);
        var neg = IR.F.Math.Neg(reduce);

        var output = neg;

        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");
        var pmgr2 = CompileSession.CreatePassManager("Passes2");

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new SolidCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });
        pmgr2.AddWithName<DataflowPass>("TargetInDependent2").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");
        pmgr2.AddWithName<EGraphPassWithQuantize>("AssignRanges2");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<ToFakePdpReduce>();
            p.Add<NegToFakeActivation>();
        });
        pmgr2.AddWithName<DataflowPass>("TargetDependent2").Configure(p =>
        {
            p.Add<ToFakeConv2D>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<ToFakePdpReduce>();
            p.Add<NegToFakeActivation>();
        });

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<ToGNNEPdpReduce>();
        });
        pmgr.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNEActivation>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
            p.Add<RemoveMarker>();
        });
        pmgr.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FuseQuantIntoPdp0Reduce>();
        });

        // 3. after quant pass
        pmgr2.AddWithName<DataflowPass>("Lowering").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<ToGNNEPdpReduce>();
        });
        pmgr2.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNEActivation>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
        });
        pmgr2.AddWithName<DataflowPass>("AfterFuse").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FuseQuantIntoPdp0Reduce>();
            p.Add<FoldGNNEPdp0ReduceAndGNNEActivation>();
        });

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(1, dumpVisitor.FoundOpCount<Nncase.IR.K230.GNNEActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is IR.K230.GNNEPdp0Reduce)
            {
                ((TensorConst)((Call)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[8])
                    .Arguments[0]).Value.ToArray<float>();
                break;
            }
        }

        await pmgr2.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor2 = new UnitTestK230Target.DumpVisitor();
        dumpVisitor2.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor2.FoundOpCount<Nncase.IR.K230.GNNEActivation>());

        // for (int i = 0; i < dumpVisitor2.ExpressionMemo.Keys.Count; i++)
        // {
        //     if (dumpVisitor2.ExpressionMemo.Keys.ToList()[i] is Call &&
        //         ((Call)dumpVisitor2.ExpressionMemo.Keys.ToList()[i]).Target is Nncase.IR.K230.GNNEPdp0Reduce)
        //     {
        //         float[] actParamAfterFold =
        //             ((TensorConst)((Call)((Call)dumpVisitor2.ExpressionMemo.Keys.ToList()[i]).Parameters[8])
        //                 .Parameters[0]).Value.ToArray<float>();
        //         for (int j = 0; j < actParamAfterFold.Length; j++)
        //         {
        //             if (j % 7 == 0 || (j - 1) % 7 == 0 || (j - 2) % 7 == 0 || (j - 3) % 7 == 0)
        //                 Assert.Equal(actParamAfterFold[j], -actParamBeforeFold[j]);
        //             else
        //             {
        //                 if ((j - 4) % 7 == 0)
        //                     Assert.Equal(actParamAfterFold[j], -actParamBeforeFold[j + 1]);
        //                 else if ((j - 5) % 7 == 0)
        //                     Assert.Equal(actParamAfterFold[j], -actParamBeforeFold[j - 1]);
        //                 else
        //                     Assert.Equal(actParamAfterFold[j], actParamBeforeFold[j]);
        //             }
        //         }

        // break;
        //     }
        // }
    }

    [Fact]
    private async Task TestToGNNEMatmul()
    {
        // var caseOptions = GetPassOptions();
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassContext passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input_a", new TensorType(DataTypes.Float32, new[] { 1, 1, 3, 4 }));
        var weights = Normal(DataTypes.Float32, new Shape(1, 1, 4, 5)).Evaluate().AsTensor();

        var matmul = IR.F.Math.MatMul(input, weights);

        var output = matmul;
        var module = new IRModule(new Function("main", output, new Var[] { input }));

        var eachChannelSize = 20;

        // w range
        List<ValueRange<float>> wRanges = new();
        var tmpMin = float.MaxValue;
        var tmpMax = float.MinValue;
        for (int i = 0; i < weights.Shape.Size; i++)
        {
            if (i % eachChannelSize == 0)
            {
                tmpMin = float.MaxValue;
                tmpMax = float.MinValue;
            }

            if (weights.ToArray<float>()[i] > tmpMax)
            {
                tmpMax = weights.ToArray<float>()[i];
            }

            if (weights.ToArray<float>()[i] < tmpMin)
            {
                tmpMin = weights.ToArray<float>()[i];
            }

            if ((i + 1) % 20 == 0)
            {
                wRanges.Add(new ValueRange<float>(tmpMin, tmpMax));
            }
        }

        List<QuantParam> wqpExpected = new();
        var quantModeW = compileOptions.QuantizeOptions.WQuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedAsymmetricMode;
        for (int i = 0; i < wRanges.Count; i++)
        {
            wqpExpected.Add(Utilities.QuantUtility.GetQuantParam(new ValueRange<float>(wRanges[i].Min, wRanges[i].Max), 8, quantModeW));
        }

        List<float> quantedWExpected = new();
        for (int i = 0; i < weights.Shape.Size; i++)
        {
            quantedWExpected.Add((float)System.Math.Round(
                (weights.ToArray<float>()[i] / (double)wqpExpected[i / eachChannelSize].Scale) +
                wqpExpected[i / eachChannelSize].ZeroPoint));
        }

        // Transform.PassManager pmgr = new(module, passOptions);
        var pmgr = CompileSession.CreatePassManager("Passes");
        var pmgr2 = CompileSession.CreatePassManager("Passes");

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeMatmul>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEMatMul>();
        });

        await pmgr.RunAsync(module);

        // var samples = await compileOptions.QuantizeOptions.CalibrationDataset.Samples.ToListAsync();
        // var rangeMin = float.MaxValue;
        // var rangeMax = float.MinValue;
        // // if range
        // foreach (var sample in samples)
        // {
        //     var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
        //     var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
        //     if (sampleMin < rangeMin)
        //         rangeMin = sampleMin;
        //     if (sampleMax > rangeMax)
        //         rangeMax = sampleMax;
        // }
        // var quantMode = compileOptions.QuantType == DataTypes.UInt8 ? QuantMode.UnsignedMode : QuantMode.SignedAsymmetricMode;
        // var qpExpected = Utilities.QuantUtility.GetQuantParam(new ValueRange<float> (rangeMin, rangeMax), 8, quantMode);
        //
        // System.Console.WriteLine(CompilerServices.Print((Function)(module.Functions[0])));
        // var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        // dumpVisitor.Visit(module.Functions[0]);
        // Trace.Assert(dumpVisitor.FoundOpCount<IR.Math.Quantize>() == 1);
        //
        // for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        // {
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is IR.Math.Quantize)
        //     {
        //         // if quant param
        //         Trace.Assert((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].Scale == qpExpected.Scale);
        //         Trace.Assert((((TensorConst)(((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[1]))).Value.ToArray<QuantParam>()[0].ZeroPoint == qpExpected.ZeroPoint);
        //     }
        //     if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Target is Nncase.IR.K510.GNNELoad)
        //     {
        //         // if dequant param
        //         if (((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0] is TensorConst && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0].CheckedShape == new Shape((int)(input.CheckedShape[1].FixedValue)))
        //         {
        //             for (int j = 0; j < (int)(input.CheckedShape[1].FixedValue); j++)
        //             {
        //                 Trace.Assert(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Scale == IR.K230.GNNETypePatternUtility.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).Scale);
        //                 Trace.Assert(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].ZeroPoint == IR.K230.GNNETypePatternUtility.GetGNNEDeqParams(qpExpected.Scale, qpExpected.ZeroPoint).ZeroPoint);
        //             }
        //         }
        //         // w quant param
        //         if (((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0] is TensorConst && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0].CheckedShape == new Shape(weights.Shape[0]))
        //         {
        //             for (int j = 0; j < (int)(weights.Shape[0].FixedValue); j++)
        //             {
        //                 Trace.Assert(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].Scale == IR.K230.GNNETypePatternUtility.GetGNNEDeqParams(wqpExpected[j].Scale, wqpExpected[j].ZeroPoint).Scale);
        //                 Trace.Assert(((TensorConst)((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0]).Value.ToArray<DeQuantizeParam>()[j].ZeroPoint == IR.K230.GNNETypePatternUtility.GetGNNEDeqParams(wqpExpected[j].Scale, wqpExpected[j].ZeroPoint).ZeroPoint);
        //             }
        //         }
        //         // quanted w
        //         if (((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0] is TensorConst && ((Call)(dumpVisitor.ExprMemo.Keys.ToList()[i])).Parameters[0].CheckedDataType == compileOptions.WQuantType)
        //         {
        //             for (int j = 0; j < weights.Shape.Size; j++)
        //                 Trace.Assert(weights.ToArray<float>()[j] == quantedWExpected[j]);
        //         }
        //     }
        // }

        // Transform.PassManager pmgr2 = new(module, caseOptions);
        // pmgr2.Add(new Transform.DataflowPass("4_Fusion"){
        //     // new Passes.Rules.Lower.RemoveMarker(),
        //     // todo: matmul fusion
        //     new Passes.Rules.Neutral.FoldNopReshape(),
        // });
        // pmgr2.Add(new Passes.Rules.Tile.FusionToTirPass("5_ToTir", Transform.Passes.Tile.TileOptions.DefaultForTest));
        // // Before CodeGen
        // pmgr2.Add(new PrimFuncPass("6_BufferStage") {
        //     Transform.Mutator.UnRollLoop(), // 展开循环.
        //     Transform.ExtMutator.FoldConstCall(), // 常量折叠所有的指令参数.
        //     Transform.Mutator.FoldLet(), // 折叠let表达式.
        //     Transform.Mutator.FoldIfThen(), // 折叠let表达式.
        //     Transform.Mutator.FlattenSequential(), // 折叠sequential.
        // });
        // pmgr2.Add(new Passes.DDrBufferSchdeulePass("7_DDrBufferSchdeule"));
        // pmgr2.Add(new Transform.Passes.AssignCcrPass("8_AssignCcr"));
        // pmgr2.Add(new PrimFuncPass("9_MiddleStage") {
        //     Transform.ExtMutator.UnFoldEActions(), // 展开自定义的op.
        //     Transform.ExtMutator.FoldConstCall(), // 常量折叠所有的指令参数.
        //     Transform.ExtMutator.FoldBufferSlot(), //
        //     Transform.Mutator.FlattenSequential(), // 折叠sequential.
        // });
        // pmgr2.Add(new Passes.DDrBufferSchdeulePass("9.5_AssginAddress"));
        // pmgr2.Add(new PrimFuncPass("10_InstStage") {
        //     Transform.ExtMutator.FoldConstCall(),
        //     Transform.ExtMutator.FoldCustomInstructionOp() // 折叠自定义op.
        // });
        // await pmgr2.RunAsync();
        // var (kmodel_path, kmodel) = TestFixture.Testing.BuildKModel("main", module, compileOptions);
        // var input_gen = Normal(DataTypes.Float32, new Shape(3, 4));
        // var input_tensor = new Tensor[] { input_gen.Evaluate().AsTensor() };
        // TestFixture.Testing.DumpInterpModel(kmodel_path, input_tensor, caseOptions);
        // var post = TestFixture.Testing.RunKModel(kmodel, caseOptions.DumpDir, input_tensor);
        // var pre = IR.F.Math.MatMul(input_gen, weights);
    }
}
