﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.Passes;
using Nncase.Passes.Rules.K230;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using Nncase.Utilities;
using Xunit;
using static Nncase.IR.F.Math;
using static Nncase.IR.F.Random;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.Tests.DataGenerator;
using Quantize = Nncase.IR.Math.Quantize;
using Tuple = Nncase.IR.Tuple;

namespace Nncase.Tests.K230.TransformTest;

/// <summary>
/// UnitTestOpt.
/// </summary>
[TestFixture.AutoSetupTestMethod(InitSession = true)]
public class UnitTestOpt : TransformTestBase
{
#if false
    [Fact]
    public void TestFuseQuantIntoConv()
    {
        var q = Quantize(GNNEStore(DataTypes.Float32, GnneConv2D()), new QuantParam(0, 1), DataTypes.UInt8);
        TestMatched<FuseQuantIntoConv>(q);
    }
#endif

    [Fact]
    public void TestQuantToAct1()
    {
        var input = DefaultRandom(DataTypes.Float32, new long[] { 3, 2, 4, 8 });
        var q = Quantize(input, new QuantParam(0, 1), DataTypes.Int16);
        TestMatched<QuantToAct1>(q);
    }

    [Fact]
    public void TestDeqToAct1()
    {
        var input = DefaultRandom(DataTypes.UInt8, new long[] { 1, 2, 4, 8 });
        var q = Dequantize(input, new QuantParam(0, 1), DataTypes.Float32);
        TestMatched<DeqToAct1>(q);
    }

    [Fact]
    public void TestFoldStoreLoad()
    {
        var input = DefaultRandom(DataTypes.Int8, new long[] { 1, 3, 10, 10 });
        var pre = GNNELoad(DataTypes.Int8, GNNEStore(DataTypes.Int8, input));
        TestMatched<FoldStoreLoad>(pre);
    }

    [Fact]
    public void TestFoldLoadStore()
    {
        var input = DefaultRandom(DataTypes.Int8, new long[] { 1, 3, 10, 10 });
        var pre = GNNEStore(DataTypes.Int8, GNNELoad(DataTypes.Int8, input));
        TestMatched<FoldLoadStore>(pre);
    }

    [Fact]
    public async Task TestEliminateFloat32Async()
    {
        var inputData = DefaultRandom(DataTypes.Float16, new long[] { 1, 3, 10, 10 });
        var input = new Var("input", new TensorType(DataTypes.Float16, new[] { 1, 3, 10, 10 }));
        var q = GNNELoad(DataTypes.Float16, GNNEStore(DataTypes.Float32, input));
        var pmgr = CompileSession.CreatePassManager("Passes");
        pmgr.AddWithName<DataflowPass>("TestFuseQuantIntoPdp1").Configure(p =>
        {
            p.AddAnalysis<Passes.Analysis.IExprUserAnalysisResult>();
            p.Add<EliminateFloat32>();
        });
        var module = new IRModule(new Function("main", q, input));
        var feedDictPre = new Dictionary<Var, IValue>();
        feedDictPre.Add(((Function)module.Entry!).Parameters[0], inputData.Evaluate());
        var v1 = ((Function)module.Entry).Body.Evaluate(feedDictPre);
        await pmgr.RunAsync(module);
        var feedDictPost = new Dictionary<Var, IValue> { { ((Function)module.Entry).Parameters[0], inputData.Evaluate() } };
        var v2 = ((Function)module.Entry).Body.Evaluate(feedDictPost);
        Assert.NotEqual(((Call)((Function)module.Entry).Body)[IR.K230.GNNELoad.Input].CheckedDataType, DataTypes.Float32);
        Comparator.Compare(v1, v2);
    }

    [Fact]
    public void TestFuseDequantIntoPdp0()
    {
        var quantMode = QuantMode.SignedSymmetricMode;
        var inQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var deqParamFromQ = GNNEGetDeqParams(1, inQP.Scale, inQP.ZeroPoint);
        var outQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var quantParamFromQ = GNNEGetQuantParams(1, outQP.Scale, outQP.ZeroPoint);
        var input = DefaultRandom(DataTypes.UInt8, new long[] { 3, 2, 4, 8 });
        var actParam = new ActParam2(2, new QuantParam(outQP.ZeroPoint, outQP.Scale), true);
        var r = GNNEPdp0Reduce(
            PU_PDP0_MODE.max,
            DataTypes.Float16,
            actParam,
            Dequantize(input, new QuantParam(0, 1), DataTypes.UInt8),
            new[] { 3, 3 },
            new[] { 1, 1 },
            new[,] { { 0, 0 }, { 0, 0 } },
            deqParamFromQ,
            1,
            0,
            false,
            actParam.ToAct0Data());
        TestMatched<FuseDequantIntoPdp0>(r);
    }

    [Fact]
    public void TestFuseDequantIntoPdp1()
    {
        var quantMode = QuantMode.SignedSymmetricMode;
        var inQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var deqParamFromQ = GNNEGetDeqParams(1, inQP.Scale, inQP.ZeroPoint);
        var outQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var quantParamFromQ = GNNEGetQuantParams(1, outQP.Scale, outQP.ZeroPoint);
        var input = DefaultRandom(DataTypes.UInt8, new long[] { 3, 2, 4, 8 });
        var r = GNNEPdp1(
            DataTypes.Float16,
            MFU_PDP_OP.MAX,
            Dequantize(input, new QuantParam(0, 1), DataTypes.UInt8),
            new[] { 3, 3 },
            new[] { 1, 1 },
            new[,] { { 0, 0 }, { 0, 0 } },
            quantParamFromQ,
            deqParamFromQ,
            1f,
            0,
            false);
        TestMatched<FuseDequantIntoPdp1>(r);
    }

    [Fact]
    public void TestFuseQuantIntoPdp0Dw()
    {
        var pre = GnneConv2D();

        // var pre = FuseQuantHelper.MakePattern<GNNEConv2D>("call","op");
        TestNotMatch<FuseQuantIntoConv>(pre);
    }

    [Fact]
    public async Task TestFuseQuantIntoPdp1Async()
    {
        var quantMode = QuantMode.SignedSymmetricMode;
        var inQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var deqParamFromQ = GNNEGetDeqParams(1, inQP.Scale, inQP.ZeroPoint);
        var outQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var quantParamFromQ = GNNEGetQuantParams(1, outQP.Scale, outQP.ZeroPoint);
        var inputData = DefaultRandom(DataTypes.Float16, new long[] { 3, 2, 4, 8 });
        var input = new Var("input", new TensorType(DataTypes.Float16, new int[] { 3, 2, 4, 8 }));
        var r = GNNEPdp1(
            DataTypes.Float16,
            MFU_PDP_OP.MAX,
            GNNELoad(DataTypes.Float16, input),
            new[] { 3, 3 },
            new[] { 1, 1 },
            new[,] { { 0, 0 }, { 0, 0 } },
            quantParamFromQ,
            deqParamFromQ,
            1f,
            0,
            false);
        var q = Quantize(GNNEStore(DataTypes.Float32, r), new QuantParam(0, 1), DataTypes.UInt8);
        var pmgr = CompileSession.CreatePassManager("Passes");
        pmgr.AddWithName<DataflowPass>("TestFuseQuantIntoPdp1").Configure(p =>
        {
            p.AddAnalysis<Passes.Analysis.IExprUserAnalysisResult>();
            p.Add<FuseQuantIntoPdp1>();
        });
        var module = new IRModule(new Function("main", q, input));
        var feedDictPre = new Dictionary<Var, IValue> { { ((Function)module.Entry!).Parameters[0], inputData.Evaluate() } };
        var v1 = ((Function)module.Entry).Body.Evaluate(feedDictPre);
        await pmgr.RunAsync(module);
        var feedDictPost = new Dictionary<Var, IValue> { { ((Function)module.Entry).Parameters[0], inputData.Evaluate() } };
        var v2 = ((Function)module.Entry).Body.Evaluate(feedDictPost);
        var testV = new TestVisitor();
        testV.Visit(((Function)module.Entry).Body);
        int quantNum = testV.CountCallOp<Quantize>();
        Assert.Equal(0, quantNum);
        Comparator.Compare(v1, v2);
    }

    [Fact]
    public async Task TestFuseQuantIntoPdp0Reduce()
    {
        var quantMode = QuantMode.SignedSymmetricMode;
        var inQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var deqParamFromQ = GNNEGetDeqParams(1, inQP.Scale, inQP.ZeroPoint);
        var outQP = QuantUtility.GetQuantParam(new ValueRange<float>(-1.0f, 1.0f), 8, quantMode);
        var quantParamFromQ = GNNEGetQuantParams(1, outQP.Scale, outQP.ZeroPoint);
        var input = new Var("input", new TensorType(DataTypes.Float16, new[] { 3, 2, 4, 8 }));
        var inputData = DefaultRandom(DataTypes.Float16, new long[] { 3, 2, 4, 8 });
        var actParam = new ActParam2(2, new QuantParam(outQP.ZeroPoint, outQP.Scale), true);
        var r = GNNEPdp0Reduce(
            PU_PDP0_MODE.max,
            DataTypes.Float16,
            actParam,
            GNNELoad(DataTypes.Float16, input),
            new[] { 3, 3 },
            new[] { 1, 1 },
            new[,] { { 0, 0 }, { 0, 0 } },
            deqParamFromQ,
            1,
            0,
            false,
            actParam.ToAct0Data());
        var q = Quantize(GNNEStore(DataTypes.Float32, r), new QuantParam(0, 1), DataTypes.Int16);

        var pmgr = CompileSession.CreatePassManager("Passes");
        pmgr.AddWithName<DataflowPass>("TestFuseQuantIntoPdp0").Configure(p =>
        {
            p.AddAnalysis<Passes.Analysis.IExprUserAnalysisResult>();
            p.Add<FuseQuantIntoPdp0Reduce>();
        });

        var module = new IRModule(new Function("main", q, input));
        var feedDictPre = new Dictionary<Var, IValue> { { ((Function)module.Entry!).Parameters[0], inputData.Evaluate() } };
        var v1 = ((Function)module.Entry).Body.Evaluate(feedDictPre);
        await pmgr.RunAsync(module);
        var feedDictPost = new Dictionary<Var, IValue> { { ((Function)module.Entry).Parameters[0], inputData.Evaluate() } };
        var v2 = ((Function)module.Entry).Body.Evaluate(feedDictPost);
        var testV = new TestVisitor();
        testV.Visit(((Function)module.Entry).Body);
        var quantNum = testV.CountCallOp<Quantize>();
        Assert.Equal(0, quantNum);
        Comparator.Compare(v1, v2);
    }

#if false
    [Fact]
    public void TestFoldBitcastLeakyBitcast()
    {
        var passOptions = GetPassOptions();
        var input = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var input_b = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var bc1 = Bitcast(DataTypes.Float16, input, DataTypes.Float16, new[] {1, 1, 1, 1});
        var bin1 = IR.F.Math.Binary(BinaryOp.Mul, bc1, input_b);
        var bin2 = Binary(BinaryOp.Max,bin1, input_b);
        var bc2 = Bitcast(DataTypes.Float16, bin2, DataTypes.Float16, new[] {1, 1, 1, 1});
        //TestNotMatch<FoldBitcastLeakyBitcast>(bc2, passOptions);
    }

    [Fact]
    public void TestBitcastLeakyBitcastMotion()
    {
        var passOptions = GetPassOptions();
        var input_a = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var input_b = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var r = IR.F.Math.Binary(BinaryOp.Add, input_a, input_b);
        TestNotMatch<BitcastLeakyBitcastMotion>(r, passOptions);
    }

    [Fact]
    public void TestBitcastLeakyPadBitcastMotion()
    {
        var passOptions = GetPassOptions();
        var input_a = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var input_b = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var r = IR.F.Math.Binary(BinaryOp.Add, input_a, input_b);
        TestNotMatch<BitcastLeakyPadBitcastMotion>(r, passOptions);
    }

    [Fact]
    public void TestBitcastPadBitcastMotion()
    {
        var passOptions = GetPassOptions();
        var input_a = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var input_b = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var r = IR.F.Math.Binary(BinaryOp.Add, input_a, input_b);
        TestNotMatch<BitcastPadBitcastMotion>(r, passOptions);
    }

    [Fact]
    public void TestInnodePadBitcastMotion()
    {
        var passOptions = GetPassOptions();
        var input_a = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var input_b = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var r = IR.F.Math.Binary(BinaryOp.Add, input_a, input_b);
        TestNotMatch<InnodePadBitcastMotion>(r, passOptions);
    }

    [Fact]
    public void TestFoldTransposeBitcast()
    {
        var passOptions = GetPassOptions();
        var input_a = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var input_b = Normal(DataTypes.Float32, new[] {1, 1, 1, 1});
        var r = IR.F.Math.Binary(BinaryOp.Add, input_a, input_b);
        TestNotMatch<FoldTransposeBitcast>(r, passOptions);
    }

    [Fact]
    public void TestReshapeFullyConnected()
    {
        var ic = 3;
        var oc = 1;
        var input = DefaultRandom(DataTypes.UInt8, new[] {1, ic, 1, 1});
        var weights = DefaultRandom(DataTypes.UInt8, new[] {oc, ic, 1, 1});
        var weight_bias =
            Tensor<byte>.From(Enumerable.Range(0, oc).Select(i => new[] {(byte)0}).SelectMany(i => i).ToArray(),
                new[] {1, 1, 1, oc});
        var bias = DefaultRandom(DataTypes.Float32, new[] {oc}).Evaluate();
        var stride = Tensor.From<int>(new[] {1, 1}, new[] {2});
        var dilation = Tensor.From<int>(new[] {1, 1}, new[] {2});
        var padding = new[,] {{0, 0}, {0, 0}};
        var act = Tensor<Half>.From(
            Enumerable.Range(0, oc).Select(i => new[]
            {
                (Half)0.00390625, (Half)0.00390625, (Half)0, (Half)0, Half.NegativeInfinity, Half.PositiveInfinity,
                (Half)0
            }).SelectMany(i => i).ToArray(), new[] {1, 1, oc, 7});
        var conv = GNNEConv2D(DataTypes.Float16, GNNELoad(DataTypes.UInt8, input), GNNELoadW(DataTypes.UInt8, weights),
            GNNELoadW(DataTypes.UInt8, weights), GNNELoadW(DataTypes.UInt8, weight_bias),
            GNNELoadW(DataTypes.UInt8, weight_bias), GNNELoadW(DataTypes.Float16, act),
            GNNELoadW(DataTypes.Float16, act), 10, 10, 10,
            Tensor<DeQuantizeParam>.FromScalar(new DeQuantizeParam(0, 255)), padding, stride, dilation, 1,
            false, 0, null, null);
        conv.InferenceType();
        TestNotMatch<ReshapeFullyConnected>(conv);

        var notZeroPadding = ReplaceParams(conv, (IR.K230.GNNEConv2D.Padding, new[,] {{0, 0}, {0, 1}}));
        TestNotMatch<ReshapeFullyConnected>(notZeroPadding);
        var not11If = GNNELoad(new Float16Type(),
            DefaultRandom(DataTypes.Int8, new[] {1, ic, 2, 2}));
        var not11InputConv = ReplaceParams(conv, (IR.K230.GNNEConv2D.Input, not11If));
        var ty = not11InputConv.CheckedType;
        TestNotMatch<ReshapeFullyConnected>(not11InputConv);
        var not11Weights = GNNELoadW(new Float16Type(),
            DefaultRandom(DataTypes.Float32, new[] {oc, ic, 2, 2}));
        var not11WeightsConv = ReplaceParams(conv, (IR.K230.GNNEConv2D.Weights, not11Weights));
        TestNotMatch<ReshapeFullyConnected>(not11WeightsConv);
    }
#endif

    [Fact]
    public void TestReplaceInt16QuantWithQint8Quant()
    {
        var r = GnneConv2D();
        TestNotMatch<ReplaceInt16QuantWithQint8Quant>(r);
    }

    [Fact]
    public void TestReshape1x1Conv()
    {
        var r = GnneConv2D();
        TestNotMatch<Reshape1x1Conv>(r);
    }

    [Fact]
    public void TestResizeToFakeActivation()
    {
        var i = DefaultRandom(DataTypes.Float32, new long[] { 1, 3, 8, 15 });
        var validR = IR.F.Imaging.ResizeImage(
            ImageResizeMode.NearestNeighbor,
            ImageResizeTransformationMode.AlignCorners,
            ImageResizeNearestMode.RoundPreferCeil,
            i,
            None.Default,
            new[] { 1, 3, 16, 30 },
            None.Default,
            None.Default,
            None.Default);
        TestMatched<ResizeToFakeActivation>(validR);
    }

    [Fact]
    public void TestFiveDimWithOneBatchTranspose()
    {
        var input = Normal(DataTypes.UInt8, new Shape(1, 2, 4, 8, 1));
        var r = IR.F.Tensors.Transpose(input, new[] { 1, 1, 0, 2, 1 });
        TestNotMatch<FiveDimWithOneBatchTranspose>(r);
    }

    [Fact]
    public void TestReduceToGlobalReduceWindowNotMatch()
    {
        var axisNotSatisfied = Reduce(ReduceOp.Max, DefaultRandom(), new[] { 0 }, 1, false);
        TestNotMatch<ReduceToGlobalReduceWindow>(axisNotSatisfied);

        var shapeTooBig = Reduce(ReduceOp.Max, Normal(new Shape(1, 3, 224, 224)), new[] { 0 }, 1, false);
        TestNotMatch<ReduceToGlobalReduceWindow>(shapeTooBig);

        var meanNeedSmallSize = Reduce(ReduceOp.Mean, Normal(new Shape(1, 3, 14, 12)), new[] { 0 }, 1, false);
        TestNotMatch<ReduceToGlobalReduceWindow>(meanNeedSmallSize);
    }

    [Fact]
    public void TestReduceToGlobalReduceWindow()
    {
        var r = RangeOfMarker(Reduce(ReduceOp.Max, RangeOfMarker(Normal(new[] { 1, 1, 1, 1 }), new[] { 0f, 1f }), new[] { 2, 3 }, 1f, 1), new[] { 0f, 1f });
        TestMatched<ReduceToGlobalReduceWindow>(r);
    }

    [Fact]
    public void TestExpandShapeOfGlobalReduceWindow()
    {
        var r = RangeOfMarker(Reduce(ReduceOp.Max, RangeOfMarker(Normal(new[] { 1, 16, 7, 7 }), new[] { 0f, 1f }), new[] { 2, 3 }, 0, false), new[] { 0f, 1f });
        TestMatched<ExpandShapeOfGlobalReduceWindow>(r);
    }

    [Fact]
    public void TestReduceWithDim1ToGlobalReduceWindow()
    {
        var r = RangeOfMarker(Reduce(ReduceOp.Max, RangeOfMarker(Normal(new[] { 1, 3, 16, 16 }), new[] { 0f, 1f }), new[] { 1 }, 0, true), new[] { 0f, 1f });
        TestMatched<ReduceWithDim1ToGlobalReduceWindow>(r);
    }

    [Fact]
    public void TestReduceWithDim12ToGlobalReduceWindow()
    {
        var r = RangeOfMarker(Reduce(ReduceOp.Max, RangeOfMarker(Normal(new[] { 1, 1, 1, 16 }), new[] { 0f, 1f }), new[] { 1, 2 }, 0, true), new[] { 0f, 1f });
        TestMatched<ReduceWithDim12ToGlobalReduceWindow>(r);
    }

    [Fact]
    public void TestGNNEConvEqual()
    {
        var actParam = new ActParam2(16);
        var actParam2 = new ActParam2(16);

        Assert.True(actParam.Equals(actParam2));

        var conv = new GNNEConv2D(actParam, actParam, DataTypes.Float32);
        var conv2 = conv.With(actParam: actParam2, actParamQInt8: actParam2);

        Assert.True(conv.Equals(conv2));
        Assert.Equal(conv.GetHashCode(), conv2.GetHashCode());
    }

    private async Task TestFuseQuantIntoConv2()
    {
        var input = new Var(new TensorType(DataTypes.Float16, new[] { 1, 3, 16, 16 }));
        Fusion fusion;
        {
            // IR.K230.F.Tensors.GNNELoad
            var v0 = GNNEStore(
                DataTypes.Float32,
                GNNETranspose(GNNELoad(DataTypes.Float16, input), MFU_TRANS_PERMUTE.NWCH));

            Expr v1;
            {
                var convInput = GNNELoad(
                    DataTypes.UInt8,
                    IR.F.Math.Quantize(v0, new QuantParam(0, 1), DataTypes.UInt8));
                var convW = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(16, 3, 3, 3));
                var convWBias = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(1, 1, 1, 16));
                var convWBiasQ8 = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(1, 1, 1, 16));
                var convAct = GNNELoadW(DataTypes.Float16, Testing.Rand<Half>(1, 1, 16, 7));
                var convActQ8 = GNNELoadW(DataTypes.Float16, Testing.Rand<Half>(1, 1, 16, 7));
                var convWQ8 = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(432));

                v1 = GNNEConv2D(
                    DataTypes.Float16,
                    convInput,
                    convW,
                    convWQ8,
                    convWBias,
                    convWBiasQ8,
                    convAct,
                    convActQ8,
                    1,
                    1,
                    1,
                    1,
                    new[,] { { 1, 1 }, { 1, 1 } },
                    new[] { 1, 1 },
                    new[] { 1, 1 },
                    1,
                    0,
                    (Half)0.0f,
                    new(16),
                    new(16));
            }

            Expr v2;
            {
                var convInput = GNNELoad(
                    DataTypes.UInt8,
                    IR.F.Math.Quantize(v0, new QuantParam(0, 1), DataTypes.UInt8));
                var convW = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(16, 1, 3, 3));
                var convWBias = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(1, 1, 1, 16));
                var convWBiasQ8 = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(1, 1, 1, 16));
                var convAct = GNNELoadW(DataTypes.Float16, Testing.Rand<Half>(1, 1, 16, 7));
                var convActQ8 = GNNELoadW(DataTypes.Float16, Testing.Rand<Half>(1, 1, 16, 7));
                var convWQ8 = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(432));

                v2 = GNNEConv2D(DataTypes.Float16, convInput, convW, convWQ8, convWBias, convWBiasQ8, convAct, convActQ8, 1, 1, 1, 1, new[,] { { 1, 1 }, { 1, 1 } }, new[] { 1, 1 }, new[] { 1, 1 }, 16, 0, (Half)0.0f, new(16), new(16));
            }

            fusion = new Fusion("main", Targets.K230Target.Kind, v2, new[] { input });
        }

        CompilerServices.InferenceType(fusion);
        var egraph = new EGraph(fusion);
        var pass = new EGraphRulesPass() { Name = "3_1_LowerFake" };
        pass.Add<FuseQuantIntoConv>();
        pass.Add<FuseQuantIntoPdp1>();
        await pass.RunAsync(egraph, new());
    }

    private Expr GnneConv2D(Expr input = null!)
    {
        input = input is null ? DefaultRandom(DataTypes.UInt8, new long[] { 1, 3, 16, 16 }) : input;
        var convInput = GNNELoad(DataTypes.UInt8, input);
        var convW = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(16, 3, 3, 3));
        var convWBias = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(1, 1, 1, 16));
        var convWBiasQ8 = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(1, 1, 1, 16));
        var convAct = GNNELoadW(DataTypes.Float16, Testing.Rand<Half>(1, 1, 16, 7));
        var convActQ8 = GNNELoadW(DataTypes.Float16, Testing.Rand<Half>(1, 1, 16, 7));
        var convWQ8 = GNNELoadW(DataTypes.UInt8, Testing.Rand<byte>(432));

        return GNNEConv2D(DataTypes.Float16, convInput, convW, convWQ8, convWBias, convWBiasQ8, convAct, convActQ8, 1, 1, 1, Tensor.FromScalar(new DeQuantizeParam(0, 1)), new[,] { { 1, 1 }, { 1, 1 } }, new[] { 1, 1 }, new[] { 1, 1 }, 1, false, (Half)0.0f, new(16), new(16));
    }
}
