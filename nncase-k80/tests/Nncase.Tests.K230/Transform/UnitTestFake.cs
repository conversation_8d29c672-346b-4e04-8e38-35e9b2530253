﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Linq;
using System.Threading.Tasks;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.K230;
using Nncase.Quantization;
using Nncase.Tests.K230.TargetTest;
using Xunit;
using Xunit.Abstractions;
using static Nncase.IR.F.NN;
using static Nncase.IR.F.Random;

namespace Nncase.Tests.K230.TransformTest;

/// <summary>
/// UnitTestFake.
/// </summary>
[TestFixture.AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestFake : TestClassBase
{
    private readonly ITestOutputHelper _testOutputHelper;

    public UnitTestFake(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;
        CompileOptions.DumpFlags = DumpFlags.ImportOps | DumpFlags.EGraphCost | DumpFlags.Compile | DumpFlags.PassIR |
                                   DumpFlags.Rewrite;
        DefaultTargetName = "k230";
    }

    public Expr TestMatched<T>(Expr pre)
        where T : IRewriteRule, new()
    {
        return TestMatchedCore(pre, new T());
    }

    public Expr TestMatchedCore(Expr pre, params IRewriteRule[] rules)
    {
        Assert.True(pre.InferenceType(), "TestInferFailed:" + pre.CheckedType);
        if (rules.Length == 0)
        {
            throw new InvalidOperationException("Rules should not be empty");
        }

        var preHashCode = pre.GetHashCode();
        var v1 = pre.Evaluate();
        var post = CompilerServices.Rewrite(pre, rules, new());
        Assert.NotEqual(preHashCode, post.GetHashCode());
        var v2 = post.Evaluate();

        Comparator.Compare(v1, v2);
        return post;
    }

    [Fact]
    public void TestToFakePdpReduce()
    {
        var pdpTest = new Pdp1Case();
        var cases = pdpTest.GetCaseParams();
        foreach (var c in cases)
        {
            var pdp = pdpTest.GetPdp1Call((Pdp1Params)c)[0];
            TestMatched<ToFakePdpReduce>(pdp);
        }
    }

    [Fact]
    public void TestToFakeConv2D()
    {
        var conv2dTest = new Conv2dCase();
        var cases = conv2dTest.GetCaseParams();
        foreach (var c in cases)
        {
            var conv = conv2dTest.GetConv2DCase((Conv2dParams)c)[0];
            TestMatched<ToFakeConv2D>(conv);
        }
    }

    [Fact]
    public void TestToFakeConv2DTranspose()
    {
        var conv2dTransposeTest = new Conv2dTransposeCase();
        var cases = conv2dTransposeTest.GetCaseParams();
        foreach (var c in cases)
        {
            var conv = conv2dTransposeTest.GetOriginalFunsion((Conv2dTransposeParams)c)[0];
            TestMatched<ToFakeConv2DTranspose>(conv);
        }
    }

    [Fact]
    public void TestToFakeAi2dResize()
    {
        var resizeTest = new Ai2DResizeCase();
        var cases = resizeTest.GetCaseParams();
        foreach (var c in cases)
        {
            var validR = resizeTest.GetOriginalFunsion((Ai2DResizeParams)c);
            TestMatched<ToFakeAi2dResize>(validR[0]);
            TestMatched<ToFakeAi2dResize>(validR[1]);
            TestMatched<ToFakeAi2dResize>(validR[2]);
            TestMatched<ToFakeAi2dResize>(validR[3]);
        }
    }

    [Fact]
    public void TestToFakeAi2dPad()
    {
        var padTest = new Ai2DPadCase();
        var cases = padTest.GetCaseParams();
        foreach (var c in cases)
        {
            var pad = padTest.GetOriginalFunsion((Ai2DPadParams)c);
            TestNotMatch<ToFakeAi2dPad>(pad[0]);
            TestMatched<ToFakeAi2dPad>(pad[1]);
            TestMatched<ToFakeAi2dPad>(pad[2]);
        }
    }

    [Fact]
    public void TestToFakeLSTM()
    {
        var lSTMTest = new LSTMCase();
        var cases = lSTMTest.GetCaseParams();
        foreach (var c in cases)
        {
            var tuple = lSTMTest.GetLSTMCall((FakeLSTMParams)c);
            TestMatched<ToFakeLSTM>(tuple);
        }
    }

    [Fact]
    public void TestToFakeMatmul()
    {
        var matmulTest = new MatMulCase();
        var cases = matmulTest.GetCaseParams();
        foreach (var c in cases)
        {
            var matmul = matmulTest.GetMatMulCall((MatMulParams)c)[0];
            TestMatched<ToFakeMatmul>(matmul);
        }
    }

    [Fact]
    public void TestToFakeElu()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<EluToFakeActivation>(act[4]);
        }
    }

    [Fact]
    public void TestToFakeGelu()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<GeluToFakeActivation>(act[6]);
        }
    }

#if false
    [Fact]
    public void TestToFakeAsin()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<ASinToFakeActivation>(act[1]);
        }
    }

    [Fact]
    public void TestToFakeAcos()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<ACosToFakeActivation>(act[2]);
        }
    }
#endif

    [Fact]
    public void TestToFakeSigmoid()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<SigmoidToFakeActivation>(act[7]);
        }
    }

    [Fact]
    public void TestToFakeHardSigmoid()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<HardSigmoidToFakeActivation>(act[8]);
        }
    }

    [Fact]
    public void TestToFakeSwish()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<SwishToFakeActivation>(act[9]);
        }
    }

    [Fact]
    public void TestToFakeHardSwish()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<HardSwishToFakeActivation>(act[10]);
        }
    }

    [Fact]
    public void TestToFakeTanh()
    {
        var actTest = new ActCase();
        var cases = actTest.GetCaseParams();
        foreach (var c in cases)
        {
            var act = actTest.GetActCall((ActCaseParams)c);
            TestMatched<TanhToFakeActivation>(act[3]);
        }
    }

    [Fact]
    public void TestToRestoreFakeConv2D()
    {
        var input = Uniform(DataTypes.Float32, 1, -1, 0, new[] { 1, 3, 24, 32 });
        var weights = Uniform(DataTypes.Float32, 1, -1, 0, new[] { 16, 3, 3, 3 });
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 0, 1 }, { 0, 0 } };
        var actParam = new ActParam2(16, new QuantParam(0, 1));

        var act = new Tensor<float>(actParam.GetAct0Data, new long[] { 16, 7 });
        var conv = IR.F.Math.RangeOfMarker(
            IR.K230.F.Tensors.FakeConv2D(
                IR.F.Math.RangeOfMarker(input, new[] { -1.0f, 1.0f }),
                IR.F.Math.RangeOfMarker(
                    weights.Evaluate().AsTensor(),
                    Tensor.From(
                        Enumerable.Repeat(new[] { -1.0f, 1.0f }, 16).SelectMany(x => x).ToArray(),
                        new long[] { 16, 2 })),
                act,
                padding,
                stride,
                dilation,
                1,
                0f,
                actParam),
            new[] { -1.0f, 1.0f });

        TestMatched<RestoreFakeConv2D>(conv);
    }

    [Fact]
    public async Task TestFoldTwoFakeActivations()
    {
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var add = IR.F.Math.Add(input, 1.5f);
        var neg = IR.F.Math.Neg(add);

        var output = neg;

        var module = new IRModule(new Function("main", output, new[] { input }));

        await DefalutPasses(input, module, DataTypes.UInt8, DataTypes.UInt8);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeActivation)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value.Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[3])
                    .Value.ToScalar<int>();
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(0, actParam[j, 0]);
                    Assert.Equal(-1, actParam[j, 1]);
                    Assert.Equal(-1, actParam[j, 2]);
                    Assert.Equal(-1.5f, actParam[j, 3]);
                    Assert.Equal(-1.5f, actParam[j, 4]);
                }

                break;
            }
        }
    }

    internal async Task DefalutPasses(Var input, IRModule module, DataType quantType, DataType wQuantType, bool useMixQuant = false, bool toGNNE = true)
    {
        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new SolidCalibrationDatasetProvider(new[] { input }),
            QuantType = quantType,
            WQuantType = wQuantType,
            BindQuantMethod = useMixQuant,
        };

        var pmgr = CompileSession.CreatePassManager("Passes");

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("2_TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ToFakeConv2DTranspose>();
            p.Add<ToFakeMatmul>();
            p.Add<ToFakeDynamicMatmul>();
            p.Add<ToFakeAi2dPad>();
            p.Add<ToFakeAi2dResize>();
            p.Add<ToFakeLSTM>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<RestoreFakeConv2D>();
            p.Add<RestoreFakeActivation>();
            p.Add<ReplaceFakeDynamicMatMulConstInBRangeToByChannel>();
            p.Add<BinaryToFakeActivation>();
            p.Add<UnaryToFakeActivation>();
            p.Add<ReluToFakeActivation>();
            p.Add<Relu6ToFakeActivation>();
            p.Add<LeakyReluToFakeActivation>();
            p.Add<PReluToFakeActivation>();
            p.Add<SquantFineTuneFakeConv2DWeights>();
        });
        pmgr.AddWithName<DataflowPass>("Fold").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldTwoFakeActivation>();
            p.Add<FoldFakeConv2DTransposeAndFakeActivation>();
            p.Add<FoldFakeMatMulAndFakeActivation>();
            p.Add<FoldFakeConv2DAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("2.5_BindQuantizeConfig");
        if (toGNNE)
        {
            // 3. after quant pass
            pmgr.AddWithName<DataflowPass>("3_AfterQuant").Configure(p =>
            {
                p.Add<ToGNNEConv2D>();
                p.Add<ToGNNEConv2DTranspose>();
                p.Add<ToGNNEMatMul>();
                p.Add<ToDynamicGNNEMatMul>();
                p.Add<ToGNNETranspose>();
                p.Add<ToGNNEPad>();
                p.Add<ToGNNELSTM>();
                p.Add<ToGNNEPdpReduce>();
                p.Add<ToGNNEActivation>();
                p.Add<ToAi2dPad>();
                p.Add<ToAi2dResize>();
                p.Add<Passes.Rules.Neutral.FoldConstCall>();
            });
        }

        // create sub dumper scope and run pass
        using (var i = new DumpScope("Dump")) // note the subDumpFlags is a subset of parent DumpFlags
        {
            await pmgr.RunAsync(module);

            // note in the subDumpScope need using `DumpScope.Current` get the current dumper.
            DumpScope.Current.DumpIR(module.Entry!, "post");
        }
    }

    internal void TestNotMatch(Expr pre, params IRewriteRule[] rules)
    {
        pre.InferenceType();
        var post = CompilerServices.Rewrite(pre, rules, new());
        Assert.Equal(pre, post);
    }

    internal void TestNotMatch<T>(Expr pre)
        where T : IRewriteRule, new()
    {
        TestNotMatch(pre, new T());
    }

    [Fact]
    private async Task TestFoldFakeConv2DAndFakeActivations()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var weights = Normal(DataTypes.Float32, new[] { 16, 3, 3, 3 }).Evaluate();
        var bias = Normal(DataTypes.Float32, new[] { 16 }).Evaluate();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 0, 1 }, { 0, 0 } };

        var conv = Conv2D(input, weights.AsTensor(), bias.AsTensor(), stride, padding, dilation, PadMode.Constant, 1);
        var neg = IR.F.Math.Neg(conv);

        var output = neg;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        pmgr.Add<Passes.Transforms.ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,

            // UseSquant = true,
            UseAdaRound = true,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<NegToFakeActivation>();
        });
        pmgr.AddWithName<DataflowPass>("FoldFakeConvAndAct").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldFakeConv2DAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeConv2D)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value
                    .Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .CheckedShape[0].FixedValue;
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(-1, actParam[j, 0]);
                    Assert.Equal(-1, actParam[j, 1]);
                    Assert.Equal(-(float)bias.AsTensor()[j], actParam[j, 2]);
                    Assert.Equal(-(float)bias.AsTensor()[j], actParam[j, 3]);
                    Assert.Equal(0, actParam[j, 6]);
                }

                break;
            }
        }
    }

    [Fact]
    private async Task TestFoldFakeConv2DTransposeAndFakeActivations()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 2, 12, 12 }));
        var weights = Normal(DataTypes.Float32, 0, 1, 0, new[] { 2, 2, 3, 3 }).Evaluate();
        var bias = Normal(DataTypes.Float32, 0, 1, 0, new[] { 2 }).Evaluate();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 1, 1 }, { 1, 1 } };
        var outShape = new[] { 1, 2, 12, 12 };
        var conv2dtranspose = Conv2DTranspose(input, weights.AsTensor(), bias.AsTensor(), outShape, stride, padding, new[] { 0, 0, 0, 0 }, dilation, PadMode.Constant, 1);

        var neg = IR.F.Math.Neg(conv2dtranspose);

        var output = neg;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        pmgr.Add<Passes.Transforms.ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,

            // UseSquant = true,
            UseAdaRound = true,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2DTranspose>();
            p.Add<NegToFakeActivation>();
        });
        pmgr.AddWithName<DataflowPass>("FoldFakeConv2DTransposeAndFakeActivation").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldFakeConv2DTransposeAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeConv2DTranspose)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value
                    .Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .CheckedShape[0].FixedValue;
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(-1, actParam[j, 0]);
                    Assert.Equal(-1, actParam[j, 1]);
                    Assert.Equal(-(float)bias.AsTensor()[j], actParam[j, 2]);
                    Assert.Equal(-(float)bias.AsTensor()[j], actParam[j, 3]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 4]));
                    Assert.True(double.IsPositiveInfinity(actParam[j, 5]));
                    Assert.Equal(0, actParam[j, 6]);
                }

                break;
            }
        }
    }

    // [Fact]
    private async Task TestFoldFakeMatMulAndFakeActivations()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        var m = 3;
        var k = 4;
        var n = 2;
        Var input_a = new Var("input_a", new TensorType(DataTypes.Float32, new[] { 1, 1, m, k }));
        var input_b = Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 1, k, n }).Evaluate().AsTensor();
        var matmul = IR.F.Math.MatMul(input_a, input_b);

        var neg = IR.F.Math.Neg(matmul);

        var output = neg;

        var module = new IRModule(new Function("main", output, new[] { input_a }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        pmgr.Add<Passes.Transforms.ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input_a }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeMatmul>();
            p.Add<NegToFakeActivation>();
        });
        pmgr.AddWithName<DataflowPass>("FoldFakeMatmulAndFakeAct").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldFakeMatMulAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        Console.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(1, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeMatMul)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value
                    .Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .CheckedShape[0].FixedValue;
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(-1, actParam[j, 0]);
                    Assert.Equal(-1, actParam[j, 1]);
                    Assert.Equal(0, actParam[j, 2]);
                    Assert.Equal(0, actParam[j, 3]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 4]));
                    Assert.True(double.IsPositiveInfinity(actParam[j, 5]));
                    Assert.Equal(0, actParam[j, 6]);
                }

                break;
            }
        }
    }

    [Fact]
    private async Task TestReluToFakeActivation()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var relu = IR.F.NN.Relu(input);

        var output = relu;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        pmgr.Add<Passes.Transforms.ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ReluToFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(1, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeActivation)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value.Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[3])
                    .Value.ToScalar<int>();
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(0, actParam[j, 0]);
                    Assert.Equal(0, actParam[j, 1]);
                    Assert.Equal(1, actParam[j, 2]);
                    Assert.Equal(0, actParam[j, 3]);
                    Assert.Equal(0, actParam[j, 4]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 5]));
                    Assert.True(double.IsPositiveInfinity(actParam[j, 6]));
                }

                break;
            }
        }
    }

    [Fact]
    private async Task TestRelu6ToFakeActivation()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var relu6 = IR.F.NN.Relu6(input);

        var output = relu6;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<Relu6ToFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(1, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeActivation)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value.Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[3])
                    .Value.ToScalar<int>();
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(0, actParam[j, 0]);
                    Assert.Equal(0, actParam[j, 1]);
                    Assert.Equal(1, actParam[j, 2]);
                    Assert.Equal(0, actParam[j, 3]);
                    Assert.Equal(0, actParam[j, 4]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 5]));
                    Assert.Equal(6, actParam[j, 6]);
                }

                break;
            }
        }
    }

    [Fact]
    private async Task TestLeakyReluToFakeActivation()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);

        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var leakyrelu = IR.F.NN.LeakyRelu(input, 0.1f);

        var output = leakyrelu;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        pmgr.Add<Passes.Transforms.ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<Relu6ToFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(0, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeActivation)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value.Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[3])
                    .Value.ToScalar<int>();
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(0, actParam[j, 0]);
                    Assert.Equal(0.1f, actParam[j, 1]);
                    Assert.Equal(1, actParam[j, 2]);
                    Assert.Equal(0, actParam[j, 3]);
                    Assert.Equal(0, actParam[j, 4]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 5]));
                    Assert.True(double.IsPositiveInfinity(actParam[j, 6]));
                }

                break;
            }
        }
    }

    [Fact]
    private async Task TestPReluToFakeActivation()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var prelu = IR.F.NN.PRelu(input, 0.1f);

        var output = prelu;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<PReluToFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(1, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeActivation)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value.Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[3])
                    .Value.ToScalar<int>();
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(0, actParam[j, 0]);
                    Assert.Equal(0.1f, actParam[j, 1]);
                    Assert.Equal(1, actParam[j, 2]);
                    Assert.Equal(0, actParam[j, 3]);
                    Assert.Equal(0, actParam[j, 4]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 5]));
                    Assert.True(double.IsPositiveInfinity(actParam[j, 6]));
                }

                break;
            }
        }
    }

    [Fact]
    private async Task TestLeakyReluAndFakeActivationFoldOpt()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        // Transform.RunPassOptions passOptions = new(compileOptions);
        // var target = CompilerServices.GetTarget(compileOptions.Target);
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 24, 32 }));
        var neg = IR.F.Math.Neg(input);
        var leakyrelu = IR.F.NN.LeakyRelu(neg, 0.1f);

        var output = leakyrelu;

        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");
        pmgr.Add<Passes.Transforms.ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<NegToFakeActivation>();
            p.Add<LeakyReluToFakeActivation>();
        });
        pmgr.AddWithName<DataflowPass>("FoldTwoFakeActivation").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldTwoFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        await pmgr.RunAsync(module);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new UnitTestK230Target.DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);

        Assert.Equal(1, dumpVisitor.FoundOpCount<FakeActivation>());
        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is FakeActivation)
            {
                var actParam = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2])
                    .Value.Cast<float>();
                var channels = ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[3])
                    .Value.ToScalar<int>();
                for (int j = 0; j < channels; j++)
                {
                    Assert.Equal(0, actParam[j, 0]);
                    Assert.Equal(-1, actParam[j, 1]);
                    Assert.Equal(-0.1f, actParam[j, 2]);
                    Assert.Equal(0, actParam[j, 3]);
                    Assert.Equal(0, actParam[j, 4]);
                    Assert.True(double.IsNegativeInfinity(actParam[j, 5]));
                    Assert.True(double.IsPositiveInfinity(actParam[j, 6]));
                }

                break;
            }
        }
    }

    public sealed class DumpVisitor : ExprVisitor<int, IRType>
    {
        public int FoundOpCount<T>()
            where T : Op
        {
            return ExprMemo.Keys.OfType<T>().Count();
        }

        protected override int DefaultVisitLeaf(Expr expr) => 0;
    }
}
