﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.Passes;
using Nncase.Passes.Rules.K230;
using Nncase.Passes.Rules.Neutral;
using Nncase.Quantization;
using Nncase.Tests.K230.TargetTest;
using Nncase.Tests.TestFixture;
using Nncase.Utilities;
using Xunit;

namespace Nncase.Tests.K230.TransformTest;

public sealed class UnitTestQuantAlgorithm : TestClassBase
{
    public UnitTestQuantAlgorithm()
    {
        CompileOptions.DumpFlags = DumpFlags.ImportOps | DumpFlags.EGraphCost | DumpFlags.Compile | DumpFlags.PassIR | DumpFlags.Rewrite;
        DefaultTargetName = "k230";
        CompileOptions.DumpDir = System.IO.Path.Join(CompileOptions.DumpDir, DefaultTargetName);
    }

    [Fact]
    [AutoSetupTestMethod(InitSession = true)]
    public async Task TestConv2DSQuant()
    {
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 5, 5 }));
        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new SolidCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            QuantType = DataTypes.UInt8,
            WQuantType = DataTypes.UInt8,
            BindQuantMethod = false,
            UseSquant = true,
        };

        List<float> quantedWExpected = new List<float> { 0, 9, 19, 28, 38, 47, 57, 66, 76, 85, 94,
                                                                                        104, 113, 123, 132, 142, 151, 161, 170, 179, 189, 198, 208,
                                                                                        217, 227, 236, 246, 0, 10, 20, 29, 39, 49, 59,
                                                                                        69, 78, 88, 98, 108, 118, 127, 137, 147, 157, 167, 177,
                                                                                        186, 196, 206, 216, 226, 235, 245, 255, };

        await TestBase(input, quantedWExpected);
    }

    [Fact(Skip = "QuantBug")]
    [AutoSetupTestMethod(InitSession = true)]
    public async Task TestConv2DAdaRound()
    {
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 3, 5, 5 }));
        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new SolidCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            QuantType = DataTypes.UInt8,
            WQuantType = DataTypes.UInt8,
            BindQuantMethod = false,
            UseAdaRound = true,
        };

        List<float> quantedWExpected = new List<float> { 0, 9, 19, 28, 37, 48, 57, 67, 76, 85, 94,
                                                                                        104, 113, 122, 133, 141, 152, 161, 170, 179, 189, 198, 208,
                                                                                        218, 226, 237, 245, 0, 9, 20, 29, 39, 49, 59,
                                                                                        69, 79, 88, 98, 108, 117, 128, 137, 147, 157, 167, 176,
                                                                                        186, 196, 206, 216, 225, 235, 246, 255, };

        await TestBase(input, quantedWExpected);
    }

    private async Task TestBase(Var input, List<float> quantedWExpected)
    {
        var weightsValue = new List<float>();
        for (int i = 0; i < 2 * 3 * 3 * 3; i++)
        {
            weightsValue.Add(((i * 1.0f / (2 * 3 * 3 * 3)) - 0.5f) * 2);
        }

        var biasValue = new List<float>();
        for (int i = 0; i < 2; i++)
        {
            biasValue.Add(((i * 1.0f / 2) - 0.5f) * 2);
        }

        var weights = Tensor.From(weightsValue.ToArray(), new long[] { 2, 3, 3, 3 });
        var bias = Tensor.From(biasValue.ToArray(), new long[] { 2 });

        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[] { new[] { 0, 1 }, new[] { 0, 0 } };

        var conv = NN.Conv2D(input, weights, bias, stride, Pad(padding), dilation, PadMode.Constant, 1);

        var output = conv;
        var module = new IRModule(new Function("main", output, input));

        var eachChannelSize = (weights.Shape.Size / weights.Shape[0]).FixedValue;

        // w range
        List<ValueRange<float>> wRanges = new();
        var tmpMin = float.MaxValue;
        var tmpMax = float.MinValue;
        for (int i = 0; i < weights.Shape.Size; i++)
        {
            if (i % eachChannelSize == 0)
            {
                tmpMin = float.MaxValue;
                tmpMax = float.MinValue;
            }

            if (weights.ToArray<float>()[i] > tmpMax)
            {
                tmpMax = weights.ToArray<float>()[i];
            }

            if (weights.ToArray<float>()[i] < tmpMin)
            {
                tmpMin = weights.ToArray<float>()[i];
            }

            if ((i + 1) % (weights.Shape[1].FixedValue * weights.Shape[2].FixedValue * weights.Shape[3].FixedValue) == 0)
            {
                wRanges.Add(new ValueRange<float>(tmpMin, tmpMax));
            }
        }

        List<QuantParam> wqpExpected = new();
        var quantModeW = QuantMode.UnsignedMode;
        for (int i = 0; i < wRanges.Count; i++)
        {
            wqpExpected.Add(QuantUtility.GetQuantParam(new ValueRange<float>(wRanges[i].Min, wRanges[i].Max), 8, quantModeW));
        }

        Assert.Equal(0.003921569F, wqpExpected[0].Scale);
        Assert.Equal(255, wqpExpected[0].ZeroPoint);
        Assert.Equal(0.0037763254F, wqpExpected[1].Scale);
        Assert.Equal(0, wqpExpected[1].ZeroPoint);

        var pmgr = CompileSession.CreatePassManager("Passes");

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("2_TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<SquantFineTuneFakeConv2DWeights>();
        });

        // 2.5. AdaRoundWeights or BindQuantizeConfig
        pmgr.AddWithName<EGraphPassWithAdaRound>("2.5_AdaRoundWeights");
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("2.5_BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("3_AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNETranspose>();
            p.Add<FoldConstCall>();
        });

        // await pmgr.RunAsync(module);
        // create sub dumper scope and run pass
        using (var i = new DumpScope("Dump")) // note the subDumpFlags is a subset of parent DumpFlags
        {
            await pmgr.RunAsync(module);

            // note in the subDumpScope need using `DumpScope.Current` get the current dumper.
            DumpScope.Current.DumpIR(module.Entry!, "post");
        }

        var samples = await CompileOptions.QuantizeOptions.CalibrationDataset!.Samples.ToListAsync();

        var rangeMin = float.MaxValue;
        var rangeMax = float.MinValue;

        // if range
        foreach (var sample in samples)
        {
            var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
            var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
            if (sampleMin < rangeMin)
            {
                rangeMin = sampleMin;
            }

            if (sampleMax > rangeMax)
            {
                rangeMax = sampleMax;
            }
        }

        var qpExpected = new QuantParam(129, 0.0077385623F); // Utilities.QuantUtility.GetQuantParam(new ValueRange<float> (rangeMin, rangeMax), 8, compileOptions.QuantMode);

        Console.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);
        Assert.Equal(1, dumpVisitor.FoundOpCount<Quantize>());

        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is Quantize)
            {
                // if quant param
                Assert.Equal(((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Value.ToArray<QuantParam>()[0].Scale, qpExpected.Scale);
                Assert.Equal(((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Value.ToArray<QuantParam>()[0].ZeroPoint, qpExpected.ZeroPoint);
            }

            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call && ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is GNNEConv2D)
            {
                // quanted w
                for (int j = 0; j < ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Value.ToArray<int>().Length; j++)
                {
                    Assert.Equal(quantedWExpected[j], ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Value.ToArray<int>()[j]);
                }

                // weights bias
                Assert.Equal(255, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2]).Value.ToArray<int>()[0]);
                Assert.Equal(0, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2]).Value.ToArray<int>()[1]);

                // deq bias
                Assert.Equal(129, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[6]).Value.ToArray<int>()[0]);

                // act
                Assert.Equal((Half)3.034E-05, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[0]);
                Assert.Equal((Half)3.034E-05, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[1]);
                Assert.Equal((Half)(-1), ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[2]);
                Assert.Equal((Half)(-1), ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[3]);
                Assert.Equal((Half)float.NegativeInfinity, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[4]);
                Assert.Equal((Half)float.PositiveInfinity, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[5]);
                Assert.Equal((Half)0, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[6]);

                Assert.Equal((Half)2.92E-05, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[7]);
                Assert.Equal((Half)2.92E-05, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[8]);
                Assert.Equal((Half)0, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[9]);
                Assert.Equal((Half)0, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[10]);
                Assert.Equal((Half)float.NegativeInfinity, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[11]);
                Assert.Equal((Half)float.PositiveInfinity, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[12]);
                Assert.Equal((Half)0, ((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Value.ToArray<Half>()[13]);
            }
        }
    }

    private Expr Pad(int[][] p) => Const.FromTensor(Tensor.From(p.SelectMany(i => i).ToArray(), new long[] { 2, 2 }));

    public sealed class DumpVisitor : ExprVisitor<int, IRType>
    {
        public int FoundOpCount<T>()
            where T : Op
        {
            return ExprMemo.Keys.OfType<T>().Count();
        }

        protected override int DefaultVisitLeaf(Expr expr) => 0;
    }
}
