﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.Math;
using Nncase.IR.NN;
using Nncase.IR.Tensors;
using Nncase.Quantization;
using Nncase.Targets;
using Nncase.Tests.TestFixture;
using Xunit;
using Function = Nncase.IR.Function;
using Tuple = Nncase.IR.Tuple;

namespace Nncase.Tests.K230.TargetTest;

[AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestK230PipeLine : TestClassBase
{
    public UnitTestK230PipeLine()
    {
#if DEBUG
        CompileOptions.DumpFlags = DumpFlags.PassIR | DumpFlags.Rewrite;
#endif
        DefaultTargetName = K230Target.Kind;
    }

    [Fact]
    public async Task TestDCE()
    {
        Tensor GetD<T>(BinaryReader reader, long start, int size, params int[] shape)
            where T : unmanaged, IEquatable<T>
        {
            var buffer = new byte[size];
            Testing.RandGenerator.NextBytes(buffer);
            return Tensor.FromBytes<T>(buffer, shape.Select(x => (long)x).ToArray());
        }

        using var vD = new BinaryReader(new MemoryStream());
        Function v0; // (f32[1,34,25,45]) -> (f32[1,34,3,75])
        var v1 = new Var("model_input", new TensorType(DataTypes.Float32, new[] { 1, 34, 25, 45 }));
        {
            var v2 = new Call(new Pad(PadMode.Constant), v1, new[,] { { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 } }, 0.0f); // f32[1,34,25,45]
            var v3 = new Call(new Transpose(), v2, new[] { 0, 3, 1, 2 }); // f32[1,45,34,25]
            var v4 = new Call(new Conv2D(PadMode.Constant), v3, GetD<float>(vD, 0, 13500, 75, 45, 1, 1), GetD<float>(vD, 0, 300, 75), new[] { 1, 5 }, new[,] { { 0, 0 }, { 0, 0 } }, new[] { 1, 1 }, 1, new[] { -float.PositiveInfinity, float.PositiveInfinity }); // f32[1,75,34,5]
            var v5 = new Call(new Transpose(), v4, new[] { 0, 2, 3, 1 }); // f32[1,34,5,75]
            var v6 = new Call(new Pad(PadMode.Constant), v5, new[,] { { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 } }, 0.0f); // f32[1,34,5,75]
            var v7 = new Call(new Binary(BinaryOp.Mul), v6, -1.0f); // f32[1,34,5,75]
            var v8 = new Call(new Transpose(), v7, new[] { 0, 3, 1, 2 }); // f32[1,75,34,5]
            var v9 = new Call(new ReduceWindow2D(ReduceOp.Max), v8, -3.4028235E+38, new[] { 1, 1 }, new[] { 1, 2 }, new[,] { { 0, 0 }, { 0, 0 } }, new[] { 1L, 1L }, false, false); // f32[1,75,34,3]
            var v10 = new Call(new Transpose(), v9, new[] { 0, 2, 3, 1 }); // f32[1,34,3,75]
            var v11 = new Call(new Binary(BinaryOp.Mul), v10, -1.0f); // f32[1,34,3,75]
            var v12 = new Tuple(v11); // (f32[1,34,3,75])

            v0 = new Function("main", v12, v1);
        }

        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { v1 }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false, // true
        };
        var compiler = CompileSession.Compiler;
        var module = new IRModule(v0);
        compiler.ImportIRModule(module);
        await compiler.CompileAsync();
    }

    [Theory]
    [InlineData("/root/k510-gnne-compiler/k230-gnne-compiler-tests/benchmark-test/CRNN/crnn_model_32_320.onnx")]
    [InlineData("/home/<USER>/Work/Repo/rebuild-ir/k510-gnne-compiler/k230-gnne-compiler-tests/benchmark-test/CRNN/crnn_model_32_320.onnx")]
    public async Task TestEndlessIsDescendantOf(string model_path)
    {
        if (!File.Exists(model_path))
        {
            return;
        }

        var compiler = CompileSession.Compiler;

        IRModule module = await compiler.ImportOnnxModuleAsync(File.OpenRead(model_path));

        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new RandCalibrationDatasetProvider(((Function)module.Entry!).Parameters.ToArray()),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
        };
        compiler.ImportIRModule(module);
        await compiler.CompileAsync();
    }
}
