﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.K230;
using Nncase.Quantization;
using Nncase.Quantization.K230;
using Nncase.Tests.TestFixture;
using Xunit;

namespace Nncase.Tests.K230.TargetTest;

[AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestDynamicModel : TestClassBase
{
    public UnitTestDynamicModel()
    {
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;
        CompileOptions.DumpFlags = DumpFlags.ImportOps | DumpFlags.EGraphCost | DumpFlags.Compile | DumpFlags.PassIR | DumpFlags.Rewrite;
        DefaultTargetName = "k230";
    }

    /// <summary>
    /// TestLiteTransformerDecoder.
    /// </summary>
    [Fact]
    public void TestLiteTransformerDecoder()
    {
        var root = ModelRoot("decoder");
        var modelPath = Path.Combine(root, "lite_transformer_decoder_dynamic_axes.onnx");
        var datasetPath = Path.Combine(root, "decoder_input_output_example_2");
        var options = CompileOptions;
        TestDynamicModel(options, modelPath, datasetPath, vars => new LiteTransformerDecoderDatasetProvider(vars, datasetPath));
    }

    [Fact]
    public void TestLiteTransformerEncoder()
    {
        string root = ModelRoot("encoder");
        string modelPath = Path.Combine(root, "lite_transformer_encoder_dynamic_axes.onnx");
        string datasetPath = Path.Combine(root, "encoder_dynamic_test_data_20220718");
        var options = CompileOptions;
        TestDynamicModel(options, modelPath, datasetPath, vars => new LiteTransformerEncoderDatasetProvider(vars, datasetPath));
    }

    [Fact]
    public void TestFastspeech()
    {
        string root = Path.Combine("..", "..", "tmp", "fastspeech");
        string modelPath = Path.Combine(root, "fastspeech2dyn.tflite");
        string datasetPath = Path.Combine(root, "input_data");
        var options = CompileOptions;
        options.InputFormat = "tflite";
        TestDynamicModel(options, modelPath, datasetPath, vars => new FastspeechDatasetProvider(vars, datasetPath));
    }

    [Fact]
    public async Task TestDynamicMatmul()
    {
        var compileOptions = CompileOptions;
        var target = CompilerServices.GetTarget("k230");

        int m = 3;
        int k = 4;
        int n = 2;
        var inputA = new Var("input_a", new TensorType(DataTypes.Float32, new[] { Dimension.Unknown, Dimension.Unknown, m, k }));
        var inputB = new Var("input_b", new TensorType(DataTypes.Float32, new[] { Dimension.Unknown, Dimension.Unknown, k, n }));
        var main = new Function("main", IR.F.Math.MatMul(inputA, inputB), new[] { inputA, inputB });
        main.InferenceType();

        var module = new IRModule(main);

        ValueRange<float> range = new(0.0f, 10.0f);
        compileOptions.QuantizeOptions = new K230QuantizeOptions(DataTypes.UInt8, DataTypes.UInt8) { CalibrationDataset = new RangedCalibrationDatasetProvider(new[] { inputA, inputB }, range), CalibrationMethod = CalibMethod.Kld, BindQuantMethod = true };
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;

        await DefalutPasses(module);
    }

    [Fact]
    public async Task TestTwoLayersDynamicMatmul()
    {
        var compileOptions = CompileOptions;
        var target = CompilerServices.GetTarget("k230");

        var m = 3;
        var k = 4;
        var n = 2;
        var v = 5;
        var inputA = new Var("input_a", new TensorType(DataTypes.Float32, new[] { Dimension.Unknown, Dimension.Unknown, m, k }));
        var inputB = new Var("input_b", new TensorType(DataTypes.Float32, new[] { Dimension.Unknown, Dimension.Unknown, k, n }));
        var inputC = new Var("input_c", new TensorType(DataTypes.Float32, new[] { Dimension.Unknown, Dimension.Unknown, n, v }));
        var matmul1 = IR.F.Math.MatMul(inputA, inputB);
        var matmul2 = IR.F.Math.MatMul(matmul1, inputC);

        var main = new Function("main", matmul2, new[] { inputA, inputB, inputC });
        main.InferenceType();

        var module = new IRModule(main);

        // 1. Optimize target dependent
        ValueRange<float> range = new(0.0f, 10.0f);
        compileOptions.QuantizeOptions = new K230QuantizeOptions(DataTypes.UInt8, DataTypes.UInt8) { CalibrationDataset = new RangedCalibrationDatasetProvider(new[] { inputA, inputB, inputC }, range), CalibrationMethod = CalibMethod.Kld, BindQuantMethod = true };
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;

        await DefalutPasses(module);
    }

    private static IValue ReadTensor<T>(string path, int[] shape)
        where T : unmanaged, IEquatable<T>
    {
        byte[] bytes = File.ReadAllBytes(path);
        return Value.FromTensor(Tensor.FromBytes<T>(bytes, shape.Select(x => (long)x).ToArray()));
    }

    private async Task DefalutPasses(IRModule module, bool toGNNE = true)
    {
        var pmgr = CompileSession.CreatePassManager("Passes");

        // pmgr.Add<ShapeInferPass>();
        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("2_TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ToFakeConv2DTranspose>();

            // dynamic first
            p.Add<ToFakeDynamicMatmul>();
            p.Add<ToFakeMatmul>();
            p.Add<ToFakeAi2dPad>();
            p.Add<ToFakeAi2dResize>();
            p.Add<ToFakeLSTM>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<RestoreFakeConv2D>();
            p.Add<RestoreFakeActivation>();
            p.Add<ReplaceFakeDynamicMatMulConstInBRangeToByChannel>();
            p.Add<BinaryToFakeActivation>();
            p.Add<UnaryToFakeActivation>();
            p.Add<ReluToFakeActivation>();
            p.Add<Relu6ToFakeActivation>();
            p.Add<LeakyReluToFakeActivation>();
            p.Add<PReluToFakeActivation>();
            p.Add<SquantFineTuneFakeConv2DWeights>();
        });
        pmgr.AddWithName<DataflowPass>("Fold").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();

            p.Add<FoldTwoFakeActivation>();
            p.Add<FoldFakeConv2DTransposeAndFakeActivation>();
            p.Add<FoldFakeMatMulAndFakeActivation>();
            p.Add<FoldFakeConv2DAndFakeActivation>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("2.5_BindQuantizeConfig");
        if (toGNNE)
        {
            // 3. after quant pass
            pmgr.AddWithName<DataflowPass>("3_AfterQuant").Configure(p =>
            {
                p.Add<ToGNNEConv2D>();
                p.Add<ToGNNEConv2DTranspose>();

                // dynamic first
                p.Add<ToDynamicGNNEMatMul>();
                p.Add<ToGNNEMatMul>();
                p.Add<ToGNNETranspose>();
                p.Add<ToGNNEPad>();
                p.Add<ToGNNELSTM>();
                p.Add<ToGNNEPdpReduce>();
                p.Add<ToGNNEActivation>();
                p.Add<ToAi2dPad>();
                p.Add<ToAi2dResize>();
                p.Add<Passes.Rules.Neutral.FoldConstCall>();
            });
        }

        // create sub dumper scope and run pass
        using (var dumpScope = new DumpScope("Dump")) // note the subDumpFlags is a subset of parent DumpFlags
        {
            await pmgr.RunAsync(module);

            // note in the subDumpScope need using `DumpScope.Current` get the current dumper.
            DumpScope.Current.DumpIR(module.Entry!, "post");
        }
    }

    private async void TestDynamicModel(CompileOptions compileOptions, string onnxPath, string datasetPath, Func<Var[], ICalibrationDatasetProvider> datasetProviderCreator)
    {
        var target = CompilerServices.GetTarget("k230");
        if (!File.Exists(onnxPath))
        {
            return;
        }

        byte[] modelBytes = File.ReadAllBytes(onnxPath);
        await using var modelStream = File.OpenRead(onnxPath);
        BaseImporter importer;
        importer = new Importer.OnnxImporter(modelStream, CompileSession);
        var module = importer.Import();

        var calibrationDataset = datasetProviderCreator(((Function)module.Entry!).Parameters.ToArray());

        compileOptions.QuantizeOptions = new K230QuantizeOptions(DataTypes.UInt8, DataTypes.UInt8) { CalibrationDataset = calibrationDataset, BindQuantMethod = false };

        // var input_a = new Var("input_a", new TensorType(DataTypes.Float32, new Dimension[] { 1, 10, 256 }));
        // var input_b = new Var("input_b", new TensorType(DataTypes.Float32, new Dimension[] { 1, 1, 1, 10 }));
        // var input_c = new Var("input_c", new TensorType(DataTypes.Int64, new Dimension[] { 1, 10 }));
        // var input_d = new Var("input_d", new TensorType(DataTypes.Int64, new Dimension[] { 1 }));
        // ValueRange<float> range = new(0.0f, 10.0f);
        // compileOptions.QuantizeOptions = new K230QuantizeOptions(DataTypes.UInt8, DataTypes.UInt8) { CalibrationDataset = new RangedCalibrationDatasetProvider(new[] { input_a, input_b, input_c, input_d }, range), BindQuantMethod = false };
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;

        await DefalutPasses(module);
    }

    private string ModelRoot(string name) => Path.Combine("..", "..", "tmp", name);

    private class RangedCalibrationDatasetProvider : ICalibrationDatasetProvider
    {
        public RangedCalibrationDatasetProvider(IEnumerable<Var> vars, ValueRange<float> range)
        {
            var values = new Dictionary<Var, IValue>();
            foreach (var var in vars)
            {
                CompilerServices.InferenceType(var);
                int[] shape = var.CheckedShape.Select(d => d.IsUnknown ? 1 : (int)d.FixedValue).ToArray();
                var value = Value.FromTensor(Testing.ReArangeSeq(Testing.Seq<float>(shape.Select(x => (long)x).ToArray()), range));
                values.Add(var, value);
            }

            Samples = new[] { values }.ToAsyncEnumerable();
        }

        public int? Count => 5;

        public IAsyncEnumerable<IReadOnlyDictionary<Var, IValue>> Samples { get; }
    }

    private class LiteTransformerEncoderDatasetProvider : ICalibrationDatasetProvider
    {
        public LiteTransformerEncoderDatasetProvider(Var[] vars, string dataset_dir)
        {
            var srcSeq = vars[0];
            var srcMask = vars[1];

            // _seq_files = new[]{Directory.GetFiles(dataset_dir, "encoder_source_seq_len*.data").ToArray().OrderByDescending(x => x).ToArray()[4]};
            string[] seqFiles = Directory.GetFiles(dataset_dir, "encoder_source_seq_len*.data");
            Samples = seqFiles.Select(seq_file =>
            {
                string maskFile = seq_file.Replace("encoder_source_seq_len", "encoder_source_mask_len", StringComparison.CurrentCulture);
                int len = int.Parse(System.Text.RegularExpressions.Regex
                    .Match(maskFile, @"encoder_source_mask_len(\d+)\.data").Groups[1].Value);

                byte[] seqBytes = File.ReadAllBytes(seq_file);
                byte[] maskBytes = File.ReadAllBytes(maskFile);
                return new Dictionary<Var, IValue>()
                {
                    [srcSeq] = Value.FromTensor(Tensor.FromBytes<long>(seqBytes, new long[] { seqBytes.Length / 8 / len, len })),
                    [srcMask] = Value.FromTensor(Tensor.FromBytes<long>(maskBytes, new long[] { maskBytes.Length / 8 / len, len })),
                };
            }).ToAsyncEnumerable();
        }

        public int? Count => 5;

        public IAsyncEnumerable<IReadOnlyDictionary<Var, IValue>> Samples { get; }
    }

    private class LiteTransformerDecoderDatasetProvider : ICalibrationDatasetProvider
    {
        public LiteTransformerDecoderDatasetProvider(Var[] vars, string dataset_dir)
        {
            var encoderOutput1 = vars[0];
            var encAttnBias1 = vars[1];
            var tgtSeq1 = vars[2];
            var decodeRound1 = vars[3];
            string[] outFiles = Directory.GetFiles(dataset_dir, $"decode_step_*_decode_output.data");

            Samples = outFiles.Select((f, _) =>
            {
                string GetInput(int inputIndex)
                {
                    return f.Replace("decode_output.data", $"decoder_input_{inputIndex}.data", StringComparison.CurrentCulture);
                }

                byte[] o = File.ReadAllBytes(f);
                int batchSize = o.Length / sizeof(float) / 32000;

                byte[] encoderOutputBytes = File.ReadAllBytes(GetInput(0));
                int length1 = encoderOutputBytes.Length / sizeof(float) / batchSize / 256;
                var encoderOutput =
                    Value.FromTensor(Tensor.FromBytes<float>(encoderOutputBytes, new long[] { batchSize, length1, 256 }));

                var encAttnBias = ReadTensor<float>(GetInput(1), new[] { batchSize, 1, 1, length1 });

                byte[] tgtSeqBytes = File.ReadAllBytes(GetInput(2));
                int length2 = tgtSeqBytes.Length / sizeof(long) / batchSize;
                var tgtSeq = Value.FromTensor(Tensor.FromBytes<long>(tgtSeqBytes, new long[] { batchSize, length2 }));

                var decodeRound = ReadTensor<long>(GetInput(3), new[] { 1 });
                return new Dictionary<Var, IValue>()
                {
                    [encoderOutput1] = encoderOutput,
                    [encAttnBias1] = encAttnBias,
                    [tgtSeq1] = tgtSeq,
                    [decodeRound1] = decodeRound,
                };
            }).ToAsyncEnumerable();
        }

        public int? Count => 6;

        public IAsyncEnumerable<IReadOnlyDictionary<Var, IValue>> Samples { get; }
    }

    private class FastspeechDatasetProvider : ICalibrationDatasetProvider
    {
        public FastspeechDatasetProvider(Var[] vars, string dataset_dir)
        {
            var inputIds1 = vars[0];
            var speakerIds1 = vars[1];
            var speedRatios2 = vars[2];
            var f0Ratios3 = vars[3];
            var energyRatios4 = vars[4];

            // var files = Directory.GetFiles(dataset_dir, "input_*.bin")[0..1];
            string[] files = Directory.GetFiles(dataset_dir, "input_*.bin");
            Samples = files.Select(f =>
            {
                byte[] bytes = File.ReadAllBytes(f);
                var inputIds = Value.FromTensor(Tensor.FromBytes<int>(bytes, new long[] { 1, bytes.Length / sizeof(int) }));
                return new Dictionary<Var, IValue>()
                {
                    [inputIds1] = inputIds,
                    [speakerIds1] = Value.FromTensor(Tensor.FromArray(new[] { 0 })),
                    [speedRatios2] = Value.FromTensor(Tensor.FromArray(new[] { 1f })),
                    [f0Ratios3] = Value.FromTensor(Tensor.FromArray(new[] { 1f })),
                    [energyRatios4] = Value.FromTensor(Tensor.FromArray(new[] { 1f })),
                };
            }).ToAsyncEnumerable();
        }

        public int? Count => 5;

        // private string[] _seqFiles;
        public IAsyncEnumerable<IReadOnlyDictionary<Var, IValue>> Samples { get; }
    }
}
