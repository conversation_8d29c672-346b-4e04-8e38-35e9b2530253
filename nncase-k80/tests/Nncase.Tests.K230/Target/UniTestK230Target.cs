﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Nncase.Diagnostics;
using Nncase.Importer;
using Nncase.Importer.TFLite;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.IR.K230;
using Nncase.IR.Math;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules.K230;
using Nncase.Passes.Rules.Lower;
using Nncase.Passes.Rules.Neutral;
using Nncase.Passes.Rules.Tile;
using Nncase.Passes.Transforms;
using Nncase.Quantization;
using Nncase.Tests.TestFixture;
using Nncase.Utilities;
using Xunit;
using Xunit.Abstractions;
using FoldConstCall = Nncase.Passes.Rules.Neutral.FoldConstCall;
using Math = System.Math;
using Random = Nncase.IR.F.Random;

namespace Nncase.Tests.K230.TargetTest;

/// <inheritdoc />
[AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestK230Target : TestClassBase
{
    private readonly ITestOutputHelper _testOutputHelper;

    public UnitTestK230Target(ITestOutputHelper testOutputHelper)
    {
        _testOutputHelper = testOutputHelper;
        CompileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        CompileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;
        CompileOptions.DumpFlags = DumpFlags.ImportOps | DumpFlags.EGraphCost | DumpFlags.Compile | DumpFlags.PassIR |
                                   DumpFlags.Rewrite;

        DefaultTargetName = "k230";
    }

    [Fact]
    public async Task TestFakeConvLowering()
    {
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        compileOptions.QuantizeOptions.QuantType = DataTypes.UInt8;
        compileOptions.QuantizeOptions.WQuantType = DataTypes.UInt8;

        var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 24, 32, 3 }));

        var weights = Random.Normal(DataTypes.Float32, new[] { 16, 3, 3, 3 }).Evaluate().AsTensor();
        var bias = Random.Normal(DataTypes.Float32, new[] { 16 }).Evaluate().AsTensor();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        int[][] padding = { new[] { 0, 1 }, new[] { 0, 0 } };

        var conv = NN.Conv2D(Tensors.NHWCToNCHW(input), weights, bias, stride, Pad(padding), dilation, PadMode.Constant, 1);

        var output = Tensors.NCHWToNHWC(conv);
        var module = new IRModule(new Function("main", output, new[] { input }));

        var eachChannelSize = (weights.Shape.Size / weights.Shape[0]).FixedValue;

        // w range
        List<ValueRange<float>> wRanges = new();
        float tmpMin = float.MaxValue;
        float tmpMax = float.MinValue;
        for (int i = 0; i < weights.Shape.Size; i++)
        {
            if (i % eachChannelSize == 0)
            {
                tmpMin = float.MaxValue;
                tmpMax = float.MinValue;
            }

            if (weights.ToArray<float>()[i] > tmpMax)
            {
                tmpMax = weights.ToArray<float>()[i];
            }

            if (weights.ToArray<float>()[i] < tmpMin)
            {
                tmpMin = weights.ToArray<float>()[i];
            }

            if ((i + 1) % (weights.Shape[1].FixedValue * weights.Shape[2].FixedValue * weights.Shape[3].FixedValue) == 0)
            {
                wRanges.Add(new ValueRange<float>(tmpMin, tmpMax));
            }
        }

        List<QuantParam> wqpExpected = new();
        var quantModeW = CompileOptions.QuantizeOptions.WQuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedAsymmetricMode;
        for (int i = 0; i < wRanges.Count; i++)
        {
            wqpExpected.Add(QuantUtility.GetQuantParam(new ValueRange<float>(wRanges[i].Min, wRanges[i].Max), 8, quantModeW));
        }

        List<float> quantedWExpected = new();
        for (int i = 0; i < weights.Shape.Size; i++)
        {
            quantedWExpected.Add((float)Math.Round(
                (weights.ToArray<float>()[i] / (double)wqpExpected[i / (int)eachChannelSize].Scale) +
                wqpExpected[i / (int)eachChannelSize].ZeroPoint));
        }

        var pmgr = CompileSession.CreatePassManager("Passes");

        CompileOptions.QuantizeOptions.CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input });
        CompileOptions.QuantizeOptions.CalibrationMethod = CalibMethod.NoClip;
        CompileOptions.QuantizeOptions.BindQuantMethod = false;

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("2_TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
        });
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("2.5_BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("3_AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNETranspose>();

            // p.Add<Transform.Rules.Neutral.FoldConstCall>();
        });

        await pmgr.RunAsync(module);

        var samples = await compileOptions.QuantizeOptions.CalibrationDataset!.Samples.ToListAsync();
        var rangeMin = float.MaxValue;
        var rangeMax = float.MinValue;

        // if range
        foreach (var sample in samples)
        {
            var sampleMin = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Min();
            var sampleMax = sample.Values.ToArray()[0].AsTensor().ToArray<float>().Max();
            if (sampleMin < rangeMin)
            {
                rangeMin = sampleMin;
            }

            if (sampleMax > rangeMax)
            {
                rangeMax = sampleMax;
            }
        }

        var quantMode = CompileOptions.QuantizeOptions.QuantType == DataTypes.UInt8
            ? QuantMode.UnsignedMode
            : QuantMode.SignedSymmetricMode;
        var qpExpected = QuantUtility.GetQuantParam(new ValueRange<float>(rangeMin, rangeMax), 8, quantMode);

        _testOutputHelper.WriteLine(CompilerServices.Print((Function)module.Functions[0]));
        var dumpVisitor = new DumpVisitor();
        dumpVisitor.Visit(module.Functions[0]);
        Assert.Equal(1, dumpVisitor.FoundOpCount<Quantize>());

        for (int i = 0; i < dumpVisitor.ExprMemo.Keys.Count; i++)
        {
            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is Quantize)
            {
                // if quant param
                Assert.Equal(((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Value.ToArray<QuantParam>()[0].Scale, qpExpected.Scale);
                Assert.Equal(((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Value.ToArray<QuantParam>()[0].ZeroPoint, qpExpected.ZeroPoint);
            }

            if (dumpVisitor.ExprMemo.Keys.ToList()[i] is Call &&
                ((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Target is GNNEConv2D)
            {
                // w bias
                for (int j = 0; j < weights.Shape[0].FixedValue; j++)
                {
                    Assert.Equal(((TensorConst)((Call)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[2]).Arguments[0]).Value.ToArray<float>()[j], wqpExpected[j].ZeroPoint);
                }

                // quanted w
                for (int j = 0; j < weights.Shape.Size; j++)
                {
                    Assert.Equal(((TensorConst)((Call)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[1]).Arguments[0]).Value.ToArray<float>()[j], quantedWExpected[j]);
                }

                // if deq bias
                Assert.Equal(((TensorConst)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[6]).Value.ToScalar<float>(), qpExpected.ZeroPoint);

                // act
                var actParamArr = ((TensorConst)((Call)((Call)dumpVisitor.ExprMemo.Keys.ToList()[i]).Arguments[4]).Arguments[0]).Value.ToArray<Half>();
                for (int j = 0; j < actParamArr.Length; j++)
                {
                    // scale
                    if (j % 7 == 0 || (j - 1) % 7 == 0)
                    {
                        Assert.Equal(actParamArr[j], (Half)(wqpExpected[j / 7].Scale * qpExpected.Scale));
                    }

                    // bias
                    if ((j - 2) % 7 == 0 || (j - 3) % 7 == 0)
                    {
                        Assert.Equal(actParamArr[j], (Half)(float)bias[j / 7]);
                    }

                    // clamp
                    if ((j - 4) % 7 == 0 || (j - 5) % 7 == 0)
                    {
                        if ((j - 4) % 7 == 0)
                        {
                            bool isNegativeInfinity = float.IsNegativeInfinity((float)actParamArr[j]);
                        }

                        if ((j - 5) % 7 == 0)
                        {
                            bool isPositiveInfinity = float.IsPositiveInfinity((float)actParamArr[j]);
                        }
                    }

                    // x0
                    if ((j + 1) % 7 == 0)
                    {
                        Assert.Equal((Half)0, actParamArr[j]);
                    }
                }
            }
        }
    }

    [Fact]
    public async Task TestSimpleModel()
    {
        // Transform.RunPassContext passOptions = GetPassOptions();
        var compileOptions = CompileOptions;
        compileOptions.QuantizeOptions.ModelQuantMode = ModelQuantMode.UsePTQ;
        Var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 24, 32, 3 }));
        var weights = Random.Normal(DataTypes.Float32, new[] { 16, 3, 3, 3 }).Evaluate().AsTensor();
        var bias = Random.Normal(DataTypes.Float32, new[] { 16 }).Evaluate().AsTensor();
        var stride = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var dilation = Tensor.From(new[] { 1, 1 }, new long[] { 2 });
        var padding = new[,] { { 0, 1 }, { 0, 0 } };
        var conv = NN.Conv2D(Tensors.NHWCToNCHW(input), weights, bias, stride, padding, dilation, PadMode.Constant, 1);
        var output = Tensors.NCHWToNHWC(conv);
        var module = new IRModule(new Function("main", output, new[] { input }));

        var pmgr = CompileSession.CreatePassManager("Passes");

        CompileOptions.QuantizeOptions.CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input });

        pmgr.Add<ShapeInferPass>();

        compileOptions.QuantizeOptions = new()
        {
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = true,

            // UseSquant = true,
            UseAdaRound = false,
        };

        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<DisableConvDWPermitInt16Quant>();
            p.Add<DisableConvPdp0ReducePermitInt16Quant>();
            p.Add<SquantFineTuneFakeConv2DWeights>();
        });

        // 2.5. AdaRoundWeights
        pmgr.AddWithName<EGraphPassWithAdaRound>("AdaRoundWeights");

        // 2.5. BindQuantizeConfig
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
            {
                p.Add<ToGNNEConv2D>();
                p.Add<ToGNNETranspose>();
                p.Add<FuseQuantIntoConv>();
                p.Add<QuantToAct1>();
                p.Add<RemoveMarker>();
            });

        pmgr.AddWithName<DataflowPass>("Fusion").Configure(p =>
        {
            p.Add<ConvFusion>();
            p.Add<TransposeFusion>();
            p.Add<ActSIFFusion>();
            p.Add<ConvTransposeFusion>();
            p.Add<PadFusion>();
            p.Add<FoldNopReshape>();
        });

        // pmgr.Add(new K230FusionToTirPass("5_FusionToTirPass"));
        pmgr.AddWithName<K230FusionToTirPass>("ToTir");

        // Before CodeGen
        pmgr.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>(); // 常量折叠所有的指令参数.
        });

        // // dump ddr bandwidth.
        pmgr.AddWithName<DDrBufferSchdeulePass>("DDrBufferSchdeule");
        pmgr.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>(); // 折叠自定义op.
        });

        await pmgr.RunAsync(module);

        var (kmodel_path, kmodel) = Testing.BuildKModel("main", module, CompileSession);

        var input_tensor = new[]
        {
            Random.Normal(input.CheckedDataType, input.CheckedShape).Evaluate().AsTensor(),
        };
        Testing.DumpInterpModel(kmodel_path, input_tensor, Dumpper.Directory);

        // var result = Testing.RunKModel(kmodel, Dumpper.Directory, input_tensor);
    }

#if false
    // [Fact]
    public async Task TestMobileNetV1Async()
    {
        CompileOptions.InputFormat = "onnx";

        // step 1. import
        IRModule module;
        var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 224, 224, 3 }));

        // var input = new Var("input", new TensorType(DataTypes.Float32, new int[] { 1,3,224,224 }));
        {
            var v_0 = Tensors.Transpose(input, new[] { 0, 3, 1, 2 }); // f32[1,3,224,224]
            var v_1 = NN.Conv2D(
                v_0,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 32, 3, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 32 }).Evaluate().AsTensor(), new[] { 2, 2 },
                Pad(new[] { new[] { 0, 1 }, new[] { 0, 1 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,32,112,112]
            var v_2 = NN.Conv2D(
                v_1,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 32, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 32 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)32,
                new float[] { 0, 6 }); // f32[1,32,112,112]
            var v_3 = NN.Conv2D(
                v_2,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 64, 32, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 64 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,64,112,112]
            var v_4 = NN.Conv2D(
                v_3,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 64, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 64 }).Evaluate().AsTensor(), new[] { 2, 2 },
                Pad(new[] { new[] { 0, 1 }, new[] { 0, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)64,
                new float[] { 0, 6 }); // f32[1,64,56,56]
            var v_5 = NN.Conv2D(
                v_4,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128, 64, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,128,56,56]
            var v_6 = NN.Conv2D(
                v_5,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)128,
                new float[] { 0, 6 }); // f32[1,128,56,56]
            var v_7 = NN.Conv2D(
                v_6,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128, 128, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,128,56,56]
            var v_8 = NN.Conv2D(
                v_7,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 128 }).Evaluate().AsTensor(), new[] { 2, 2 },
                Pad(new[] { new[] { 0, 1 }, new[] { 0, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)128,
                new float[] { 0, 6 }); // f32[1,128,28,28]
            var v_9 = NN.Conv2D(v_8,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256, 128, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,256,28,28]
            var v_10 = NN.Conv2D(v_9,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)256,
                new float[] { 0, 6 }); // f32[1,256,28,28]
            var v_11 = NN.Conv2D(v_10,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256, 256, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,256,28,28]
            var v_12 = NN.Conv2D(v_11,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 256 }).Evaluate().AsTensor(), new[] { 2, 2 },
                Pad(new[] { new[] { 0, 1 }, new[] { 0, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)256,
                new float[] { 0, 6 }); // f32[1,256,14,14]
            var v_13 = NN.Conv2D(v_12,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 256, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_14 = NN.Conv2D(v_13,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)512,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_15 = NN.Conv2D(v_14,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 512, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_16 = NN.Conv2D(v_15,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)512,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_17 = NN.Conv2D(v_16,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 512, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_18 = NN.Conv2D(v_17,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)512,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_19 = NN.Conv2D(v_18,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 512, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_20 = NN.Conv2D(v_19,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)512,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_21 = NN.Conv2D(v_20,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 512, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_22 = NN.Conv2D(v_21,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)512,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_23 = NN.Conv2D(v_22,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 512, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,512,14,14]
            var v_24 = NN.Conv2D(v_23,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 512 }).Evaluate().AsTensor(), new[] { 2, 2 },
                Pad(new[] { new[] { 0, 1 }, new[] { 0, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)512,
                new float[] { 0, 6 }); // f32[1,512,7,7]
            var v_25 = NN.Conv2D(v_24,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1024, 512, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1024 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,1024,7,7]
            var v_26 = NN.Conv2D(v_25,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1024, 1, 3, 3 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1024 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, (long)1024,
                new float[] { 0, 6 }); // f32[1,1024,7,7]
            var v_27 = NN.Conv2D(v_26,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1024, 1024, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1024 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new float[] { 0, 6 }); // f32[1,1024,7,7]
            var v_28 = NN.ReduceWindow2D(ReduceOp.Mean, v_27, 0, new[] { 7, 7 }, new[] { 2, 2 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new long[] { 1, 1 }, false, false); // f32[1,1024,1,1]
            var v_29 = NN.Conv2D(v_28,
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1001, 1024, 1, 1 }).Evaluate().AsTensor(),
                Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1001 }).Evaluate().AsTensor(), new[] { 1, 1 },
                Pad(new[] { new[] { 0, 0 }, new[] { 0, 0 } }), new[] { 1, 1 }, PadMode.Constant, 1,
                new[] { float.MinValue, float.MaxValue }); // f32[1,1001,1,1]
            var v_30 = Tensors.Transpose(v_29, new[] { 0, 2, 3, 1 }); // f32[1,1,1,1001]
            var v_31 = Tensors.Reshape(v_30, new[] { -1, 1001 }); // f32[1,1001]
            var v_32 = NN.Softmax(v_31, -1); // f32[1,1001]
            var v_33 = (v_32); // (f32[1,1001])
            module = new IRModule(new Function("main", v_30, input));
        }

        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.Kld,
            UseSquant = true,
            BindQuantMethod = true
        };

        var pmgr = CompileSession.CreatePassManager("Passes");
        // 0. Shape infer
        // pmgr.Add(new ShapeInferPass("0_ShapeInfer"));
        // 1. RegisterTargetInDependentPass
        // pmgr.Add(new Transform.EGraphPass("1_TargetInDependentPass")
        // {
        //   new Passes.Rules.Neutral.FoldNopTranspose(),
        //   new Passes.Rules.Neutral.FoldTwoTransposes(),
        // });

        // 0. TargetIndependentPass
        // pmgr.Add(new Transform.DataflowPass("0_AddMarker")
        // {
        //     new AddRangeOfAndMarkerToConv2D(),
        // });

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<AddRangeOfAndMarker>();
        });
        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");
        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ToFakePdpReduce>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<DisableConvDWPermitInt16Quant>();
            p.Add<DisableConvPdp0ReducePermitInt16Quant>();
            p.Add<SquantFineTuneFakeConv2DWeights>();
        });
        // 2.5. BindQuantizeConfig
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");
        pmgr.AddWithName<DataflowPass>("LowerFakePdp").Configure(p =>
        {
            p.Add<ToGNNEPdpReduce>();
        });
        pmgr.AddWithName<DataflowPass>("LowerFake").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNETranspose>();
            p.Add<EliminateFloat32>();
            p.Add<FoldQuantDeQuant>();
            p.Add<FoldDeQuantQuant>();
            // p.Add<Passes.Rules.Neutral.FoldConstCall>();
            p.Add<FoldNopReshape>();
            p.Add<QuantToAct1>();
            p.Add<RemoveMarker>();
        });
        pmgr.AddWithName<DataflowPass>("PostLowering").Configure(p =>
        {
            p.Add<FuseQuantIntoConv>();
            p.Add<FuseQuantIntoPdp1>();
            p.Add<DWToPdp>();
        });


        pmgr.AddWithName<DataflowPass>("Fusion").Configure(p =>
        {
            p.Add<ConvFusion>();
            p.Add<TransposeFusion>();
            p.Add<Pdp1Fusion>();
            p.Add<Pdp0DwFusion>();
            p.Add<Pdp0ReduceFusion>();
            p.Add<ActSIFFusion>();
        });

        // pmgr.Add(new K230FusionToTirPass("5_FusionToTirPass"));
        pmgr.AddWithName<K230FusionToTirPass>("ToTir");

        // Before CodeGen
        pmgr.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>(); // 常量折叠所有的指令参数.
        });
        // // dump ddr bandwidth.
        pmgr.AddWithName<DDrBufferSchdeulePass>("DDrBufferSchdeule");
        pmgr.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>(); // 折叠自定义op.
        });

        await pmgr.RunAsync(module);

        var (kmodel_path, kmodel) = Testing.BuildKModel("main", module, CompileSession);

        var input_tensor = new[]
        {
            Random.Normal(input.CheckedDataType, input.CheckedShape).Evaluate().AsTensor()
        };
        Testing.DumpInterpModel(kmodel_path, input_tensor, Dumpper.Directory);
        var result = Testing.RunKModel(kmodel_path, Dumpper.Directory, input_tensor);
    }
#endif

    /// <summary>
    /// TestFuseQuantWithPre.
    /// </summary>
    [Fact]
    public async Task TestFuseQuantWithPre()
    {
        CompileOptions.InputFormat = "onnx";

        // step 1. import
        IRModule module;
        var input = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 16, 16, 3 }));

        // var input = new Var("input", new TensorType(DataTypes.Float32, new int[] { 1,3,224,224 }));
        {
            var v0 = Tensors.Transpose(input, new[] { 0, 3, 1, 2 }); // f32[1,3,224,224]
            var v1 = NN.Conv2D(v0, Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 16, 3, 3, 3 }).Evaluate().AsTensor(), Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 16 }).Evaluate().AsTensor(), new[] { 2, 2 }, Pad(new[] { new[] { 0, 1 }, new[] { 0, 1 } }), new[] { 1, 1 }, PadMode.Constant, 1, new float[] { 0, 6 }); // f32[1,32,112,112]
            var v2 = NN.Conv2D(v1, Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 16, 1, 3, 3 }).Evaluate().AsTensor(), Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 16 }).Evaluate().AsTensor(), new[] { 1, 1 }, Pad(new[] { new[] { 1, 1 }, new[] { 1, 1 } }), new[] { 1, 1 }, PadMode.Constant, 16, new float[] { 0, 6 }); // f32[1,32,112,112]
            module = new IRModule(new Function("main", v2, input));
        }

        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false, // true
        };

        var pmgr = CompileSession.CreatePassManager("Passes");

        // 0. Shape infer
        // pmgr.Add(new ShapeInferPass("0_ShapeInfer"));
        // 1. RegisterTargetInDependentPass
        // pmgr.Add(new Transform.EGraphPass("1_TargetInDependentPass")
        // {
        //   new Passes.Rules.Neutral.FoldNopTranspose(),
        //   new Passes.Rules.Neutral.FoldTwoTransposes(),
        // });

        // 0. TargetIndependentPass
        // pmgr.Add(new Transform.DataflowPass("0_AddMarker")
        // {
        //     new AddRangeOfAndMarkerToConv2D(),
        // });

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<AddRangeOfAndMarker>();
        });

        // await pmgr.RunAsync(module);
        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("ToFake").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ToFakePdpReduce>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
        });

        // 2.5. BindQuantizeConfig
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");
        pmgr.AddWithName<DataflowPass>("LowerFake").Configure(p =>
        {
            p.AddAnalysis<IExprUserAnalysisResult>();
            p.Add<ToGNNEPdpReduce>();
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNETranspose>();
            p.Add<EliminateFloat32>();
            p.Add<FoldQuantDeQuant>();
            p.Add<FoldDeQuantQuant>();

            // p.Add<Transform.Rules.Neutral.FoldConstCall>();
            p.Add<FoldNopReshape>();
            p.Add<FuseQuantIntoConv>();
            p.Add<FuseQuantIntoPdp1>();
            p.Add<QuantToAct1>();
            p.Add<DWToPdp>();
            p.Add<RemoveMarker>();
        });

        pmgr.AddWithName<DataflowPass>("Fusion").Configure(p =>
        {
            p.Add<ConvFusion>();
            p.Add<TransposeFusion>();
            p.Add<Pdp1Fusion>();
            p.Add<Pdp0DwFusion>();
            p.Add<Pdp0ReduceFusion>();
            p.Add<ActSIFFusion>();
        });

        pmgr.AddWithName<K230FusionToTirPass>("FusionToTirPass");

        // Before CodeGen
        pmgr.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>(); // 常量折叠所有的指令参数.
        });

        // // dump ddr bandwidth.
        pmgr.AddWithName<Passes.DDrBufferSchdeulePass>("DDrBufferSchdeule");
        pmgr.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>(); // 折叠自定义op.
        });

        await pmgr.RunAsync(module);

        var (kmodelPath, kmodel) = Testing.BuildKModel("main", module, CompileSession);

        var inputTensor = new[]
        {
            Random.Normal(input.CheckedDataType, input.CheckedShape).Evaluate().AsTensor(),
        };
        Testing.DumpInterpModel(kmodelPath, inputTensor, Dumpper.Directory);
        var result = Testing.RunKModel(kmodelPath, Dumpper.Directory, inputTensor);
    }

    [Fact]
    public void TestMiniTestModelWithTflite()
    {
        var root = ModelRoot("models");

        // var modelPath = Path.Combine(root, "ssd_mobilenetv1_300x300.onnx");
        var modelPath = Path.Combine(root, "model_f32.tflite");
        var in_shape = new[] { 1, 32, 32, 16 };
        var options = CompileOptions;
        options.InputFormat = "tflite";
        var result = TestMiniTestModel(options, modelPath, in_shape, DataTypes.Float32, "input");
    }

    [Fact]
    public async Task TestPtqTflite()
    {
        string root = ModelRoot("models");
        string modelPath = Path.Combine(root, "model_f32.tflite");

        int[] inShape = { 1, 224, 224, 3 };
        var options = CompileOptions;
        options.InputFormat = "tflite";
        await TestPtqModel(options, modelPath, inShape);
    }

    internal async Task TestPtqModel(CompileOptions compileOptions, string modelPath, Shape inShape)
    {
        if (!File.Exists(modelPath))
        {
            return;
        }

        await using var modelStream = File.OpenRead(modelPath);
        BaseImporter importer;
        if (CompileOptions.InputFormat == "tflite")
        {
            using var reader = new BinaryReader(modelStream);
            importer = new TFLiteImporter(reader.ReadBytes((int)modelStream.Length), CompileSession);
        }
        else
        {
            importer = new OnnxImporter(modelStream, CompileSession);
        }

        var module = importer.Import();

        var pmgr = CompileSession.CreatePassManager("Passes");

        // Var input = new Var("input", new TensorType(DataTypes.Float32, in_shape));
        var input = ((Function)module.Entry!).Parameters[0];
        CompileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
            UseSquant = true,

            // ExportQuantScheme = true,
            // ExportWeightRangeByChannel = true,
        };

        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            p.Add<Passes.Rules.Neutral.BroadcastTransposeOutputNames>();
        });

        pmgr.Add<ShapeInferPass>();

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            // don't reorder
            p.Add<Passes.Rules.Neutral.FoldConstCall>();
            p.Add<Passes.Rules.Neutral.FoldNopTranspose>();
            p.Add<Passes.Rules.Neutral.FoldTwoTransposes>();
            p.Add<Passes.Rules.Neutral.CombineTransposeUnary>();
            p.Add<Passes.Rules.Neutral.CombineTransposePad>();
            p.Add<Passes.Rules.Neutral.CombinePadTranspose>();
            p.Add<Passes.Rules.Neutral.CombineBinaryTranspose>();
            p.Add<Passes.Rules.Neutral.CombineConstBinaryTranspose>();
            p.Add<Passes.Rules.Neutral.CombineTransposeConstBinary>();
            p.Add<Passes.Rules.Neutral.CombineTransposeReduce>();
            p.Add<Passes.Rules.Neutral.CombineTransposeActivations>();
            p.Add<Passes.Rules.Neutral.CombineActivationsTranspose>();
            p.Add<Passes.Rules.Neutral.CombineTransposeConcat>();
            p.Add<Passes.Rules.Neutral.CombineBinaryReshape>();
            p.Add<Passes.Rules.Neutral.CombineConstBinaryReshape>();
            p.Add<Passes.Rules.Neutral.CombineUnaryReshape>();
            p.Add<Passes.Rules.Neutral.CombineActivationsReshape>();
            p.Add<Passes.Rules.Neutral.FoldNopPad>();
            p.Add<Passes.Rules.Neutral.FoldConv2DPads>();
            p.Add<Passes.Rules.Neutral.FoldReduceWindow2DPads>();
            p.Add<Passes.Rules.Neutral.SqueezeToReshape>();
            p.Add<Passes.Rules.Neutral.UnSqueezeToReshape>();
            p.Add<Passes.Rules.Neutral.TransposeToReshape>();
            p.Add<Passes.Rules.Neutral.FlattenToReshape>();
            p.Add<Passes.Rules.Neutral.FoldNopReshape>();
            p.Add<Passes.Rules.Neutral.FoldTwoReshapes>();
            p.Add<Passes.Rules.Neutral.FoldLayerNormPattern1>();
            p.Add<Passes.Rules.Neutral.FoldLayerNormPattern2>();
            p.Add<Passes.Rules.Neutral.FoldLayerNormPattern3>();
            p.Add<Passes.Rules.Neutral.FoldGeluWithScale>();
            p.Add<Passes.Rules.Neutral.FoldGeneralGelu>();
            p.Add<Passes.Rules.Neutral.FoldSwishPattern1>();
            p.Add<Passes.Rules.Neutral.FoldSwishPattern2>();
            p.Add<Passes.Rules.Neutral.FoldHardSwish1>();
            p.Add<Passes.Rules.Neutral.FoldHardSwish2>();
        });

        // 0.5.
        pmgr.AddWithName<DataflowPass>("TargetInDependent0.5").Configure(p =>
        {
            p.Add<AddRangeOfAndMarker>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
            p.Add<ToFakeConv2DTranspose>();
            p.Add<ToFakeMatmul>();
            p.Add<ToFakePdpReduce>();
            p.Add<ToFakeDynamicMatmul>();
            p.Add<ToFakeLSTM>();
            p.Add<ToFakeAi2dPad>();
            p.Add<ToFakeAi2dResize>();
            p.Add<AddToFakeActivation>();
            p.Add<MulToFakeActivation>();
            p.Add<ReluToFakeActivation>();
            p.Add<GeneralAddSubMulDivToFakeActivation>();
            p.Add<ReplaceFakeConv2DWeightsRangeToByChannel>();
            p.Add<FoldTwoFakeActivation>();
            p.Add<FoldFakeConv2DAndFakeActivation>();
            p.Add<DisableConvDWPermitInt16Quant>();
            p.Add<DisableConvPdp0ReducePermitInt16Quant>();
            p.Add<SquantFineTuneFakeConv2DWeights>();
        });

        // 2.5. BindQuantizeConfig
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            // don't reorder
            p.Add<ToGNNEPdpReduce>();
            p.Add<ToGNNEConv2D>();
            p.Add<RestoreFakeConv2D>();
            p.Add<ToGNNEConv2DTranspose>();
            p.Add<ToDynamicGNNEMatMul>();
            p.Add<ToGNNELSTM>();
            p.Add<ToAi2dPad>();
            p.Add<ToAi2dResize>();

            // new ToGNNEMatMul(),
            p.Add<ToGNNEActivation>();
            p.Add<ToGNNETranspose>();
            p.Add<FuseQuantIntoPdp0Reduce>();
            p.Add<FoldGNNEPdp0ReduceAndGNNEActivation>();
            p.Add<FoldNopReshape>();
            p.Add<FoldConstCall>();
            p.Add<RemoveMarker>();
        });

        await pmgr.RunAsync(module);
    }

    private string ModelRoot(string name) => Path.Combine(Dumpper.Directory, "..", "..", "tmp", name);

    private async Task TestMiniTestModel(CompileOptions compileOptions, string modelPath, Shape inShape, DataType inType, string inName)
    {
        if (!File.Exists(modelPath))
        {
            return;
        }

        // using var modelStream = File.OpenRead(model_path);
        using var modelStream = File.OpenRead(modelPath);
        BaseImporter importer;
        if (compileOptions.InputFormat == "tflite")
        {
            using var reader = new BinaryReader(modelStream);
            importer = new TFLiteImporter(reader.ReadBytes((int)modelStream.Length), CompileSession);
        }
        else
        {
            importer = new OnnxImporter(modelStream, CompileSession);
        }

        var module = importer.Import();

        var pmgr = CompileSession.CreatePassManager("Passes");

        Var input = new Var("input", new TensorType(DataTypes.Float32, inShape));
        compileOptions.QuantizeOptions = new()
        {
            ModelQuantMode = ModelQuantMode.UsePTQ,
            CalibrationDataset = new RandCalibrationDatasetProvider(new[] { input }),
            CalibrationMethod = CalibMethod.NoClip,
            BindQuantMethod = false,
            UseSquant = true,
        };

        pmgr.AddWithName<ShapeInferPass>("ShapeInfer");

        // 0. TargetIndependentPass
        pmgr.AddWithName<DataflowPass>("TargetInDependent").Configure(p =>
        {
            // new Passes.Rules.Neutral.AddRangeOfAndMarkerToConv2D(),
            // new Passes.Rules.Neutral.AddRangeOfAndMarkerToReduceWindow2D(),
            // //new Passes.Rules.Neutral.AddRangeOfAndMarkerToConv2DTranspose(),
            // new Passes.Rules.Neutral.AddRangeOfAndMarkerToBinary(),
            // new Passes.Rules.Neutral.AddRangeOfAndMarkerToUnary(),
            // new FoldTwoTransposes(),
            p.Add<AddRangeOfAndMarker>();
            p.Add<FoldTwoTransposes>();
        });

        // 1. AssignRanges
        pmgr.AddWithName<EGraphPassWithQuantize>("AssignRanges");

        // 2. RegisterTargetDependentPass
        pmgr.AddWithName<DataflowPass>("TargetDependent").Configure(p =>
        {
            p.Add<ReplaceMarker>();
            p.Add<ToFakeConv2D>();
        });

        // 2.5. BindQuantizeConfig
        pmgr.AddWithName<EGraphPassWithBindQuantizeConfig>("BindQuantizeConfig");

        // 3. after quant pass
        pmgr.AddWithName<DataflowPass>("AfterQuant").Configure(p =>
        {
            p.Add<ToGNNEConv2D>();
            p.Add<ToGNNEPad>();
            p.Add<EliminateFloat32>();
            p.Add<FoldQuantDeQuant>();
            p.Add<FoldDeQuantQuant>();
            p.Add<FoldNopReshape>();
            p.Add<FuseQuantIntoConv>();
        });

        pmgr.AddWithName<DataflowPass>("Fusion").Configure(p =>
        {
            p.Add<ConvFusion>();
            p.Add<PadFusion>();
        });

        pmgr.AddWithName<K230FusionToTirPass>("FusionToTirPass");

        // Before CodeGen
        pmgr.AddWithName<PrimFuncPass>("BufferStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>(); // 常量折叠所有的指令参数.
        });

        // // dump ddr bandwidth.
        pmgr.AddWithName<Passes.DDrBufferSchdeulePass>("DDrBufferSchdeule");
        pmgr.AddWithName<PrimFuncPass>("InstStage").Configure(p =>
        {
            p.Add<Mutators.K230.FoldConstCall>();
            p.Add<Mutators.K230.FoldBufferSlot>(); // 折叠自定义op.
        });

        await pmgr.RunAsync(module);

        var (kmodel_path, kmodel) = Testing.BuildKModel("main", module, CompileSession);

        var input_tensor = new[]
        {
            Random.Normal(input.CheckedDataType, input.CheckedShape).Evaluate().AsTensor(),
        };
        Testing.DumpInterpModel(kmodel_path, input_tensor, Dumpper.Directory);
        var result = Testing.RunKModel(kmodel_path, Dumpper.Directory, input_tensor);
    }

    private Expr Pad(int[][] p) => Const.FromTensor(Tensor.From(p.SelectMany(i => i).ToArray(), new long[] { 2, 2 }));

    public sealed class DumpVisitor : ExprVisitor<int, IRType>
    {
        public int FoundOpCount<T>()
            where T : Op
        {
            return ExprMemo.Keys.OfType<T>().Count();
        }

        protected override int DefaultVisitLeaf(Expr expr) => 0;
    }
}

internal sealed class RandCalibrationDatasetProvider : ICalibrationDatasetProvider
{
    private const int _count = 5;

    public RandCalibrationDatasetProvider(IEnumerable<Var> vars)
    {
        Samples = Enumerable.Range(0, _count).Select(i =>
        {
            var values = new Dictionary<Var, IValue>();
            int j = 0;
            foreach (var var in vars)
            {
                var.InferenceType();
                var shape = var.CheckedShape.Select(d => d.IsUnknown ? 1 : d.FixedValue).ToArray();
                var value = Value.FromTensor(Random.Normal(var.CheckedDataType, 0, 1, (i * _count) + j, shape).Evaluate()
                    .AsTensor());
                values.Add(var, value);
            }

            return values;
        }).ToAsyncEnumerable();
    }

    public int? Count => _count;

    public IAsyncEnumerable<IReadOnlyDictionary<Var, IValue>> Samples { get; }
}

internal sealed class SolidCalibrationDatasetProvider : ICalibrationDatasetProvider
{
    private const int _count = 5;

    public SolidCalibrationDatasetProvider(IEnumerable<Var> vars)
    {
        Samples = Enumerable.Range(0, _count).Select(_ =>
        {
            var values = new Dictionary<Var, IValue>();
            foreach (var var in vars)
            {
                var.InferenceType();
                int[] shape = var.CheckedShape.Select(d => d.IsUnknown ? 1 : (int)d.FixedValue).ToArray();
                int shapeSize = 1;
                foreach (var t in shape)
                {
                    shapeSize *= t;
                }

                var tmpValue = new List<float>();
                for (int j = 0; j < shapeSize; j++)
                {
                    tmpValue.Add(((j * 1.0f / shapeSize) - 0.5f) * 2);
                }

                var value = Value.FromTensor(Tensor.From(tmpValue.ToArray(), shape.Select(x => (long)x).ToArray()));
                values.Add(var, value);
            }

            return values;
        }).ToAsyncEnumerable();
    }

    public int? Count => _count;

    public IAsyncEnumerable<IReadOnlyDictionary<Var, IValue>> Samples { get; }
}
