﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime;
using System.Runtime.InteropServices;
using System.Runtime.Intrinsics;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NetFabric.Hyperlinq;
using Nncase.CodeGen;
using Nncase.IR;
using Nncase.IR.NN;
using Nncase.IR.Tensors;
using Nncase.Passes.Transforms;
using Nncase.Runtime.Interop;
using Nncase.Targets;
using Nncase.Tests.TestFixture;
using Nncase.Utilities;
using Xunit;

namespace Nncase.Tests.TargetTest;

public class XpuKernelCase
{
    public XpuKernelCase(string name, Fusion fusion, IVar[] vars, Tensor[] inputs, Tensor[] rtinputs)
    {
        Name = name;
        Fusion = fusion;
        Vars = vars;
        Inputs = inputs;
        RTInputs = rtinputs;
    }

    public string Name { get; }

    public Fusion Fusion { get; }

    public IReadOnlyList<IVar> Vars { get; }

    public IReadOnlyList<Tensor> Inputs { get; }

    public IReadOnlyList<Tensor> RTInputs { get; set; }
}

[CollectionDefinition(nameof(NotThreadSafeResourceCollection), DisableParallelization = true)]
public class NotThreadSafeResourceCollection
{
}

[Collection(nameof(NotThreadSafeResourceCollection))]
[AutoSetupTestMethod(InitSession = true)]
public sealed class UnitTestXPUKernels : TestClassBase
{
    public UnitTestXPUKernels()
    {
        DefaultTargetName = XPUTarget.Kind;
        CompileOptions.TargetOptions = new NTTTargetOptions
        {
            Hierarchies = new[] { new[] { 1, 2, 8, 4, 4 } },
            HierarchyNames = "cdyxt",
            HierarchySizes = new long[] { 512 * (int)MathF.Pow(2, 20), 1 * (int)MathF.Pow(2, 20) },
            MemoryCapacities = new[] { 262144, 150994944 },
            MemoryBandWidths = new[] { 64, 32 },
            HierarchyLatencies = new[] { 10000, 10000, 10000, 10000, 10000 },
            UnifiedMemoryArch = false,
            Vectorize = true,
            CustomOpScheme = string.Empty,
            HierarchyKind = HierarchyKind.SMT,
        };
#if DEBUG
        CompileOptions.DumpFlags = Diagnostics.DumpFlags.PassIR | Diagnostics.DumpFlags.Compile | Diagnostics.DumpFlags.Schedule | Diagnostics.DumpFlags.Rewrite | Diagnostics.DumpFlags.CodeGen | Diagnostics.DumpFlags.EGraphCost | Diagnostics.DumpFlags.Tiling;
#endif
    }

    public enum PostOpKind
    {
        None,
        MulScalar,
        ScalarDiv,
    }

    public static Placement DefaultPlacement => new Placement(new[] { 1 }, "t");

    public static int Lane => Vector256.IsHardwareAccelerated ? 32 : 16;

    public static int Rank => 2;

    public static TheoryData<long[], Runtime.TypeCode, Runtime.TypeCode, PostOpKind[], int[], int[][], int> TestVectorizeCastData => new()
    {
        { [256 /* seq_len */, 16, 128], Runtime.TypeCode.BFloat16, Runtime.TypeCode.Float32, [], [0], [[1, 2, 3, 4], [-1], [-1]], 3 },
    };

    [Fact]
    public async Task TestOutputCopyOpt()
    {
        var hierarchy = new[] { 1, 2, 8, 4, 4 };
        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;
        targetOptions.Hierarchies[0] = hierarchy;
        targetOptions.UnifiedMemoryArch = false;
        targetOptions.HierarchyKind = HierarchyKind.SMT;
        targetOptions.HierarchyNames = string.Join(string.Empty, "cdyxt".TakeLast(hierarchy.Length));
        targetOptions.HierarchySizes = Enumerable.Repeat((long)MathF.Pow(2, 30), hierarchy.Length).ToArray();
        var lhsType = new TensorType(DataTypes.Float32, new[] { 1, 56, 4096 });
        var rhsType = new TensorType(DataTypes.Float32, new[] { 4096, 4608 });
        var lhs = new Var(lhsType);
        var rhs = new Var(rhsType);

        var feedDict = new Dictionary<IVar, IValue>() {
            { lhs, IR.F.Random.Normal(DataTypes.Float32, 1.0f, 1.0f, 1, lhsType.Shape.ToValueArray()).Evaluate() },
            { rhs, IR.F.Random.Normal(DataTypes.Float32, 1.0f, 1.0f, 1, rhsType.Shape.ToValueArray()).Evaluate() },
        };

        var placement = new Placement(hierarchy, targetOptions.HierarchyNames);
        var lhsBoxing = IR.F.Distributed.Boxing(lhs, new DistributedType(lhsType, new SBP[] { SBP.B, SBP.B, SBP.S(new[] { 2, 3 }) }, placement));
        var rhsBoxing = IR.F.Distributed.Boxing(rhs, new DistributedType(rhsType, new SBP[] { SBP.S(new[] { 2, 3 }), SBP.S(new[] { 1, 4 }) }, placement));
        var matmul = IR.F.Tensors.MatMul(lhsBoxing, rhsBoxing);
        var outShape = new[] { 1, 56, 4608 };
        var mm = IR.F.Distributed.Boxing(matmul, new DistributedType(new TensorType(DataTypes.Float32, outShape), new SBP[] { SBP.B, SBP.B, SBP.S(new[] { 1, 4 }) }, placement));
        var sumed = IR.F.Distributed.Boxing(mm, new DistributedType(new TensorType(DataTypes.Float32, outShape), new SBP[] { SBP.B, SBP.S(new[] { 2 }), SBP.S(new[] { 1, 3, 4 }) }, placement));
        var post = IR.F.Distributed.Boxing(sumed, new TensorType(DataTypes.Float32, outShape));
        post.Metadata = new Passes.Distributed.AutoDistributedMetaData() { Skip = true };

        await RunCases(Path.Join(CompileOptions.DumpDir.ToString(), $"Theory{0}"), feedDict, new[] { post });
    }

    [Theory(Skip = "Double free Bug")]
    [InlineData(new object[] { new long[] { 83 }, new long[] { 83 }, 0 })]
    [InlineData(new object[] { new long[] { 1 }, new long[] { 84 }, 1 })]
    [InlineData(new object[] { new long[] { 12, 1 }, new long[] { 12, 84 }, 2 })]
    [InlineData(new object[] { new long[] { 4, 4, 4, 4 }, new long[] { 4, 12, 20, 28 }, 3 })]
    [InlineData(new object[] { new long[] { 25, 28, 31, 19, 25 }, new long[] { 25 + 0, 28 + 1, 31 + 16, 19 + 4, 25 + 7 }, 3 })]
    public async Task TestDynamicGetPositionIds(long[] queryLens, long[] seqLens, int count)
    {
        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;
        var hierarchy = targetOptions.Hierarchies[0];
        var dimVar = new DimVar("seq_length")
        {
            Metadata = new() { Range = new(1, MathUtility.AlignUp(queryLens.Sum(), 128)) },
        };

        var fixture = new PagedAttentionKVCacheTestFixture(queryLens, seqLens, 2, 2, 64, 64, 32, Runtime.TypeCode.Float32, 1, [PagedKVCacheDimKind.NumBlocks, PagedKVCacheDimKind.NumLayers, PagedKVCacheDimKind.KV, PagedKVCacheDimKind.NumKVHeads, PagedKVCacheDimKind.HeadDim, PagedKVCacheDimKind.BlockSize], [PagedKVCacheDimKind.HeadDim], [PagedKVCacheDimKind.NumKVHeads, PagedKVCacheDimKind.NumBlocks], [SBP.S(1), SBP.S(2, 3)], [AttentionDimKind.Seq, AttentionDimKind.Dim, AttentionDimKind.Head], [AttentionDimKind.Seq, AttentionDimKind.Dim, AttentionDimKind.Head]);

        var placement = new Placement(hierarchy, targetOptions.HierarchyNames);
        var dataGeneratorOptions = new PagedAttentionKVCacheTestFixture.DataGeneratorOptions(Random: true, IncreaseBy: [AttentionDimKind.Head], ResetForKV: true);
        var referenceResults = PagedAttentionKVCacheTestFixture.PrepareReferenceResults(fixture.QueryLens, fixture.SeqLens, fixture.NumQHeads, fixture.Config.NumKVHeads, fixture.Config.HeadDim, fixture.Config.NumLayers, fixture.Config.KVPrimType, dataGeneratorOptions);

        var (_, _, _, kVCacheObjVar) = Evaluator.NN.RefPagedAttentionKVCache.BuildPagedAttentionKernel(fixture.QueryLens, fixture.SeqLens, fixture.NumQHeads, fixture.NumBlocks, fixture.QLayout, fixture.KLayout, fixture.Config, new(true));

        var kvinputs = PagedAttentionKVCacheTestFixture.PrepareKVInputs(fixture.QueryLens, fixture.SeqLens, fixture.ContextLens, fixture.NumBlocks, placement, referenceResults, fixture.Config);

        var pre = IR.F.NN.GetPositionIds(dimVar, kVCacheObjVar);

        var feedDict = new Dictionary<IVar, IValue>
        {
            { kVCacheObjVar, Value.FromTensor(Tensor.FromScalar(new Reference<IPagedAttentionKVCache>(kvinputs.KVCacheObj))) },
            { dimVar, Value.FromTensor(queryLens.Sum()) },
        };

        var rtFeedDict = new Dictionary<IVar, IValue>();
        var kvCacheAddrs = new List<long>();
        {
            var logicalKVShape = kvinputs.KVCacheObj.KVCaches.Dimensions.ToArray();
            foreach (var topoIndices in hierarchy.Select(i => Enumerable.Range(0, i)).CartesianProduct().Select(arr => arr.Select(i => (long)i).ToArray()))
            {
                var indices = topoIndices.Concat(Enumerable.Repeat(0L, logicalKVShape.Length - hierarchy.Length)).ToArray();
                var shape = Enumerable.Repeat(1L, hierarchy.Length).Concat(logicalKVShape[hierarchy.Length..]).ToArray();
                var kvStorage = kvinputs.KVCacheObj.KVCaches.View(indices, shape);

                // FIXME: Memory leak here
                unsafe
                {
                    kvCacheAddrs.Add((long)kvStorage.PinBuffer().Pointer);
                }
            }
        }

        var rtkvObj = RTPagedAttentionKVCache.Create(
                    kvinputs.KVCacheObj.NumSeqs,
                    kvinputs.KVCacheObj.NumTokens,
                    RTTensor.FromTensor(kvinputs.KVCacheObj.ContextLens),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.SeqLens),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.BlockTables),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.SlotMapping),
                    RTTensor.FromTensor(kvCacheAddrs.ToArray()));
        rtFeedDict.Add(kVCacheObjVar, Value.FromTensor(Tensor.FromScalar(new Reference<IPagedAttentionKVCache>(rtkvObj))));
        rtFeedDict.Add(dimVar, Value.FromTensor(queryLens.Sum()));

        await RunCases($"Theory{count}", feedDict, new[] { pre }, rtFeedDict);
    }

    [Theory]
    [ClassData(typeof(TestPagedAttentionWithKVCacheData))]
    public async Task TestGatherPagedAttentionKVCache(PagedAttentionKVCacheTestFixture fixture, bool dynamicShape, bool dynamicPadding, int count)
    {
        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;
        targetOptions.Vectorize = false;
        var hierarchy = targetOptions.Hierarchies[0];
        var placement = new Placement(targetOptions.Hierarchies[0], targetOptions.HierarchyNames);
        bool gatherOnly = true;
        var dataGeneratorOptions = new PagedAttentionKVCacheTestFixture.DataGeneratorOptions(Random: true, IncreaseBy: [AttentionDimKind.Head], ResetForKV: true);
        var referenceResults = PagedAttentionKVCacheTestFixture.PrepareReferenceResults(fixture.QueryLens, fixture.SeqLens, fixture.NumQHeads, fixture.Config.NumKVHeads, fixture.Config.HeadDim, fixture.Config.NumLayers, fixture.Config.KVPrimType, dataGeneratorOptions);

        var testKernel = Evaluator.NN.RefPagedAttentionKVCache.BuildPagedAttentionKernel(fixture.QueryLens, fixture.SeqLens, fixture.NumQHeads, fixture.NumBlocks, fixture.QLayout, fixture.KLayout, fixture.Config, new(true, dynamicShape, dynamicPadding, MathUtility.AlignUp(fixture.QueryLens.Sum(), 128)));

        CompileOptions.ShapeBucketOptions.VarMap.Add(testKernel.QueryVar, testKernel.QueryVar.CheckedShape.ToArray());

        var kvinputs = PagedAttentionKVCacheTestFixture.PrepareKVInputs(fixture.QueryLens, fixture.SeqLens, fixture.ContextLens, fixture.NumBlocks, placement, referenceResults, fixture.Config);

        var feedDict = new Dictionary<IVar, IValue>();
        var rtFeedDict = new Dictionary<IVar, IValue>();
        {
            var queryTensor = referenceResults.GetQueryTensor();
            feedDict.Add(testKernel.QueryVar, Value.FromTensor(queryTensor));
            rtFeedDict.Add(testKernel.QueryVar, Value.FromTensor(queryTensor));
            for (int layerId = 0; layerId < fixture.Config.NumLayers; layerId++)
            {
                feedDict.Add(testKernel.KVVars[layerId][0], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 0)));
                feedDict.Add(testKernel.KVVars[layerId][1], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 1)));
                rtFeedDict.Add(testKernel.KVVars[layerId][0], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 0)));
                rtFeedDict.Add(testKernel.KVVars[layerId][1], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 1)));
            }

            feedDict.Add(testKernel.KVCacheObjVar, Value.FromTensor(Tensor.FromScalar(new Reference<IPagedAttentionKVCache>(kvinputs.KVCacheObj))));
            var shardingDimensions = kvinputs.KVCacheObj.KVCaches.Dimensions[..fixture.Config.ShardingAxes.Count].ToArray();
            var kvCacheAddrs = new List<long>();
            {
                var logicalKVShape = kvinputs.KVCacheObj.KVCaches.Dimensions.ToArray();
                foreach (var shardIndices in shardingDimensions.Select(i => LinqUtility.Range(0L, (int)i)).CartesianProduct().Select(arr => arr.ToArray()))
                {
                    var indices = shardIndices.Concat(Enumerable.Repeat(0L, logicalKVShape.Length - shardIndices.Length)).ToArray();
                    var shape = Enumerable.Repeat(1L, shardIndices.Length).Concat(logicalKVShape[shardIndices.Length..]).ToArray();
                    var kvStorage = kvinputs.KVCacheObj.KVCaches.View(indices, shape).Squeeze(LinqUtility.Range(0L, shardIndices.Length).ToArray());

                    // FIXME: Memory leak here
                    unsafe
                    {
                        kvCacheAddrs.Add((long)kvStorage.PinBuffer().Pointer);
                    }
                }
            }

            var rtkvObj = RTPagedAttentionKVCache.Create(
                    kvinputs.KVCacheObj.NumSeqs,
                    kvinputs.KVCacheObj.NumTokens,
                    RTTensor.FromTensor(kvinputs.KVCacheObj.ContextLens),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.SeqLens),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.BlockTables),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.SlotMapping),
                    RTTensor.FromTensor(kvCacheAddrs.ToArray()));
            rtFeedDict.Add(testKernel.KVCacheObjVar, Value.FromTensor(Tensor.FromScalar(new Reference<IPagedAttentionKVCache>(rtkvObj))));
        }

        // 4. evaluate and compare
        if (!gatherOnly)
        {
            var refTensor = referenceResults.GetOutputTensor();
            var actualTensor = testKernel.Root.Evaluate(feedDict).AsTensor();
            var cos = Comparator.CosSimilarity(refTensor, actualTensor);
            Assert.True(cos > 0.999, $"cos: {cos} ");
        }

        await RunCases($"Theory{count}", feedDict, new[] { testKernel.Root }, rtFeedDict);
    }

    [Theory]
    [ClassData(typeof(TestPagedAttentionWithKVCacheData))]
    public async Task TestPagedAttentionKVCache(PagedAttentionKVCacheTestFixture fixture, bool dynamicShape, bool dynamicPadding, int count)
    {
        if (CodeGen.XPU.CSourceUtilities.GetRuntimeMode() != CodeGen.XPU.RuntimeMode.SystemMode || dynamicShape == false)
        {
            return;
        }

        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;

        var paged_attn_csource_path = Environment.GetEnvironmentVariable("NNCASE_PLUGIN_PAGED_ATTENTION") ?? throw new InvalidOperationException("NNCASE_PLUGIN_PAGED_ATTENTION environment variable is not set.");
        var scheme = new Passes.Distributed.CustomOpScheme("1", "TestModel", [new("xx", "PagedAttention", Array.Empty<long[]>(), Array.Empty<SBP[]>(), 0, paged_attn_csource_path, "fasterllm::distributed_flash_attention")]);
        var scheme_path = Path.Join(Diagnostics.DumpScope.Current.Directory, "custom_scheme.json");
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("custom_scheme.json"))
        {
            JsonSerializer.Serialize(fs, scheme, new JsonSerializerOptions { WriteIndented = true });
        }

        targetOptions.CustomOpScheme = scheme_path;

        using (var fs = Diagnostics.DumpScope.Current.CreateSubDummper($"Theory{count}").OpenFile("fixture.txt"))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(fixture.ToString());
            }
        }

        targetOptions.Vectorize = false;
        var hierarchy = targetOptions.Hierarchies[0];
        var placement = new Placement(targetOptions.Hierarchies[0], targetOptions.HierarchyNames);
        bool gatherOnly = false;
        var dataGeneratorOptions = new PagedAttentionKVCacheTestFixture.DataGeneratorOptions(Random: true, IncreaseBy: [AttentionDimKind.Head], ResetForKV: true);
        var referenceResults = PagedAttentionKVCacheTestFixture.PrepareReferenceResults(fixture.QueryLens, fixture.SeqLens, fixture.NumQHeads, fixture.Config.NumKVHeads, fixture.Config.HeadDim, fixture.Config.NumLayers, fixture.Config.KVPrimType, dataGeneratorOptions);

        // note: dynamic shape but only support one.
        var testKernel = Evaluator.NN.RefPagedAttentionKVCache.BuildPagedAttentionKernel([], [], fixture.NumQHeads, fixture.NumBlocks, fixture.QLayout, fixture.KLayout, fixture.Config, new(false, dynamicShape, dynamicPadding, MathUtility.AlignUp(fixture.QueryLens.Sum(), 128)));

        CompileOptions.ShapeBucketOptions.VarMap.Add(testKernel.QueryVar, testKernel.QueryVar.CheckedShape.ToArray());

        var kvinputs = PagedAttentionKVCacheTestFixture.PrepareKVInputs(fixture.QueryLens, fixture.SeqLens, fixture.ContextLens, fixture.NumBlocks, placement, referenceResults, fixture.Config);

        var feedDict = new Dictionary<IVar, IValue>();
        var rtFeedDict = new Dictionary<IVar, IValue>();
        {
            var queryTensor = referenceResults.GetQueryTensor();
            feedDict.Add(testKernel.QueryVar, Value.FromTensor(queryTensor));
            rtFeedDict.Add(testKernel.QueryVar, Value.FromTensor(queryTensor));
            for (int layerId = 0; layerId < fixture.Config.NumLayers; layerId++)
            {
                feedDict.Add(testKernel.KVVars[layerId][0], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 0)));
                feedDict.Add(testKernel.KVVars[layerId][1], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 1)));
                rtFeedDict.Add(testKernel.KVVars[layerId][0], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 0)));
                rtFeedDict.Add(testKernel.KVVars[layerId][1], Value.FromTensor(kvinputs.GetKeyValueTensor(layerId, 1)));
            }

            feedDict.Add(testKernel.KVCacheObjVar, Value.FromTensor(Tensor.FromScalar(new Reference<IPagedAttentionKVCache>(kvinputs.KVCacheObj))));
            var shardingDimensions = kvinputs.KVCacheObj.KVCaches.Dimensions[..fixture.Config.ShardingAxes.Count].ToArray();
            var kvCacheAddrs = new List<long>();
            {
                var logicalKVShape = kvinputs.KVCacheObj.KVCaches.Dimensions.ToArray();
                foreach (var shardIndices in shardingDimensions.Select(i => LinqUtility.Range(0L, (int)i)).CartesianProduct().Select(arr => arr.ToArray()))
                {
                    var indices = shardIndices.Concat(Enumerable.Repeat(0L, logicalKVShape.Length - shardIndices.Length)).ToArray();
                    var shape = Enumerable.Repeat(1L, shardIndices.Length).Concat(logicalKVShape[shardIndices.Length..]).ToArray();
                    var kvStorage = kvinputs.KVCacheObj.KVCaches.View(indices, shape).Squeeze(LinqUtility.Range(0L, shardIndices.Length).ToArray());

                    // FIXME: Memory leak here
                    unsafe
                    {
                        kvCacheAddrs.Add((long)kvStorage.PinBuffer().Pointer);
                    }
                }
            }

            var rtkvObj = RTPagedAttentionKVCache.Create(
                    kvinputs.KVCacheObj.NumSeqs,
                    kvinputs.KVCacheObj.NumTokens,
                    RTTensor.FromTensor(kvinputs.KVCacheObj.ContextLens),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.SeqLens),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.BlockTables),
                    RTTensor.FromTensor(kvinputs.KVCacheObj.SlotMapping),
                    RTTensor.FromTensor(kvCacheAddrs.ToArray()));
            rtFeedDict.Add(testKernel.KVCacheObjVar, Value.FromTensor(Tensor.FromScalar(new Reference<IPagedAttentionKVCache>(rtkvObj))));
        }

        // 4. evaluate and compare
        if (!gatherOnly)
        {
            var refTensor = referenceResults.GetOutputTensor();
            var actualTensor = testKernel.Root.Evaluate(feedDict).AsTensor();
            var cos = Comparator.CosSimilarity(refTensor, actualTensor);
            Assert.True(cos > 0.999, $"cos: {cos} ");
        }

        await RunCases($"Theory{count}", feedDict, new[] { testKernel.Root }, rtFeedDict);
    }

    [Theory]
    [InlineData(new object[] { new long[] { 1, 4, 8, 16 }, new int[] { 0 }, 0 })]
    [InlineData(new object[] { new long[] { 1, 4, 8, 16 }, new int[] { 1 }, 1 })]
    [InlineData(new object[] { new long[] { 1, 4, 8, 16 }, new int[] { 2 }, 2 })]
    [InlineData(new object[] { new long[] { 1, 4, 8, 16 }, new int[] { 3 }, 3 })]
    public async Task TestDynamicUnary(long[] shape, int[] dynamicAxes, int count)
    {
        var dimVars = new[] { "n", "c", "h", "w" }.Select((x, i) =>
        {
            var v = new DimVar(x);
            v.Metadata.Range = new(1, shape[i] * 2);
            return v;
        }).ToArray();
        var input = new Var(new TensorType(DataTypes.Float32, new RankedShape(Enumerable.Range(0, shape.Length).Select(i => dynamicAxes.Contains(i) ? dimVars[i] : (Dimension)shape[i]).ToArray())));
        CompileOptions.ShapeBucketOptions.VarMap.Add(input, dimVars);

        var pre = IR.F.Math.Unary(UnaryOp.Neg, input);
        var feedDict = new Dictionary<IVar, IValue>() {
            { input, Value.FromTensor(Tensor.FromScalar<float>(1f, shape)) },
            { dimVars[0], Value.FromTensor(shape[0]) },
            { dimVars[1], Value.FromTensor(shape[1]) },
            { dimVars[2], Value.FromTensor(shape[2]) },
            { dimVars[3], Value.FromTensor(shape[3]) },
        };

        var rule = new Passes.Rules.NTT.VectorizeUnary(Rank, Lane);
        CompilerServices.TryMatch(pre, rule.Pattern, out var result);
        var posts = new[] { pre }.Concat(rule.GetReplaceCandidates(result!, new Passes.RunPassContext()));
        await RunCases($"Theory{count}", feedDict, posts);
    }

    [Fact(Skip = "test only")]
    public async Task TestTestVectorizedRope()
    {
        var count = 0;
        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;

        var seq_len = new DimVar("seq_len")
        {
            Metadata = new() { Range = new(1, 256) },
        };

        var inputShape = new RankedShape(new Dimension[] { 64, 128, seq_len });
        var inputQ = new Var(new TensorType(DataTypes.Float16, inputShape));
        var inputSin = new Var(new TensorType(DataTypes.Float16, new RankedShape(new Dimension[] { 128, seq_len })));
        var inputCos = new Var(new TensorType(DataTypes.Float16, new RankedShape(new Dimension[] { 128, seq_len })));
        CompileOptions.ShapeBucketOptions.VarMap.Add(inputQ, inputShape.ToArray());

        var rope = IR.F.NTT.VectorizedRoPE(IR.F.Tensors.Pack(inputQ, [64], [1]), IR.F.Tensors.Pack(inputSin, [64], [0]), IR.F.Tensors.Pack(inputCos, [64], [0]));
        var pre = rope;

        var feedDict = new Dictionary<IVar, IValue>() {
            { inputQ, IR.F.Random.Normal(DataTypes.Float16, 0, 1, 1, new[] { 64, 128, 256 }).Evaluate() },
            { inputSin, IR.F.Tensors.Tile(IR.F.Random.Normal(DataTypes.Float16, 0, 1, 2, new[] { 64, 256 }), [2, 1]).Evaluate() },
            { inputCos, IR.F.Tensors.Tile(IR.F.Random.Normal(DataTypes.Float16, 0, 1, 3, new[] { 64, 256 }), [2, 1]).Evaluate() },
            { seq_len, Value.FromTensor(256L) },
        };
        {
            rope.Metadata = new() { OutputNames = new[] { "call" } };
            var scheme = new Passes.Distributed.DistributedSchema("1", "llama", [new("call", new SBP[] { SBP.S(1, 3), SBP.B, SBP.S(2) }, targetOptions.Hierarchies[0], targetOptions.HierarchyNames)]);
            var options = new JsonSerializerOptions();
            options.Converters.Add(new SBPConverter());
            options.WriteIndented = true;
            var export = System.Text.Json.JsonSerializer.Serialize(scheme, options);
            var dumpper = Diagnostics.DumpScope.Current.CreateSubDummper($"Theory{count}");
            targetOptions.DistributedScheme = Path.Join(dumpper.Directory, "schema.json");
            using (var stream = dumpper.OpenFile("schema.json"))
            {
                using (var writer = new StreamWriter(stream))
                {
                    writer.Write(export);
                }
            }
        }

        await RunCases($"Theory{count}", feedDict, new[] { pre });
    }

    [Theory]
    [InlineData(new object[] { new long[] { 101, 1024 }, 1, 0, 0 })]
    public async Task TestDynamicLayerNorm(long[] shape, int axis, int dynamicAxis, int count)
    {
        var dimVar = new DimVar("seq_len")
        {
            Metadata = new() { Range = new(1, 128) },
        };
        var inputShape = new RankedShape(Enumerable.Range(0, shape.Length).Select(i => dynamicAxis == i ? dimVar : (Dimension)shape[i]).ToArray());
        var input = new Var(new TensorType(DataTypes.Float32, inputShape));
        CompileOptions.ShapeBucketOptions.VarMap.Add(input, inputShape.ToArray());

        var pre = IR.F.NN.LayerNorm(axis, 1e-6f, input, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, new RankedShape(inputShape[axis])).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new RankedShape(inputShape[axis])).Evaluate().AsTensor(), false);
        var feedDict = new Dictionary<IVar, IValue>() {
            { input, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, shape).Evaluate() },
            { dimVar, Value.FromTensor(shape[dynamicAxis]) },
        };

        var rule = new Passes.Rules.NTT.VectorizeLayerNorm(1, 128);
        CompilerServices.TryMatch(pre, rule.Pattern, out var result);
        var posts = new[] { pre }.Concat(rule.GetReplaceCandidates(result!, new Passes.RunPassContext())).Where(x =>
        {
            if (x is Call { Target: IR.Tensors.Slice } slice && slice.Arguments[0] is Call { Target: IR.Tensors.Unpack unpack } && unpack.Axes.SequenceEqual([0]))
            {
                return false;
            }

            if (x is Call { Target: IR.Tensors.Unpack unpack2 } && unpack2.Axes.SequenceEqual([0]))
            {
                return false;
            }

            return true;
        });
        await RunCases($"Theory{count}", feedDict, posts);
    }

    [Theory]
    [InlineData([new long[] { 188, 16, 128 }, new int[] { 1, 0, 2 }, 0, 0])]
    public async Task TestDynamicTranspose(long[] shape, int[] perm, int dynamicAxis, int number)
    {
        var dimVar = new DimVar("seq_len")
        {
            Metadata = new() { Range = new(1, 256) },
        };
        var inputShape = new RankedShape(Enumerable.Range(0, shape.Length).Select(i => dynamicAxis == i ? dimVar : (Dimension)shape[i]).ToArray());
        var input = new Var(new TensorType(DataTypes.Float32, inputShape));
        CompileOptions.ShapeBucketOptions.VarMap.Add(input, inputShape.ToArray());
        Expr pre;
        {
            var v4 = IR.F.Tensors.Transpose(input, perm);
            pre = v4;
        }

        var feedDict = new Dictionary<IVar, IValue>() {
            { input, Value.FromTensor(Tensor.From(Enumerable.Range(0, (int)TensorUtilities.GetProduct(shape)).Select(i => (float)i).ToArray(), shape)) },
            { dimVar, Value.FromTensor(shape[dynamicAxis]) },
        };

        var rule = new Passes.Rules.NTT.VectorizeTranspose(1, 128);
        CompilerServices.TryMatch(pre, rule.Pattern, out var result);
        var posts = new[] { pre }.Concat(rule.GetReplaceCandidates(result!, new Passes.RunPassContext()));
        await RunCases($"Theory{number}", feedDict, posts);
    }

    [Fact]
    public async Task TestDynamicMatmulBinaryBinary()
    {
        var dimM = new DimVar("m");
        dimM.Metadata.Range = new(1, 384 * 2);
        var ashape = new long[] { 1, 64, 384, 128 };
        var bshape = new long[] { 1, 64, 128, 384 };
        var aDims = ashape.Select(x => (Dimension)x).ToArray();
        aDims[^2] = dimM;

        var a = new Var("a", new TensorType(DataTypes.Float32, new RankedShape(aDims)));
        CompileOptions.ShapeBucketOptions.VarMap.Add(a, aDims.Select(x => x).ToArray());
        var b = new Var("b", new TensorType(DataTypes.Float32, bshape));
        var c = IR.F.Tensors.MatMul(a, b);
        var dshape = new[] { 1 };
        var d = new Var("d", new TensorType(DataTypes.Float32, dshape));
        var e = IR.F.Math.Binary(BinaryOp.Div, c, d);
        var fshape = new[] { 1, 1, 384, 384 };
        var f = new Var("f", new TensorType(DataTypes.Float32, fshape));
        var g = IR.F.Math.Binary(BinaryOp.Add, e, f);

        var feedDict = new Dictionary<IVar, IValue>() {
            { a, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, ashape).Evaluate() },
            { b, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, bshape).Evaluate() },
            { d, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, dshape).Evaluate() },
            { f, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, fshape).Evaluate() },
            { dimM, Value.FromTensor(ashape[^2]) },
        };

        await RunCases(string.Empty, feedDict, new[] { g });
    }

    [Theory]
    [InlineData(new object[] { BinaryOp.Max, new long[] { 56, 1 }, new long[] { 56, 1 }, new int[] { 0, 2 }, new PostOpKind[] { }, 0 })] // note max(f32[sequence_length,1],f32[sequence_length,1])
    [InlineData(new object[] { BinaryOp.Div, new long[] { 1 }, new long[] { 36, 1 }, new int[] { 1 }, new PostOpKind[] { }, 1 })] // note div(f32[1], f32[sequence_length,1])
    [InlineData(new object[] { BinaryOp.Mul, new long[] { 112, 32 }, new long[] { 112, 1 }, new int[] { 0, 2 }, new PostOpKind[] { }, 2 })] // note mul(f32[sequence_length,32], f32[sequence_length,1])
    [InlineData(new object[] { BinaryOp.Mul, new long[] { 66, 64 }, new long[] { 66, 1 }, new int[] { 0, 2 }, new PostOpKind[] { }, 3 })] // note mul(f32[sequence_length,64], f32[sequence_length,1])
    [InlineData(new object[] { BinaryOp.Mul, new long[] { 15, 64 }, new long[] { 1, 64 }, new int[] { 0 }, new PostOpKind[] { }, 4 })] // note mul(f32[sequence_length,64], const(f32[1,64]))
    [InlineData(new object[] { BinaryOp.Mul, new long[] { 16, 101, 4 }, new long[] { 1, 101, 4 }, new int[] { 1, 4 }, new PostOpKind[] { PostOpKind.MulScalar, PostOpKind.MulScalar }, 5 })] // note mul(f32[16,sequence_length,4], f32[1,sequence_length,4])
    [InlineData(new object[] { BinaryOp.Add, new long[] { 1 }, new long[] { 32, 28 }, new int[] { 2 }, new PostOpKind[] { PostOpKind.MulScalar }, 6 })] // note div(f32[1], f32[32, sequence_length])
    public async Task TestDynamicPackBinary(BinaryOp op, long[] lhsShape, long[] rhsShape, int[] dynamicAxes, PostOpKind[] postOpKinds, int count)
    {
        var dimVar = new DimVar("seq_length")
        {
            Metadata = new()
            {
                Range = new(1, 128),
            },
        };

        var lhsDynShape = new RankedShape(Enumerable.Range(0, lhsShape.Length).Select(i => dynamicAxes.Contains(i) ? dimVar : (Dimension)lhsShape[i]).ToArray());
        var lhs = new Var(new TensorType(DataTypes.Float32, lhsDynShape));
        CompileOptions.ShapeBucketOptions.VarMap.Add(lhs, lhsDynShape.ToArray());
        var rhsDynShape = new RankedShape(Enumerable.Range(0, rhsShape.Length).Select(i => dynamicAxes.Contains(lhsDynShape.Rank + i) ? dimVar : (Dimension)rhsShape[i]).ToArray());
        var rhs = new Var(new TensorType(DataTypes.Float32, rhsDynShape));
        CompileOptions.ShapeBucketOptions.VarMap.Add(rhs, rhsDynShape.ToArray());
        var pre = IR.F.Math.Binary(op, lhs, rhs);
        var rule = new Passes.Rules.NTT.VectorizeBinary(Rank, Lane);
        CompilerServices.TryMatch(pre, rule.Pattern, out var result);
        var posts = new[] { pre }.Concat(rule.GetReplaceCandidates(result!, new Passes.RunPassContext())).Select(
            post =>
            {
                var newPost = Passes.Mutator.Substitute(e =>
                {
                    if (e is not Call { Target: IR.NTT.VectorizedBinary } call)
                    {
                        return e;
                    }

                    var newcall = call;
                    if (postOpKinds.Length > 0)
                    {
                        for (int i = 0; i < postOpKinds.Length; i++)
                        {
                            newcall = postOpKinds[i] switch
                            {
                                PostOpKind.MulScalar => IR.F.Math.Binary(BinaryOp.Mul, newcall, 1.32f),
                                PostOpKind.ScalarDiv => IR.F.Math.Binary(BinaryOp.Div, 0.32f, newcall),
                                _ => throw new NotSupportedException($"Unsupported post operation kind: {postOpKinds[i]}"),
                            };
                        }
                    }

                    return newcall;
                })().Rewrite(post);
                return newPost;
            }).ToArray();

        var feedDict = new Dictionary<IVar, IValue>() {
            { lhs, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, lhsShape).Evaluate() },
            { rhs, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 3, rhsShape).Evaluate() },
            { dimVar, Value.FromTensor(lhsShape.Concat(rhsShape).Skip(dynamicAxes[0]).First()) },
        };

        await RunCases($"Theory{count}", feedDict, posts);
    }

    [Theory]
    [InlineData(new object[] { new long[] { 16, 32 * 8 }, new long[] { 32 * 8, 32 * 4 }, false, true, new[] { 0 }, 0 })] // note const(f32[sequence_length,2048]) @ [2048,4096]
    [InlineData(new object[] { new long[] { 64, 1 }, new long[] { 1, 94 }, true, false, new[] { 3 }, 1 })] // note const(f32[64,1]) @ [1,sequence_length]
    public async Task TestDynamicPackMatMul(long[] lhsShape, long[] rhsShape, bool constA, bool constB, int[] dynamicAxes, int count)
    {
        var dimVar = new DimVar("seq_len")
        {
            Metadata = new()
            {
                Range = new(1, 256),
            },
        };

        var lhsDynShape = new RankedShape(Enumerable.Range(0, lhsShape.Length).Select(i =>
        {
            if (dynamicAxes.Contains(i))
            {
                return dimVar;
            }

            return (Dimension)lhsShape[i];
        }).ToArray());
        var lhsTensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, lhsShape).Evaluate().AsTensor(); // IR.F.Tensors.ConstantOfShape(lhsShape, 1.0f).Evaluate().AsTensor();
        Expr lhs = constA ? lhsTensor : new Var(new TensorType(DataTypes.Float32, lhsDynShape));

        if (!constA)
        {
            CompileOptions.ShapeBucketOptions.VarMap.Add((Var)lhs, lhs.CheckedShape.ToArray());
        }

        var rhsDynShape = new RankedShape(Enumerable.Range(0, rhsShape.Length).Select(i =>
        {
            if (dynamicAxes.Contains(lhsShape.Length + i))
            {
                return dimVar;
            }

            return (Dimension)rhsShape[i];
        }).ToArray());
        var rhsTensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 3, rhsShape).Evaluate().AsTensor(); // IR.F.Tensors.ConstantOfShape(rhsShape, 1.0f).Evaluate().AsTensor();
        Expr rhs = constB ? rhsTensor : new Var(new TensorType(DataTypes.Float32, rhsDynShape));
        if (!constB)
        {
            CompileOptions.ShapeBucketOptions.VarMap.Add((Var)rhs, rhs.CheckedShape.ToArray());
        }

        var pre = IR.F.Tensors.MatMul(lhs, rhs);

        var feedDict = new Dictionary<IVar, IValue>();
        foreach (var axis in dynamicAxes)
        {
            feedDict.Add(dimVar, Value.FromTensor(lhsShape.Concat(rhsShape).Skip(axis).Take(1).First()));
        }

        if (!constA)
        {
            feedDict.Add((Var)lhs, Value.FromTensor(lhsTensor));
        }

        if (!constB)
        {
            feedDict.Add((Var)rhs, Value.FromTensor(rhsTensor));
        }

        var rule = new Passes.Rules.NTT.VectorizeMatMul(2, Lane, transB: true);
        CompilerServices.TryMatch(pre, rule.Pattern, out var result);

        var posts = new[] { pre }.Concat(rule.GetReplaceCandidates(result!, new Passes.RunPassContext()));
        await RunCases($"Theory{count}", feedDict, posts);
    }

    [Theory]
    [MemberData(nameof(TestVectorizeCastData))]

    // [InlineData(new object[] { new long[] { 132, 1024 }, Runtime.TypeCode.BFloat16, Runtime.TypeCode.Float32, new PostOpKind[] { }, new int[] { 0 }, 0 })]
    // [InlineData(new object[] { new long[] { 34, 1024 }, Runtime.TypeCode.Float32, Runtime.TypeCode.BFloat16, new PostOpKind[] { }, new int[] { 0 }, 1 })]
    // [InlineData(new object[] { new long[] { 169, 1024 }, Runtime.TypeCode.BFloat16, Runtime.TypeCode.Float8E4M3, new PostOpKind[] { }, new int[] { 0 }, 2 })]
    // [InlineData(new object[] { new long[] { 64 }, Runtime.TypeCode.Float8E4M3, Runtime.TypeCode.Float32, new PostOpKind[] { PostOpKind.MulScalar }, new int[] { }, 3 })]
    // [InlineData(new object[] { new long[] { 43 /* seq_len */, 16, 256 }, Runtime.TypeCode.Float32, Runtime.TypeCode.BFloat16, new PostOpKind[] { PostOpKind.MulScalar }, new int[] { 0 }, 4 })]
    // [InlineData(new object[] { new long[] { 29 /* seq_len */, 64 }, Runtime.TypeCode.BFloat16, Runtime.TypeCode.Float32, new PostOpKind[] { PostOpKind.MulScalar }, new int[] { 0 }, 5 })]
    public async Task TestVectorizeCast(long[] shape, Runtime.TypeCode type1, Runtime.TypeCode type2, PostOpKind[] postOpKinds, int[] dynamicAxes, int[][] sbps, int count)
    {
        var targetOptions = (NTTTargetOptions)CompileOptions.TargetOptions;
        Expr postOps = None.Default;

        var dynShape = new RankedShape(Enumerable.Range(0, shape.Length).Select(i => dynamicAxes.Contains(i) ? new DimVar($"dim{i}")
        {
            Metadata = new() { Range = new(1, Dimension.AlignUp(shape[i], 256).FixedValue) },
        } : (Dimension)shape[i]).ToArray());
        var input = new Var(new TensorType(DataTypes.Float32, dynShape));
        CompileOptions.ShapeBucketOptions.VarMap.Add(input, dynShape.ToArray());
        var casted1 = IR.F.Tensors.Cast(input, DataType.FromTypeCode(type1));
        var casted2 = IR.F.Tensors.Cast(casted1, DataType.FromTypeCode(type2));
        var rule = new Passes.Rules.NTT.VectorizeCast(1, 128);
        CompilerServices.TryMatchRoot(casted2, rule.Pattern, out var result);
        var posts = new[] { casted2 }.Concat(rule.GetReplaceCandidates(result!, new Passes.RunPassContext())).
            Select(c2 => IR.F.Tensors.Cast(c2, DataTypes.Float32)).
            Select(post =>
        {
            var newPost = Passes.Mutator.Substitute(e =>
                {
                    if (e is not Call { Target: IR.NTT.VectorizedCast } call)
                    {
                        return e;
                    }

                    var newcall = call;
                    if (postOpKinds.Length > 0)
                    {
                        for (int i = 0; i < postOpKinds.Length; i++)
                        {
                            newcall = postOpKinds[i] switch
                            {
                                PostOpKind.MulScalar => IR.F.Math.Binary(BinaryOp.Mul, newcall, Tensor.FromScalar(1.32f).CastElementTo(PrimType.FromTypeCode(type2))),
                                PostOpKind.ScalarDiv => IR.F.Math.Binary(BinaryOp.Div, Tensor.FromScalar(0.32f).CastElementTo(PrimType.FromTypeCode(type2)), newcall),
                                _ => throw new NotSupportedException($"Unsupported post operation kind: {postOpKinds[i]}"),
                            };
                        }
                    }

                    return newcall;
                })().Rewrite(post);
            return newPost;
        });

        if (sbps.Length > 0)
        {
            foreach (var post in posts)
            {
                foreach (var call in ExprCollector.Collect(post).Where(e => e is Call { Target: IR.NTT.VectorizedCast }))
                {
                    call.Metadata = new() { OutputNames = new[] { "call" } };
                }
            }

            var scheme = new Passes.Distributed.DistributedSchema("1", "llama", [new("call", sbps.Select(s => s[0] < 0 ? SBP.B : (SBP)SBP.S(s)).ToArray(), targetOptions.Hierarchies[0], targetOptions.HierarchyNames)]);
            var options = new JsonSerializerOptions();
            options.Converters.Add(new SBPConverter());
            options.WriteIndented = true;
            var export = System.Text.Json.JsonSerializer.Serialize(scheme, options);
            var dumpper = Diagnostics.DumpScope.Current.CreateSubDummper($"Theory{count}");
            targetOptions.DistributedScheme = Path.Join(dumpper.Directory, "schema.json");
            using (var stream = dumpper.OpenFile("schema.json"))
            {
                using (var writer = new StreamWriter(stream))
                {
                    writer.Write(export);
                }
            }
        }

        var feedDict = new Dictionary<IVar, IValue>() {
            { input, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, shape).Evaluate() },
        };

        foreach (var axis in dynamicAxes)
        {
            feedDict.Add((DimVar)dynShape[axis], Value.FromTensor(shape[axis]));
        }

        await RunCases($"Theory{count}", feedDict, posts);
    }

    [Theory]
    [InlineData(new object[] { new long[] { 1, 8, 128 }, new long[] { 1, 128, 64 }, new long[] { 1, -1, 32, 2 }, new[] { UnaryOp.Neg, UnaryOp.Cos }, 0 })]
    public async Task TestDynamicMatMulReshapeUnary(long[] lhsShape, long[] rhsShape, long[] newShape, UnaryOp[] unaryOps, int number)
    {
        var dimM = new DimVar("m");
        dimM.Metadata.Range = new(1, 64);
        var lhsDims = lhsShape.Select(x => (Dimension)x).ToArray();
        lhsDims[^2] = dimM;

        var lhs = new Var(new TensorType(DataTypes.Float32, new RankedShape(lhsDims)));
        CompileOptions.ShapeBucketOptions.VarMap.Add(lhs, lhsDims.Select(x => x).ToArray());
        var rhs = new Var(new TensorType(DataTypes.Float32, rhsShape));
        var matmul = IR.F.Tensors.MatMul(lhs, rhs);
        var reshaped = IR.F.Tensors.Reshape(matmul, newShape);
        var unary = reshaped;
        foreach (var item in unaryOps)
        {
            unary = IR.F.Math.Unary(item, unary);
        }

        var feedDict = new Dictionary<IVar, IValue>()
        {
            { lhs, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 1, lhsShape).Evaluate() },
            { rhs, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, rhsShape).Evaluate() },
            { dimM, Value.FromTensor(lhsShape[^2]) },
        };

        await RunCases($"Theory{number}", feedDict, new[] { unary });
    }

    internal async Task RunCases(string dumpDir, Dictionary<IVar, IValue> feedDict, IEnumerable<BaseExpr> posts, Dictionary<IVar, IValue>? feedDictRT = null)
    {
        var postArray = posts.ToArray();
        using var pinner = new ExprPinner(postArray);
        for (int i = 0; i < postArray.Length; i++)
        {
#if DEBUG
            System.Console.WriteLine(CompilerServices.Print(postArray[i]));
#endif
            var kernelCase = new XpuKernelCase($"Case{i}", new Fusion("kernel", XPUTarget.Kind, postArray[i], feedDict.Keys.ToArray()), feedDict.Keys.ToArray(), feedDict.Values.Select(v => v.AsTensor()).ToArray(), feedDictRT?.Values.Select(v => v.AsTensor()).ToArray() ?? []);
            await Run(dumpDir, kernelCase);
        }
    }

    internal async Task Run(string dumpDir, XpuKernelCase kernelCase)
    {
        using var dumpScope = new Diagnostics.DumpScope(Path.Join(dumpDir, kernelCase.Name), CompileOptions.DumpFlags);

        // convert fusion to prim func
        var fusion = kernelCase.Fusion;
        if (fusion.Body.CheckedType is InvalidType)
        {
            return;
        }

        var main = new Function(fusion.Body, kernelCase.Vars.ToArray());
        main.Metadata = fusion.Body.Metadata;

        var module = new IR.IRModule(main);
        var inputs = kernelCase.Inputs.ToArray();
        var outputs = fusion.Body.Evaluate(kernelCase.Vars.Zip(inputs).ToDictionary(p => p.First, p => (IValue)Value.FromTensor(p.Second))).AsTensors();

#if DEBUG
        for (var i = 0; i < inputs.Length; i++)
        {
            using (var fs = Diagnostics.DumpScope.Current.OpenFile($"input_{i}.json"))
            {
                JsonSerializer.Serialize(fs, inputs[i], JsonSerializerOptions.Default);
            }
        }

        for (int i = 0; i < outputs.Length; i++)
        {
            using (var fs = Diagnostics.DumpScope.Current.OpenFile($"output_{i}.json"))
            {
                JsonSerializer.Serialize(fs, outputs[i], JsonSerializerOptions.Default);
            }
        }
#endif
        await Compile(module);
        var (kmodel_path, _) = Testing.BuildKModel("test", module, CompileSession, false);
        if (CodeGen.XPU.CSourceUtilities.GetRuntimeMode() == CodeGen.XPU.RuntimeMode.SystemMode)
        {
            return;
        }

        Tensor[] actuals;
        if (kernelCase.RTInputs.Any())
        {
            actuals = Testing.RunKModel(kmodel_path, Diagnostics.DumpScope.Current.Directory, kernelCase.RTInputs.ToArray()).AsTensors();
        }
        else
        {
            actuals = Testing.RunKModel(kmodel_path, Diagnostics.DumpScope.Current.Directory, inputs).AsTensors();
        }
#if DEBUG
        for (int i = 0; i < actuals.Length; i++)
        {
            using (var fs = Diagnostics.DumpScope.Current.OpenFile($"actual_{i}.json"))
            {
                JsonSerializer.Serialize(fs, actuals[i], JsonSerializerOptions.Default);
            }
        }
#endif
        for (int i = 0; i < outputs.Length; i++)
        {
            var cos = Comparator.CosSimilarity(outputs[i], actuals[i]);
            Assert.True(cos > 0.999, $"the {CompileOptions.DumpDir} output {i} cos: {cos} ");
        }
    }

    private async Task Compile(IRModule module)
    {
        var pmgr = CompileSession.CreatePassManager("pmgr");
        var compiler = (Nncase.Compiler.Compiler)CompileSession.Compiler;
        compiler.TargetIndependentPass(pmgr);
        pmgr.Add<Passes.XPUModuleConvertPass>(XPUTarget.Kind);
        CompileSession.Target.RegisterPostAutoVectorizePass(pmgr, CompileSession.CompileOptions);
        compiler.AutoDistributedPass(pmgr);
        compiler.AutoTilingPass(pmgr);
        compiler.TIRPass(pmgr);
        await pmgr.RunAsync(module);
    }
}

public sealed class TestPagedAttentionWithKVCacheData : TheoryData<PagedAttentionKVCacheTestFixture, bool, bool, int>
{
    // note in xpu, seq need > 8.
    private static readonly (string, long[] QueryLens, long[] SeqLens)[] TestScenarios = [
        ("prefill", [8L], [8L]), // query % 8 == 0, for split on x.
        ("decode", [1L], [8L + 1L]), // query % 8 == 0, for split on x.
        ("extend", [32L], [32L + 16L]),
        ("prefill", [128L, 256L], [128L, 256L]),
        ("prefill+extend", [64L, 192L], [64L, 192L + 8L]),
        ("prefill+decode", [16L, 1L], [16L, 1L + 128L]),
    ];

    private static readonly Runtime.TypeCode[] TypeConfigs = [

        // Runtime.TypeCode.Float32,
        Runtime.TypeCode.Float16,
    ];

    // note in xpu, head need > 8.
    private static readonly (ModelName, int NumQ, int NumKV, int Dim)[] HeadConfigs =
    [

        // (ModelName.Llama3_2_8B, 32, 8, 128),
        (ModelName.Llama3_3_70B, 64, 8, 128),

        // (ModelName.Llama3_1_405B, 128, 8, 128),
        // (ModelName.Qwen2_5_7B, 28, 4, 128),
        // (ModelName.Qwen2_5_32B, 40, 8, 128),
        // (ModelName.Qwen2_5_72B, 64, 8, 128),
        // (ModelName.Qwen3_8B, 32, 8, 128),
        // (ModelName.Qwen3_14B, 40, 8, 128),
        (ModelName.Qwen3_32B, 64, 8, 128),

        // (ModelName.Qwen3_235B, 64, 4, 128),
    ];

    // note in xpu, blocks need > 32.
    private static readonly (int Layer, int BlockSize, int NumBlocks)[] CacheConfigs = [
        (1, 256, 32),
        (1, 128, 32),

        // (1, 32, 32),
        // (1, 16, 8),
        // (1, 32, 16),
    ];

    // xpu require head first!.
    private static readonly (PagedKVCacheDimKind[] Cache, PagedKVCacheDimKind[] Packed)[] LayoutConfigs =
    [
        (new[] {
            PagedKVCacheDimKind.NumBlocks,
            PagedKVCacheDimKind.NumLayers,
            PagedKVCacheDimKind.NumKVHeads,
            PagedKVCacheDimKind.KV,
            PagedKVCacheDimKind.HeadDim,
            PagedKVCacheDimKind.BlockSize,
         },
         new[] { PagedKVCacheDimKind.HeadDim }),
    ];

    private static readonly (PagedKVCacheDimKind[] Sharding, SBPSplit[] Policies)[] ShardingConfigs =
    [
        ([PagedKVCacheDimKind.NumKVHeads, PagedKVCacheDimKind.NumBlocks], [SBP.S(1), SBP.S(2, 3)]),
    ];

    private static readonly (AttentionDimKind[] QLayout, AttentionDimKind[] KLayout)[] QKLayoutConfigs =
    [
        ([AttentionDimKind.Head, AttentionDimKind.Dim, AttentionDimKind.Seq],
         [AttentionDimKind.Head, AttentionDimKind.Dim, AttentionDimKind.Seq]),
    ];

    public TestPagedAttentionWithKVCacheData()
    {
        int count = 0;
        foreach (var (_, queryLens, seqLens) in TestScenarios)
        {
            foreach (var (_, numQHeads, numKVHeads, headDim) in HeadConfigs)
            {
                foreach (var (numLayer, blockSize, numBlocks) in CacheConfigs)
                {
                    foreach (var typeCode in TypeConfigs)
                    {
                        foreach (var (cacheLayout, packedAxes) in LayoutConfigs)
                        {
                            foreach (var (shardingAxes, axisPolicies) in ShardingConfigs)
                            {
                                foreach (var (qlayout, klayout) in QKLayoutConfigs)
                                {
                                    // note must dynamic shape, fixed shape can't split on seq
                                    Add(new PagedAttentionKVCacheTestFixture(queryLens, seqLens, numQHeads, numKVHeads, headDim, blockSize, numBlocks, typeCode, numLayer, cacheLayout, packedAxes, shardingAxes, axisPolicies, qlayout, klayout), true, true /* currently our split method is not compatible with plugin */, count++);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private enum ModelName
    {
        Llama3_2_8B, // 32 8
        Llama3_3_70B, // 64 8
        Llama3_1_405B, // 128 8
        Qwen2_5_7B, // 28 4
        Qwen2_5_32B, // 40 8
        Qwen2_5_72B, // 64 8
        Qwen3_8B, // 32 8
        Qwen3_14B, // 40 8
        Qwen3_32B, // 64 8
        Qwen3_235B, // 64 4
    }
}
