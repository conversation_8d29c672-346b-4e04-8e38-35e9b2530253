/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "nncase/python/common/pytype_utils.h"
#include "nncase/python/common/type_casters.h"
#include <nncase/python/common/runtime_llm_ffi.h>
#include <nncase/python/common/runtime_tensor.h>
#include <nncase/runtime/interpreter.h>
#include <nncase/runtime/runtime_op_utility.h>
#include <nncase/version.h>
#include <pybind11/iostream.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/stl_bind.h>
#include <runtime_tensor_duca.h>

namespace py = pybind11;
using namespace nncase;
using namespace nncase::runtime;

namespace
{
#ifdef WIN32
#include <Windows.h>
void LaunchDebugger()
{
    // Get System directory, typically c:\windows\system32
    std::wstring systemDir(MAX_PATH + 1, '\0');
    UINT nChars = GetSystemDirectoryW(&systemDir[0], systemDir.length());
    if (nChars == 0)
        return;
    systemDir.resize(nChars);

    // Get process ID and create the command line
    DWORD pid = GetCurrentProcessId();
    std::wostringstream s;
    s << systemDir << L"\\vsjitdebugger.exe -p " << pid;
    std::wstring cmdLine = s.str();

    // Start debugger process
    STARTUPINFOW si;
    ZeroMemory(&si, sizeof(si));
    si.cb = sizeof(si);

    PROCESS_INFORMATION pi;
    ZeroMemory(&pi, sizeof(pi));

    if (!CreateProcessW(NULL, &cmdLine[0], NULL, NULL, FALSE, 0, NULL, NULL,
            &si, &pi))
        return;

    // Close debugger process handles to eliminate resource leak
    CloseHandle(pi.hThread);
    CloseHandle(pi.hProcess);

    // Wait for the debugger to attach
    while (!IsDebuggerPresent())
        Sleep(100);
}
#endif
} // namespace

PYBIND11_MODULE(_nncaseruntime, m)
{
    m.doc() = "nncase runtime Library";
    m.attr("__version__") = NNCASE_VERSION;

    // LaunchDebugger();

    py::class_<runtime_function>(m, "RuntimeFunction")
        .def("parameters_size", &runtime_function::parameters_size)
        .def("parameter_type", &runtime_function::parameter_type)
        .def("return_size", &runtime_function::return_size)
        .def("return_type",
            py::overload_cast<>(&runtime_function::return_type, py::const_))
        .def("return_type", py::overload_cast<size_t>(&runtime_function::return_type, py::const_))
        .def("invoke", [](runtime_function &func, std::vector<runtime_tensor> parameters)
            {
            std::vector<value_t> param_values;
            param_values.reserve(parameters.size());

            for (auto param : parameters) {
                param_values.push_back(param.impl());
            }

            auto results = std::vector<runtime_tensor>();
            auto ret = func.invoke(param_values).unwrap_or_throw();
            if (ret.is_a<tensor>()) {
                results.push_back(
                    runtime_tensor(ret.as<tensor>().unwrap_or_throw()));
            } else {
                auto tuple_ret = ret.as<tuple>().unwrap_or_throw();
                for (size_t i = 0; i < tuple_ret->fields().size(); i++) {
                    results.push_back(runtime_tensor(
                        tuple_ret->fields()[i].as<tensor>().unwrap_or_throw()));
                }
            }
            return results; });

    py::class_<interpreter>(m, "Interpreter")
        .def(py::init())
        .def("load_model",
            [](interpreter &interp, std::span<const std::byte> buffer)
            {
                interp.load_model(buffer, false).unwrap_or_throw();
            })
        .def("load_model",
            [](interpreter &interp, nncase::runtime::stream &stream)
            {
                interp.load_model(stream).unwrap_or_throw();
            })
        .def_property_readonly("inputs_size", &interpreter::inputs_size)
        .def_property_readonly("outputs_size", &interpreter::outputs_size)
        .def("get_input_desc", &interpreter::input_desc)
        .def("get_output_desc", &interpreter::output_desc)
        .def("get_input_shape",
            [](interpreter &interp, size_t index)
            {
                auto shape = interp.input_shape(index);
                return std::vector<py::ssize_t>(shape.begin(), shape.end());
            })
        .def("get_output_shape",
            [](interpreter &interp, size_t index)
            {
                auto shape = interp.output_shape(index);
                return std::vector<py::ssize_t>(shape.begin(), shape.end());
            })
        .def("get_input_tensor",
            [](interpreter &interp, size_t index)
            {
                return interp.input_tensor(index).unwrap_or_throw();
            })
        .def("set_input_tensor",
            [](interpreter &interp, size_t index, runtime_tensor tensor)
            {
                return interp.input_tensor(index, tensor).unwrap_or_throw();
            })
        .def("get_output_tensor",
            [](interpreter &interp, size_t index)
            {
                return interp.output_tensor(index).unwrap_or_throw();
            })
        .def("set_output_tensor",
            [](interpreter &interp, size_t index, runtime_tensor tensor)
            {
                return interp.output_tensor(index, tensor).unwrap_or_throw();
            })
        .def("enable_profiling",
            [](interpreter &interp, uint8_t enable_profiling)
            {
                interp.enable_profiling(enable_profiling);
            })
        .def("get_native_handle_by_module_kind",
             [](interpreter &interp, std::string_view kind, uint32_t flags) {
                 auto module =
                     interp.find_module_by_kind(kind).unwrap_or_throw();
                 return module->native_handle(flags).unwrap_or_throw();
             })
        .def("run",
            [](interpreter &interp)
            { interp.run().unwrap_or_throw(); })
        .def("entry_function", [](interpreter &interp)
            { return interp.entry_function().unwrap_or_throw(); }, py::return_value_policy::reference_internal)
        .def("options", &interpreter::options, py::return_value_policy::reference_internal, "Get interpreter options dictionary");

    py::class_<options_dict>(m, "OptionsDict").def("set_stream", [](options_dict &opts, int64_t stream_handle_int)
                                                  { opts.set("stream", stream_handle_int); })
        .def("get_stream", [](options_dict &opts)
            {
                auto result = opts.get_scalar_opt<int64_t>("stream");
                if (!result.is_ok()){
                  throw std::runtime_error("Stream not set in options dictionary");
                }
                return result.unwrap(); })
        .def("set_xpu_external_module_path", [](options_dict &opts, const std::string &path)
            { opts.set("xpu_external_module_path", path); });

    auto rt_class = register_runtime_tensor(m);
    register_runtime_tensor_duca(rt_class);
    register_runtime_llm_ffi(m);
}
