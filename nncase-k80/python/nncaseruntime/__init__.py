# Copyright 2019-2021 Canaan Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from _nncaseruntime import *
from typing import List, Optional, Any
import numpy as np


class RuntimeModuleOptions:
  pass


class DucaRuntimeModuleOptions(RuntimeModuleOptions):
  def __init__(self, streams: List['duca.stream']):
    self.streams = streams


class AutoModelForCausalLM:
  """Auto model for causal language modeling."""

  def __init__(self, model_file: str, config: Any = None, cache_config: Any = None, parallel_config: Any = None, runtime_module_options: Optional[RuntimeModuleOptions] = None):
    """Initialize the model with configuration.

    Args:
        model_file: Path to the model file
        config: Model configuration
        cache_config: Paged attention cache configuration
        parallel_config: Parallel execution configuration
        runtime_module_options: Runtime module options
    """
    self._interpreter = Interpreter()
    if not isinstance(runtime_module_options, DucaRuntimeModuleOptions):
      raise RuntimeError('runtime_module_options must be DucaRuntimeModuleOptions')
    if len(runtime_module_options.streams) != 1:
      raise RuntimeError('only support single stream for DucaRuntimeModuleOptions')
    self._interpreter.options().set_stream(runtime_module_options.streams[0].Stream.handle_int())
    if hasattr(runtime_module_options, 'xpu_external_module_path'):
      self._interpreter.options().set_xpu_external_module_path(runtime_module_options.xpu_external_module_path)

    with open(model_file, 'rb') as f:
      model_data = f.read()
    self._interpreter.load_model(model_data)
    self._entry_function = self._interpreter.entry_function()

    self.config = config
    self.cache_config = cache_config
    self.parallel_config = parallel_config

  @property
  def interpreter(self) -> Interpreter:
    """Get the underlying interpreter.

    Returns:
        The interpreter instance
    """
    return self._interpreter

  def __call__(self,
               input_ids: Any,
               attention_mask: Optional[Any] = None,
               positions: Optional[Any] = None,
               kv_cache: Optional['DucaPagedAttentionKVCacheV1'] = None,
               dp_rank: int = 0) -> List[RuntimeTensor]:
    """Forward pass of the model.

    Args:
        input_ids: Input token ids
        attention_mask: Attention mask (optional)
        positions: Position embeddings (optional)
        kv_cache: KV cache for attention (optional)
        dp_rank: Data parallel rank

    Returns:
        Model output tensor
    """
    # Prepare input tensors
    inputs = []

    # Convert input_ids to RuntimeTensor if needed
    if not isinstance(input_ids, RuntimeTensor):
      raise RuntimeError('only support runtime tensor input.')
    else:
      inputs.append(input_ids)

    # Add positions if provided
    if positions is not None:
      raise NotImplementedError('not support positions')

    # Add kv_cache if provided
    if attention_mask is not None:
      raise NotImplementedError('not support attention_mask')

    if isinstance(kv_cache, PagedAttentionKVCache):
      inputs.append(RuntimeTensor.from_object(kv_cache))
    elif isinstance(kv_cache, RuntimeTensor):
      inputs.append(kv_cache)
    else:
      raise RuntimeError('only support PagedAttentionKVCache as kv_cache')

    # Invoke the model
    outputs = self._entry_function.invoke(inputs)

    return outputs


class DucaPagedAttentionKVCacheV1:
  """Duca-specific paged attention KV cache implementation."""

  def __init__(self):
    self._kv_cache_obj = PagedAttentionKVCache()
    self.num_decode_tokens: int = 0
    self.num_prefill_tokens: int = 0
    self.num_prefills: int = 0
    self.num_requests: int = 0
    self._saved_references = {}

  @property
  def context_lens(self) -> RuntimeTensor:
    return self._kv_cache_obj.context_lens

  @context_lens.setter
  def context_lens(self, value: RuntimeTensor):
    self._kv_cache_obj.context_lens = value
    self._saved_references['context_lens'] = value

  @property
  def seq_lens(self) -> RuntimeTensor:
    return self._kv_cache_obj.seq_lens

  @seq_lens.setter
  def seq_lens(self, value: RuntimeTensor):
    self._kv_cache_obj.seq_lens = value
    self._saved_references['seq_lens'] = value

  @property
  def block_tables(self) -> RuntimeTensor:
    return self._kv_cache_obj.block_tables

  @block_tables.setter
  def block_tables(self, value: RuntimeTensor):
    self._kv_cache_obj.block_tables = value
    self._saved_references['block_tables'] = value

  @property
  def slot_mapping(self) -> RuntimeTensor:
    return self._kv_cache_obj.slot_mapping

  @slot_mapping.setter
  def slot_mapping(self, value: RuntimeTensor):
    self._kv_cache_obj.slot_mapping = value
    self._saved_references['slot_mapping'] = value

  @property
  def num_blocks(self) -> int:
    return self._kv_cache_obj.num_blocks

  @num_blocks.setter
  def num_blocks(self, value: int):
    self._kv_cache_obj.num_blocks = value

  @property
  def kv_caches(self) -> List[List['duca.gpuarray.GPUArray']]:
    raise NotImplementedError('not support get kv_caches currently.')

  @kv_caches.setter
  def kv_caches(self, values: List[List['duca.gpuarray.GPUArray']]):
    if not isinstance(values, list):
      raise RuntimeError('kv_caches must be a [tp_size,core_size] nested list of duca tensor')
    if len(values) != 1:
      raise RuntimeError('not support multi-tp kv_caches currently')
    kv_cache_tensors = []
    for value in values:
      if not isinstance(value, list):
        raise RuntimeError('kv_caches must be a [tp_size,core_size] nested list of duca tensor')
      numa_addrs = []
      for tensor in value:
        if tensor.mem_type.value != 4:
          raise RuntimeError('The duca tensor is not a numa tensor!')
        numa_addrs.append(tensor.addr)
      addrs_tensor = RuntimeTensor.from_numpy(np.array(numa_addrs, dtype=np.int64))
      kv_cache_tensors.append(addrs_tensor)
    self._kv_cache_obj.kv_caches = kv_cache_tensors

  def to_runtime_tensor(self):
    self._kv_cache_obj.num_seqs = self.num_requests
    self._kv_cache_obj.num_tokens = self.num_decode_tokens + self.num_prefill_tokens
    return RuntimeTensor.from_object(self._kv_cache_obj)


# Export the new classes
__all__ = ['AutoModelForCausalLM', 'DucaPagedAttentionKVCacheV1',
           'RuntimeModuleOptions', 'DucaRuntimeModuleOptions',
           'RuntimeTensor', 'Interpreter']
