﻿cmake_minimum_required(VERSION 3.13)

if(NOT DEFINED NNCASE_K510_VERSION)
    set(NNCASE_K510_VERSION "3.6.0")
endif()

if(DEFINED ENV{NNCASE_K510_VERSION_SUFFIX})
    set(NNCASE_K510_VERSION_SUFFIX $ENV{NNCASE_K510_VERSION_SUFFIX})
endif()

if(NOT DEFINED NNCASE_K510_VERSION_SUFFIX)
    find_package (Git)
    execute_process(
        COMMAND ${GIT_EXECUTABLE} describe --always --dirty
        WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
        OUTPUT_VARIABLE GIT_DESC
        OUTPUT_STRIP_TRAILING_WHITESPACE)
    set(NNCASE_K510_VERSION_SUFFIX "-${GIT_DESC}")
endif()

if (NOT PACKAGE_VERSION)
    set(PACKAGE_VERSION
        "${NNCASE_K510_VERSION}${NNCASE_K510_VERSION_SUFFIX}")
endif()

project(nncase-k510
  VERSION ${NNCASE_K510_VERSION}
  LANGUAGES C CXX ASM)

option(ENABLE_OPENMP "openmp support" ON)
option(ENABLE_HALIDE "halide kernels support" OFF)
option(BUILD_PYTHON_BINDING "Build python binding" ON)
option(BUILD_TESTING "Build test programs" OFF)
option(ENABLE_DUMP_MEM "Dump mem usage" OFF)

if (BUILDING_RUNTIME)
    option(ENABLE_VULKAN_RUNTIME "Enable Vulkan runtime" OFF)
    option(ENABLE_K510_RUNTIME "Enable k510 runtime" OFF)
    option(ENABLE_K510_RUNTIME_TRACE "Enable k510 runtime tracing" OFF)
    option(ENABLE_K230_RUNTIME_TRACE "Enable k230 runtime tracing" OFF)
endif()

if(ENABLE_DUMP_MEM)
    add_definitions(-DDUMP_MEM)
endif()

include(cmake/dependencies.cmake)

if (BUILDING_RUNTIME)
    find_package(nncaseruntime REQUIRED)
    set_property(GLOBAL PROPERTY POSITION_INDEPENDENT_CODE ON)

    if (MSVC)
        add_definitions(/D_CRT_SECURE_NO_WARNINGS /DNOMINMAX)
        add_compile_options(/wd4267 /wd4251 /FC /utf-8 /W3 /WX)
    else()
        add_compile_options(-Wall -Wextra -pedantic -Werror -Wno-multichar -Wno-missing-field-initializers -Wno-unused-function -Wno-type-limits)
        if (APPLE)
            add_compile_options(-Wno-four-char-constants -Wno-sometimes-uninitialized)
        elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
            add_compile_options(-Wno-uninitialized)
        else ()
            add_definitions(-D_GLIBCXX_USE_CXX11_ABI=1)
            add_compile_options(-Wno-maybe-uninitialized)
        endif()
    endif()

    # Modules
    add_subdirectory(modules/k510)
    add_subdirectory(modules/k230)
    if ((NOT MSVC) AND ENABLE_XPU_RUNTIME)
        add_subdirectory(modules/xpu)
        if(BUILD_PYTHON_BINDING)
            add_subdirectory(python/nncaseruntime)
        endif()
    endif()

else()
    find_package(nncase REQUIRED)
    set(CMAKE_SKIP_RPATH OFF)
    set_property(GLOBAL PROPERTY POSITION_INDEPENDENT_CODE ON)
    if (APPLE)
        set(CMAKE_MACOSX_RPATH TRUE)
        set(CMAKE_INSTALL_RPATH "@loader_path")
        set(CMAKE_INSTALL_NAME_DIR "@rpath")
    else ()
        set(CMAKE_INSTALL_RPATH "$ORIGIN")
    endif()

    if (MSVC)
        add_definitions(/D_SILENCE_ALL_CXX17_DEPRECATION_WARNINGS /D_CRT_SECURE_NO_WARNINGS /DNOMINMAX)
        add_compile_options(/wd4267 /wd4251 /FC /utf-8 /W3 /WX)
        set(PYBIND11_CPP_STANDARD "/std:c++latest")
    else()
        add_compile_options(-Wall -Wextra -pedantic -Werror -Wno-multichar -Wno-missing-field-initializers -Wno-unused-function -Wno-type-limits -Wno-unused-local-typedefs -Wno-sign-compare)
        if (APPLE)
            add_compile_options(-Wno-four-char-constants -Wno-sometimes-uninitialized)
        elseif (CMAKE_CXX_COMPILER_ID STREQUAL "Clang")
            add_compile_options(-Wno-uninitialized)
        else ()
            if (CMAKE_CXX_COMPILER_VERSION GREATER_EQUAL "12.0")
                add_compile_options(-Wno-uninitialized -Wno-stringop-overflow -Wno-array-bounds -Wno-use-after-free)
            endif()
            add_definitions(-D_GLIBCXX_USE_CXX11_ABI=1)
            add_compile_options(-Wno-maybe-uninitialized)
        endif()
    endif()

    if (BUILD_TESTING)
        #enable_testing()
    endif()

    # Modules
    # add_subdirectory(modules/k510)
    # add_subdirectory(modules/k230)
    if (NOT MSVC)
        add_subdirectory(modules/xpu)
    endif()
endif()
