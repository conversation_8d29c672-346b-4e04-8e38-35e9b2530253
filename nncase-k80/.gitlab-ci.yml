variables:
  NNCASE_BRANCH: dev/3.0
  NNCASE_COMMIT: dev/3.0
  SOCKS_PROXY: socks5://**********:1080
  GITLAB_RUNNER_ROOT: /compiler/gitlab-runner
  SDK_DOCKER_IMAGE: sw-docker.a-bug.org:5000/sdk/duca:v0.3.3

stages:
- build
- test

linux build python job:
  except:
    - schedules
  tags:
    - nncase-wheel
    - linux
  stage: build
  script:
    - cat /etc/issue
    - source ~/.bashrc
    - export DOTNET_NUGET_SIGNATURE_VERIFICATION=false
    - export DOTNET_ROOT=${GITLAB_RUNNER_ROOT}/dotnet/dotnet-sdk-8.0.111
    - export PATH=${DOTNET_ROOT}:$PATH
    - mkdir -p $(pwd)/nuget
    - cp -r ${GITLAB_RUNNER_ROOT}/nuget/packages_ubuntu22.04 $(pwd)/nuget/packages
    - export NUGET_PACKAGES=$(pwd)/nuget/packages
    - pip3 config set global.index-url https://mirrors.tuna.tsinghua.edu.cn/pypi/web/simple
    - pip3 install --upgrade pip
    - pip3 --default-timeout=1000 install "conan==2.6.0" cibuildwheel
    - cp -r ${GITLAB_RUNNER_ROOT}/conan2/conan2_ubuntu22.04 conan2
    - export CONAN_HOME=$(pwd)/conan2
    - rm -rf ../nncase
    # - cp -r ${GITLAB_RUNNER_ROOT}/nncase ..
    - git clone https://github.com/kendryte/nncase.git ../nncase -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd ../nncase
    - git fetch origin $NNCASE_BRANCH
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT
    - until dotnet restore -r linux-x64; do :; done
    - dotnet publish src/Nncase.Compiler -c Release --no-restore --sc false -r linux-x64
    - cd -
    - until dotnet restore -r linux-x64; do :; done
    - dotnet publish modules/Nncase.Modules.XPU -c Release --no-restore --sc false -r linux-x64
    - mv ../nncase .
    - /home/<USER>/.local/bin/cibuildwheel --output-dir k80-wheelhouse-linux --platform linux
  artifacts:
    paths:
      - k80-wheelhouse-linux
  timeout: 1h

linux runtime job:
  except:
    - schedules
  tags:
    - nncase-wheel
    - linux
  stage: build
  image:
    name: compilerteamer/ubuntu24.04:v0.6
    pull_policy: if-not-present
  script:
    # 0. setup
    - source ~/python310_venv/bin/activate
    - df -h
    - ls -l ${GITLAB_RUNNER_ROOT}
    - cp -r ${GITLAB_RUNNER_ROOT}/conan2/conan2_ubuntu24.04 conan2
    - export CONAN_HOME=$(pwd)/conan2
    - conan remote add sunnycase https://conan.sunnycase.moe --index 0 --force
    - x86_64_gcc=${GITLAB_RUNNER_ROOT}/toolchain/x86_64_gcc_14.2.0
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14
    - k80=`pwd`
    - curl -# -L -o duca.tgz http://sw-file.a-bug.org/k80/common/sdk/duca/release/${DUCA_VERSION}
    - mkdir -p duca
    - tar xzf duca.tgz -C duca
    - runtime=${k80}/xpu_runtime
    - build=runtime/build/Release

    # 1. nncase
    - rm -rf ../nncase
    - git clone https://github.com/kendryte/nncase.git ../nncase -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd ../nncase
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT
    - rm -rf CMakeUserPresets.json runtime
    - |
      conan install . -of runtime --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DCMAKE_TOOLCHAIN_FILE=generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DENABLE_OP_PROFILE=0 \
            -S . \
            -B ${build}
    - cmake --build ${build} -j32
    - cmake --install ${build} --prefix=${runtime}
    - cd -

    # 2. nncase-k80
    - rm -rf CMakeUserPresets.json runtime
    - |
      conan install . -of runtime --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DCMAKE_TOOLCHAIN_FILE=generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DENABLE_OP_PROFILE=0 \
            -DENABLE_XPU_RUNTIME=1 \
            -DDISABLE_RVV=0 \
            -Dnncaseruntime_DIR=${runtime}/lib/cmake/nncaseruntime/ \
            -DDUCA_PATH=${k80}/duca/output/qemu/duca \
            -S . \
            -B ${build}
    - cmake --build ${build} -j32
    - cmake --install ${build} --prefix=${runtime}
    - mkdir -p k80-wheelhouse-runtime-linux
    - cp ${build}/modules/xpu/examples/xpu_test/xpu_test.elf k80-wheelhouse-runtime-linux

    # 3. runtime python wheel
    - pip install GitPython wheel
    - export NNCASE_RUNTIME_LIB=${runtime}
    - cd python
    - python3 setup.py bdist_wheel
    - cd -
    - cp python/dist/*.whl k80-wheelhouse-runtime-linux
  artifacts:
    paths:
      - k80-wheelhouse-runtime-linux
  timeout: 1h

linux test job:
  except:
    - schedules
  tags:
    - nncase
  stage: test
  image:
    name: compilerteamer/ubuntu24.04:v0.6
    pull_policy: if-not-present
  script:
    - source ~/python311_venv/bin/activate
    - conan remote add sunnycase https://conan.sunnycase.moe --index 0 --force
    - cp -r ${GITLAB_RUNNER_ROOT}/conan2/conan2_ubuntu24.04 $(pwd)/conan2
    - export CONAN_HOME=$(pwd)/conan2
    - x86_64_gcc=${GITLAB_RUNNER_ROOT}/toolchain/x86_64_gcc_14.2.0
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14
    - export DOTNET_ROOT=${GITLAB_RUNNER_ROOT}/dotnet/dotnet-sdk-8.0.111
    - export PATH=${DOTNET_ROOT}:$PATH
    - mkdir -p $(pwd)/nuget/
    - cp -r ${GITLAB_RUNNER_ROOT}/nuget/packages_ubuntu24.04 $(pwd)/nuget/packages
    - export NUGET_PACKAGES=$(pwd)/nuget/packages

    # 1. compile nncase native & export the nncase native lib
    - rm -rf ../nncase
    - git clone https://github.com/kendryte/nncase.git ../nncase -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    # - cp -r ${GITLAB_RUNNER_ROOT}/nncase ..
    - cd ../nncase
    - git fetch origin $NNCASE_BRANCH
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT
    - nncase_install=`pwd`/install
    - rm -rf CMakeUserPresets.json build ${nncase_install}
    - |
      conan install . --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release -j8
    - cmake --install build/Release --prefix=${nncase_install}
    - cd -

    # 2. compile nncase-k80 native and exports native libs
    - rm -rf CMakeUserPresets.json build
    - |
      conan install . --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja \
                      -o "&:nncase_dir=${nncase_install}/lib/cmake/nncase" -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release -j8
    - cmake --install build/Release --prefix=${nncase_install}

    # 3. dotnet test
    - export LD_LIBRARY_PATH="${nncase_install}/lib:${LD_LIBRARY_PATH}"
    - export DYLD_LIBRARY_PATH="${nncase_install}/lib:${DYLD_LIBRARY_PATH}"
    - export NNCASE_TILING_MAX_SOLUTIONS=1
    - export SOLVE_MAX_TIME=1800
    - until dotnet restore; do :; done
    - dotnet build -c Release
    - dotnet test -c Release --filter=UnitTestXPUTargetTiling --verbosity normal --blame
    - dotnet test -c Release --filter=UnitTestXPUKernels --verbosity normal --blame
  timeout: 3h

benchmark ntt job:
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "k80_benchmark_ntt"'
  tags:
    - nncase_v3_benchmark
  stage: test
  image:
    name: compilerteamer/ubuntu24.04:v0.6
    pull_policy: if-not-present
  script:
    - source ~/python311_venv/bin/activate
    - pip --default-timeout=1000 install confluence.md==0.2.3
    # - NNCASE_BRANCH=feature/ntt_benchmark_roofline_6
    # - NNCASE_COMMIT=feature/ntt_benchmark_roofline_6
    - K510_DIR=`pwd`
    - build=build
    - build_type=Release
    - cd ..

    # 1. nncase
    - rm -rf nncase
    - git clone https://github.com/kendryte/nncase.git -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd nncase
    - git reset --hard origin/$NNCASE_BRANCH
    - git checkout $NNCASE_COMMIT

    # 2. x86_64 build
    - x86_64_gcc=${GITLAB_RUNNER_ROOT}/toolchain/x86_64_gcc_14.2.0
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14
    - conan install . --build=missing -s build_type=${build_type} -pr:a=toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=False" -o "&:BUILD_PYTHON_BINDING=False"
    - cmake --preset conan-runtime-release
    - cmake --build ${build}/${build_type} --config ${build_type}
    - cmake --install ${build}/${build_type} --prefix install
    - mv ${build}/${build_type} x86_64_build
    - rm -rf ${build}

    # 3. riscv64 build
    - export RISCV_ROOT_PATH=${GITLAB_RUNNER_ROOT}/toolchain/riscv64-unknown-linux_gnu_14.2.0
    - export CC=${RISCV_ROOT_PATH}/bin/riscv64-unknown-linux-gnu-gcc
    - export CXX=${RISCV_ROOT_PATH}/bin/riscv64-unknown-linux-gnu-g++
    - conan install . --build=missing -s build_type=${build_type} -pr:h=toolchains/riscv64-linux.profile.jinja -pr:b=toolchains/x86_64-linux.profile.jinja -o "&:runtime=True" -o "&:python=False" -o "&:BUILD_PYTHON_BINDING=False" -o "&:k230_runtime=True"
    - cmake --preset conan-runtime-release
    - cmake --build ${build}/${build_type} --config ${build_type}
    - cmake --install ${build}/${build_type} --prefix install
    - mv ${build}/${build_type} riscv64_build
    - rm -rf ${build}

    # 4. benchmark test
    - export CI=True
    - export KPU_TARGETS=k230
    - export NUC_PROXY_IP='**************'
    - export NUC_PROXY_PORT=10002
    - export BENCHMARK_NTT_REPORT_FILE=benchmark_ntt_report_`date "+%Y_%m_%d_%H_%M_%S"`.md
    - export BENCHMARK_NTT_MATMUL_X86_64_REPORT_FILE=benchmark_ntt_matmul_x86_64_report_`date "+%Y_%m_%d_%H_%M_%S"`.md
    - export BENCHMARK_NTT_MATMUL_RISCV64_REPORT_FILE=benchmark_ntt_matmul_riscv64_report_`date "+%Y_%m_%d_%H_%M_%S"`.md
    - python3 ntt/test/benchmark_test/benchmark_ntt.py --x86_64_path x86_64_build/bin/ --riscv64_target k230 --riscv64_path riscv64_build/bin/
    - echo -e "\n### 预置条件\n\n- x86_64 platform(AMD Ryzen Threadripper 3990X 64-Core Processor)\n\n- riscv64 platform(k230)\n\n- unit(cycle)\n\n- matmul dimensions:M = K = N = 32\n\n### benchmark\n\n $(cat benchmark_ntt_report*.md)" > benchmark_ntt_report*.md
    - echo -e "\n### 预置条件\n\n- x86_64 platform(AMD Ryzen Threadripper 3990X 64-Core Processor)\n\n### benchmark\n\n $(cat benchmark_ntt_matmul_x86_64_report_*.md)" > benchmark_ntt_matmul_x86_64_report_*.md
    - echo -e "\n### 预置条件\n\n- riscv64 platform(k230)\n\n### benchmark\n\n $(cat benchmark_ntt_matmul_riscv64_report_*.md)" > benchmark_ntt_matmul_riscv64_report_*.md
    - cp benchmark_ntt*.md ${K510_DIR}
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_NTT_PAGE_ID} update --file benchmark_ntt_report*.md -v
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_NTT_MATMUL_X86_64_PAGE_ID} update --file benchmark_ntt_matmul_x86_64_report_*.md -v
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_NTT_MATMUL_RISCV64_PAGE_ID} update --file benchmark_ntt_matmul_riscv64_report_*.md -v

  artifacts:
    name: "k80_benchmark_ntt_test"
    when: always
    expire_in: never
    paths:
      - benchmark_ntt_report*.md
      - benchmark_ntt_matmul_x86_64_report_*.md
      - benchmark_ntt_matmul_riscv64_report_*.md

  timeout: 2h

"llm qemu test job":
  allow_failure: false
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_TYPE == "llm_qemu_test"'
  tags:
    - nncase
  stage: test
  image:
    name: ${SDK_DOCKER_IMAGE}
    pull_policy: if-not-present
  script:
    - llm_qemu_test=${GITLAB_RUNNER_ROOT}/llm_qemu_test
    - k80=`pwd`
    - k80_version=$(git rev-parse --short HEAD)
    - root=`pwd`/../
    - nncase=${root}/nncase
    - compiler_install=${root}/compiler_install
    - runtime_install=${root}/runtime_install
    - pipeline_version=""
    - cd ..

    # 1. setup
    # 1.0 setup env
    - chmod 0400 ${PRIVATE_KEY}
    - mkdir -p ~/.ssh
    - touch ~/.ssh/known_hosts
    - ssh-keyscan g.a-bug.org >> ~/.ssh/known_hosts
    - apt update
    - cmake --version
    - apt remove --purge -y cmake cmake-data
    - rm -rf /usr/bin/cmake /usr/bin/ctest /usr/share/cmake-3.22*
    - cp -r ${GITLAB_RUNNER_ROOT}/cmake/cmake-3.28.3-linux-x86_64 /opt
    - ln -s /opt/cmake-3.28.3-linux-x86_64/bin/* /usr/local/bin/
    - conan remote add sunnycase https://conan.sunnycase.moe --index 0 --force
    - export DOTNET_ROOT=${GITLAB_RUNNER_ROOT}/dotnet/dotnet-sdk-8.0.111
    - export PATH=${DOTNET_ROOT}:$PATH
    - x86_64_gcc=${GITLAB_RUNNER_ROOT}/toolchain/x86_64_gcc_14.2.0_ubuntu22.04
    - update-alternatives --install /usr/bin/gcc gcc ${x86_64_gcc}/bin/gcc-14.2.0 14
    - update-alternatives --install /usr/bin/g++ g++ ${x86_64_gcc}/bin/g++-14.2.0 14
    - mkdir -p $(pwd)/nuget
    - cp -r ${GITLAB_RUNNER_ROOT}/nuget/packages_ubuntu22.04 $(pwd)/nuget/packages
    - export NUGET_PACKAGES=$(pwd)/nuget/packages
    - cp -r ${GITLAB_RUNNER_ROOT}/conan2/conan2_ubuntu22.04 conan2
    - export CONAN_HOME=$(pwd)/conan2

    # 1.1 setup sdk
    - rm -rf duca duca.tgz
    - curl -# -L -o duca.tgz http://sw-file.a-bug.org/k80/common/sdk/duca/release/${DUCA_VERSION}
    - mkdir -p duca
    - tar xzf duca.tgz -C duca
    - cd duca
    - ./setup.sh toolchain
    - ./setup.sh qemu http://sw-file.a-bug.org/k80/common/sdk/qemu/release/${QEMU_VERSION}
    - cd -
    - duca=${root}/duca

    # 1.2 setup fllm
    - rm -rf fasterllm
    - ssh-agent bash -c "ssh-add ${PRIVATE_KEY};GIT_SSH_COMMAND='ssh -o StrictHostKeyChecking=no' git clone -b ${FLLM_BRANCH} ***************:software/k80/models/fasterllm.git"
    - cd fasterllm
    - git reset --hard ${FLLM_COMMIT}
    - plugin_json="plugin_scheme.json"
    - |
      jq --arg pa_path $(pwd)/fasterllm/attention/distributed_flash_attention.h \
         --arg matmul_path $(pwd)/fasterllm/nncase_plugin.h \
         '.Outputs[0].CSourcePath = $pa_path | .Outputs[1].CSourcePath = $matmul_path' \
         ${llm_qemu_test}/data/${plugin_json} > ${plugin_json}
    - cd -

    # 1.3 setup nncase
    - rm -rf nncase
    - git clone https://github.com/kendryte/nncase.git -c http.proxy=$SOCKS_PROXY -c https.proxy=$SOCKS_PROXY
    - cd nncase
    - git reset --hard origin/${NNCASE_BRANCH}
    - git checkout ${NNCASE_COMMIT}
    - nncase_version=$(git rev-parse --short HEAD)
    - pipeline_version="nncase(${nncase_version}), nncase-k80(${k80_version})"
    - echo "${pipeline_version}" > ${k80}/pipeline_version.txt
    - cd -
    - image_root=${duca}/output/qemu/nncase
    - mkdir -p ${image_root}
    - |
      if [ ! -d model ]; then
          ln -s ${llm_qemu_test}/model model
      fi
    - |
      if [ ! -d data ]; then
          ln -s ${llm_qemu_test}/data data
      fi
    - |
      if [ ! -f ${image_root}/miniconda_env.sh ]; then
          cp data/miniconda_env.sh ${image_root}
      fi
    - |
      if [ ! -f ${image_root}/miniconda_env_test_huggingface_v0.1.tar.gz ]; then
          cp data/miniconda_env_test_huggingface_v0.1.tar.gz ${image_root}
      fi
    - |
      if [ ! -d script ]; then
          ln -s ${llm_qemu_test}/script script
      fi
    - |
      if [ ! -f ${image_root}/run_hf_model.py ]; then
          cp script/run_hf_model.py ${image_root}
      fi
    - |
      if [ ! -d utils ]; then
          ln -s ${llm_qemu_test}/utils utils
      fi

    # 2. compiler build
    # 2.1 build nncase
    - cd ${nncase}
    - rm -rf CMakeUserPresets.json build
    - |
      conan install . --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release
    - cmake --install build/Release --prefix ${compiler_install}
    - cd -

    # 2.2 build k80
    - cd ${k80}
    - rm -rf CMakeUserPresets.json build
    - |
      conan install . --build=missing -s build_type=Release -pr:a=${nncase}/toolchains/x86_64-linux.profile.jinja \
                      -o "&:nncase_dir=${compiler_install}/lib/cmake/nncase" \
                      -o "&:runtime=False" -o "&:python=True" -o "&:tests=False"
    - cmake --preset conan-release
    - cmake --build build/Release --config Release
    - cmake --install build/Release --prefix=${compiler_install}
    - until dotnet restore; do :; done
    - dotnet build -c Release
    - cd -

    # 3. runtime build
    # 3.1 build nncase
    - cd ${nncase}
    - rm -rf build CMakeUserPresets.json runtime
    - |
      conan install . -of runtime --build=missing -s build_type=Release -pr:a=toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DCMAKE_TOOLCHAIN_FILE=runtime/build/Release/generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DENABLE_OP_PROFILE=0 \
            -DBUILD_BENCHMARK=0 \
            -S . \
            -B build
    - cmake --build build
    - cmake --install build --prefix=${runtime_install}
    - cd -

    # 3.2 build k80
    - cd ${k80}
    - rm -rf build CMakeUserPresets.json runtime
    - |
      conan install . -of runtime --build=missing -s build_type=Release -pr:a=../nncase/toolchains/x86_64-linux.profile.jinja \
                      -o "&:runtime=True" -o "&:python=True" -o "&:tests=False" -o "&:k80_runtime=True"
    - |
      cmake -DCMAKE_BUILD_TYPE=Release \
            -DBUILDING_RUNTIME=1 \
            -DENABLE_K80_RUNTIME=1 \
            -DENABLE_OPENMP=0 \
            -DCMAKE_TOOLCHAIN_FILE=runtime/build/Release/generators/conan_toolchain.cmake \
            -DDEFAULT_BUILTIN_RUNTIMES=0 \
            -DBUILD_PYTHON_BINDING=1 \
            -DENABLE_OP_PROFILE=0 \
            -DENABLE_XPU_RUNTIME=1 \
            -DDISABLE_RVV=0 \
            -Dnncaseruntime_DIR=${runtime_install}/lib/cmake/nncaseruntime/ \
            -DDUCA_PATH=${duca}/output/qemu/duca \
            -S . \
            -B build
    - cmake --build build
    - cmake --install build --prefix=${runtime_install}
    - export NNCASE_RUNTIME_LIB=${runtime_install}
    - cd python
    - python3 setup.py bdist_wheel
    - cp dist/nncaseruntime_xpu-*-py3-none-any.whl ${image_root}
    - cd ../../

    # 4. build LLM model
    - export LD_LIBRARY_PATH=${x86_64_gcc}/lib64:${compiler_install}/lib:${nncase}/src/Nncase.Cli/bin/Release/net8.0/runtimes/linux-x64/native:$LD_LIBRARY_PATH
    - export DYLD_LIBRARY_PATH=${compiler_install}/lib:$DYLD_LIBRARY_PATH
    - export PYTHONPATH=${PYTHONPATH}:${compiler_install}/lib:${compiler_install}/python:${nncase}/tests
    - export NNCASE_COMPILER=${nncase}/src/Nncase.Compiler/bin/Release/net8.0/Nncase.Compiler.dll
    - export NNCASE_PLUGIN_PATH=${k80}/modules/Nncase.Modules.XPU/bin/Release/net8.0
    - export PATH=${duca}/tools/llvm/bin/:$PATH
    - export DUCA_TOOLCHAIN_PATH=${duca}/tools/newlib/
    - export DUCA_XPUSDK_PATH=${duca}/output/qemu/xpusdk/
    - export SYS_MODE=1
    - export SOLVE_MAX_TIME=1800
    - export NNCASE_TILING_MAX_SOLUTIONS=1
    - artifact_dir=${GITLAB_RUNNER_ROOT}/qemu_artifact/`date "+%Y_%m_%d_%H_%M_%S"`
    - mkdir -p ${artifact_dir}
    - llm_test_config=${llm_qemu_test}/data/llm_test_config.json
    - cp ${llm_test_config} ${image_root}
    - ls -l ${image_root}
    - bash ${llm_qemu_test}/build_model.sh
    - bash utils/qemu_test.sh ${artifact_dir}
    - cp ${image_root}/llm_test_config.json ${k80}
    - confluence.md -l ${CF_HOST} -u ${CF_USERNAME} -t ${CF_TOKEN} --page_id ${CF_LLM_QEMU_TEST_PAGE_ID} update --file ${artifact_dir}/llm_qemu_test.md -v
  artifacts:
    paths:
      - pipeline_version.txt
      - llm_test_config.json
    when: always

  after_script:
    - llm_qemu_test=${GITLAB_RUNNER_ROOT}/llm_qemu_test
    - pipeline_version=""
    - |
      if [ -f pipeline_version.txt ]; then
        pipeline_version=$(cat pipeline_version.txt)
        echo "pipeline_version is: ${pipeline_version}"
      else
        echo "pipeline_version.txt not found."
      fi
    - test_result="Fail"
    - |
      if [ -f llm_test_config.json ]; then
        if jq -e '.[].test_result | select(. == "Fail")' llm_test_config.json | grep -q "Fail"; then
          test_result="Fail"
        else
          test_result="Pass"
        fi
      fi
    - total_seconds=$(($(date +%s) - $(date -d $CI_PIPELINE_CREATED_AT +%s)))
    - |
      python3 ${llm_qemu_test}/script/send_feishu.py \
      --pipeline_repo "${CI_PROJECT_NAME}" \
      --pipeline_job_name "${CI_JOB_NAME}" \
      --pipeline_source "${CI_PIPELINE_SOURCE}" \
      --pipeline_version "${pipeline_version}" \
      --pipeline_seconds ${total_seconds} \
      --pipeline_url "${CI_PIPELINE_URL}" \
      --pipeline_status "${CI_JOB_STATUS}" \
      --pipeline_test_result "${test_result}" \
      --confluence_url "${CF_URL}" \
      --feishu_group_url "${FEISHU_GROUP_URL}"

  timeout: 10h