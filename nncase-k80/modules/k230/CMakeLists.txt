cmake_minimum_required (VERSION 3.8)

# NOTE this control the __GNNE_STACK_SIZE__ mirco in the code, which used in dynamic gnne function.
set(K230_GNNE_STACK_SIZE 262144)
if (BUILDING_RUNTIME)
    add_definitions(-DBUILDING_RUNTIME)
endif()

include_directories(include)
add_subdirectory(src/runtime)
add_subdirectory(src/functional)

if (BUILDING_RUNTIME)
    if (ENABLE_K230_RUNTIME)
        add_library(nncase_rt_modules_k230 STATIC ${SRCS})
        target_include_directories(nncase_rt_modules_k230 PRIVATE include)
        target_link_libraries(nncase_rt_modules_k230 PRIVATE runtime_k230 functional_ai2d)
        set_target_properties(nncase_rt_modules_k230 PROPERTIES
                OUTPUT_NAME "nncase.rt_modules.k230")

        install(DIRECTORY include/nncase/kernels
                DESTINATION include/nncase
                COMPONENT nncase-headers
                FILES_MATCHING
                PATTERN "*.def"
                PATTERN "*.h"
                PATTERN "*.hpp"
                PATTERN "*.td"
                PATTERN "*.inc"
                PATTERN "LICENSE.TXT"
                )

        install(DIRECTORY include/nncase/runtime
                DESTINATION include/nncase
                COMPONENT nncase-headers
                FILES_MATCHING
                PATTERN "*.def"
                PATTERN "*.h"
                PATTERN "*.hpp"
                PATTERN "*.td"
                PATTERN "*.inc"
                PATTERN "LICENSE.TXT"
                )

        install(DIRECTORY include/nncase/functional
            DESTINATION include/nncase
            COMPONENT nncase-headers
            FILES_MATCHING
            PATTERN "*.def"
            PATTERN "*.h"
            PATTERN "*.hpp"
            PATTERN "*.td"
            PATTERN "*.inc"
            PATTERN "LICENSE.TXT"
        )

        install(TARGETS nncase_rt_modules_k230 EXPORT nncase_rt_modules_k230Targets
                ARCHIVE DESTINATION lib
                LIBRARY DESTINATION lib
                RUNTIME DESTINATION bin
                INCLUDES DESTINATION include
                )

        install(EXPORT nncase_rt_modules_k230Targets
                DESTINATION lib/cmake/nncase_rt_modules_k230)
#        configure_file(${CMAKE_CURRENT_LIST_DIR}/cmake/nncase_rt_modules_k230Config.cmake.in nncase_rt_modules_k230Config.cmake @ONLY)
#        install(FILES ${CMAKE_CURRENT_BINARY_DIR}/nncase_rt_modules_k230Config.cmake DESTINATION lib/cmake/nncase_rt_modules_k230)
        if (K230_LINUX_SDK_DIR)
            add_subdirectory(examples)
        endif()
    endif()
else()
    if (NOT MSVC)
        add_subdirectory(chibicc)
    endif()

    add_library(nncase_modules_k230 SHARED ${SRCS})
    target_include_directories(nncase_modules_k230 PUBLIC include)
    target_link_libraries(nncase_modules_k230 PRIVATE simulator_k230 functional_k230)
    set_target_properties(nncase_modules_k230 PROPERTIES
            OUTPUT_NAME "nncase.simulator.k230")
    install(TARGETS nncase_modules_k230
            COMPONENT nncase-runtime)

endif()
