cmake_minimum_required (VERSION 3.13)


set(SRCS dynamic_gnne_matmul.cpp)

if (BUILDING_RUNTIME)
    if (ENABLE_K230_RUNTIME)
        add_subdirectory(ai2d)
        add_library(functional_k230 STATIC ${SRCS})
        target_link_libraries(functional_k230 PUBLIC nncasebase)

        target_link_libraries(functional_k230 PRIVATE gsl::gsl-lite nncaseruntime runtime_k230)
        target_compile_definitions(functional_k230 PUBLIC -DNNCASE_MODULES_K230_DLL -DNNCASE_functional)
        install(TARGETS functional_k230 EXPORT nncase_rt_modules_k230Targets)
    endif()
else()
    add_library(functional_k230 OBJECT ${SRCS})
    target_link_libraries(functional_k230 PUBLIC nncasebase)
    
    target_link_libraries(functional_k230 PRIVATE gsl::gsl-lite nncaseruntime)
    target_compile_definitions(functional_k230 PUBLIC -DNNCASE_MODULES_K230_DLL -DNNCASE_functional)
    set_target_properties(functional_k230 PROPERTIES POSITION_INDEPENDENT_CODE ON)
    
endif()
