cmake_minimum_required(VERSION 3.13)

set(SRCS ai2d_builder.cpp)

if(BUILDING_RUNTIME)
	if(ENABLE_K230_RUNTIME)
		add_library(functional_ai2d OBJECT ${SRCS})
		set_source_files_properties(ai2d_builder.cpp PROPERTIES COMPILE_FLAGS "-w -fpermissive")

		if (K230_SDK_DIR)
			target_include_directories(functional_ai2d PRIVATE ${K230_SDK_DIR}/bsp/cpu/include ${K230_SDK_DIR}/bsp/utils/include ${K230_SDK_DIR}/bsp/controler/include)
		endif()

		target_link_directories(functional_ai2d PRIVATE ${${nncaseruntime_DIR}/lib/})
		target_link_libraries(functional_ai2d PRIVATE runtime_k230)
		set_target_properties(functional_ai2d PROPERTIES POSITION_INDEPENDENT_CODE ON)
		install(TARGETS functional_ai2d EXPORT nncase_rt_modules_k230Targets)
	endif()
else()
	add_library(functional_ai2d OBJECT ${SRCS})
	target_link_libraries(functional_ai2d PRIVATE simulator_k230)
	target_link_libraries(functional_ai2d PUBLIC nncasebase)
	set_target_properties(functional_ai2d PROPERTIES POSITION_INDEPENDENT_CODE ON)
endif()
