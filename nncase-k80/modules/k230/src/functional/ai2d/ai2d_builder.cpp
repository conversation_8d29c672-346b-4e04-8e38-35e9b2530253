#include "nncase/functional/ai2d/ops_utils.h"
// #include "nncase/transforms/k230/tiling/gnne_tile.h"
// #include "nncase/transforms/k230/tiling/gnne_tile_utils.h"
#include <algorithm>
#include <cstddef>
#include <cstdint>
#include <gsl/gsl-lite.hpp>
#include <memory>
#include <nncase/functional/ai2d/ai2d_builder.h>
// #include <nncase/ir/connectors.h>
#include <fstream>
#include <nncase/runtime/datatypes.h>
#include <nncase/runtime/result.h>
#include <nncase/runtime/runtime_tensor.h>
#include <nncase/runtime/util.h>
#include <stdexcept>
#include <sys/types.h>
#include <type_traits>
// #include <xtensor/xstrides.hpp>
#include <iostream>

#if defined(BUILDING_RUNTIME)
#include <string.h>
#if !defined(__linux__)
#include <interrupt.h>
#include <sleep.h>
#else
#include <fcntl.h>
#include <poll.h>
#include <string>
#include <sys/mman.h>
#include <sys/stat.h>
#include <unistd.h>
#endif
#endif

namespace nncase
{

namespace F
{
    namespace k230
    {
        using namespace nncase::runtime;
        using namespace nncase::runtime::k230;

#if defined(BUILDING_RUNTIME) && !defined(__linux__)
        int done_thunk(void *userdata)
        {
            ai2d_builder *p = reinterpret_cast<ai2d_builder *>(userdata);
            p->ai2d_clear_cpu_intr();
            return 0;
        }
#endif
        ai2d_builder::ai2d_builder(dims_t &input_shape, dims_t &output_shape, ai2d_datatype_t ai2d_dtype,
            ai2d_crop_param_t crop_param, ai2d_shift_param_t shift_param, ai2d_pad_param_t pad_param,
            ai2d_resize_param_t resize_param, ai2d_affine_param_t affine_param)
            : ai2d_dtype_(ai2d_dtype), crop_param_(crop_param), shift_param_(shift_param), pad_param_(pad_param), resize_param_(resize_param), affine_param_(affine_param), dump_asm_(false)
        {

#if defined(BUILDING_RUNTIME)
#if !defined(__linux__)
            ai2d_addr_.paddr = (volatile void *)(AI2D_BASE_ADDR_PAGE_ALIGNED + AI2D_BASE_OFFSET);
            ai2d_set_base(ai2d_addr_.paddr);
            ai2d_done_ = false;
#else
            // open ai2d dev
            char ai2d_name[] = "/dev/ai_2d_device";
            int fd = open(ai2d_name, O_RDWR);
            if (fd < 0)
            {
                std::cerr << "open " << ai2d_name << " failed: " << strerror(errno) << std::endl;
                std::abort();
            }
            ai2d_fd_ = fd;

            // open mem dev
            char mem_name[] = "/dev/mem";
            fd = open(mem_name, O_RDWR | O_SYNC);
            if (fd < 0)
            {
                std::cerr << "open " << mem_name << " failed: " << strerror(errno);
                std::abort();
            }
            mem_fd_ = fd;

            // mmap ai2d regs
            ai2d_addr_.mmap_size = AI2D_MMAP_SIZE;
            ai2d_addr_.paddr = (volatile void *)AI2D_BASE_ADDR_PAGE_ALIGNED;
            void *vaddr = mmap(NULL, ai2d_addr_.mmap_size, PROT_READ | PROT_WRITE, MAP_SHARED, mem_fd_, (off_t)ai2d_addr_.paddr);
            if (vaddr == MAP_FAILED)
            {
                std::cerr << "mmap failed: " << strerror(errno);
                std::abort();
            }
            ai2d_addr_.vaddr = (volatile void *)vaddr;
            ai2d_set_base(ai2d_addr_.vaddr + AI2D_BASE_OFFSET);
#endif
            ai2d_set_time_out(5000);
#endif

#if defined(BUILDING_RUNTIME) && !defined(__linux__)
            plic_interrupt_enable();
            plic_set_priority(IRQN_AI_2D_INTERRUPT, 1);
            plic_irq_register(IRQN_AI_2D_INTERRUPT, done_thunk, this);
            plic_irq_enable(IRQN_AI_2D_INTERRUPT);
#endif
            input_shape_ = input_shape;
            output_shape_ = output_shape;
            auto ret = check_config();
            if (ret.is_err())
                throw std::runtime_error("wrong ai2d configuration.");
        }

        std::unique_ptr<ai2d_builder> ai2d_builder::create(dims_t &input_shape, dims_t &output_shape, ai2d_datatype_t &ai2d_dtype,
            ai2d_crop_param_t &crop_param, ai2d_shift_param_t &shift_param, ai2d_pad_param_t &pad_param,
            ai2d_resize_param_t &resize_param, ai2d_affine_param_t &affine_param)
        {
            return std::make_unique<ai2d_builder>(input_shape, output_shape, ai2d_dtype, crop_param, shift_param, pad_param, resize_param, affine_param);
        }

        ai2d_builder::~ai2d_builder()
        {
#if defined(BUILDING_RUNTIME)
#if defined(__linux__)
            // munmap ai2d regs
            int ret = munmap((void *)ai2d_addr_.vaddr, ai2d_addr_.mmap_size);
            if (ret != 0)
            {
                std::cerr << "munmap failed: " << strerror(errno);
                std::abort();
            }
            ai2d_addr_.vaddr = nullptr;

            // close mem dev
            if (mem_fd_ > 0)
            {
                close(mem_fd_);
                mem_fd_ = -1;
            }

            // close ai2d dev
            if (ai2d_fd_ > 0)
            {
                close(ai2d_fd_);
                ai2d_fd_ = -1;
            }
#endif
            ai2d_addr_.addr = nullptr;
            ai2d_addr_.paddr = nullptr;
#endif
        }

#if defined(BUILDING_RUNTIME)
        void ai2d_builder::ai2d_set_base(volatile void *addr)
        {
            ai2d_addr_.addr = addr;
        }

        volatile uint8_t *ai2d_builder::ai2d_get_base()
        {
            return (volatile uint8_t *)ai2d_addr_.addr;
        }

        uint16_t ai2d_builder::ai2d_get_intr_num()
        {
            return *((volatile uint16_t *)(ai2d_get_base() + 0x90));
        }

        void ai2d_builder::ai2d_clear_cpu_intr()
        {
            volatile uint32_t *p = (volatile uint32_t *)(ai2d_get_base() + 0xa0);
            p[0] = 1;
            p[1] = 0;
            p[2] = 0;
            p[3] = 0;
#if !defined(__linux__)
            ai2d_done_ = true;
#endif
        }

        void ai2d_builder::ai2d_set_time_out(uint32_t val)
        {
            volatile uint32_t *p = (volatile uint32_t *)(ai2d_get_base() + 0xc0);
            p[0] = val;
            p[1] = 0;
            p[2] = 0;
            p[3] = 0;
        }
#endif
        result<void> ai2d_builder::check_config()
        {
            // ai2d_dtype_.src_type = input_.datatype;
            // ai2d_dtype_.dst_type = output_.datatype;

            input_c_ = input_shape_[1];
            input_h_ = input_shape_[2];
            input_w_ = input_shape_[3];
            output_c_ = output_shape_[1];
            output_h_ = output_shape_[2];
            output_w_ = output_shape_[3];
            if (ai2d_dtype_.src_format <= ai2d_format::YUV420_I420)
            {
                input_c_ = 3;
                input_h_ = input_shape_[2] * 2 / 3;
            }
            else if (ai2d_dtype_.src_format == ai2d_format::RGB_packed)
            {
                input_c_ = input_shape_[3];
                input_h_ = input_shape_[1];
                input_w_ = input_shape_[2];
            }
            if (ai2d_dtype_.dst_format <= ai2d_format::YUV420_I420)
            {
                output_c_ = 3;
                output_h_ = output_shape_[2] * 2 / 3;
            }
            else if (ai2d_dtype_.dst_format == ai2d_format::RGB_packed)
            {
                output_c_ = output_shape_[3];
                output_h_ = output_shape_[1];
                output_w_ = output_shape_[2];
            }

            if (pad_param_.paddings[0].sum() + pad_param_.paddings[1].sum() + pad_param_.paddings[2].sum() + pad_param_.paddings[3].sum() == 0)
            {
                pad_param_.pad_flag = false;
            }
            if (!pad_param_.pad_flag)
            {
                pad_param_.paddings = { { 0, 0 }, { 0, 0 }, { 0, 0 }, { 0, 0 } };
            }
            if (!crop_param_.crop_flag)
            {
                crop_param_.start_x = 0;
                crop_param_.start_y = 0;
                crop_param_.width = input_w_;
                crop_param_.height = input_h_;
            }

            if (resize_param_.resize_flag and affine_param_.affine_flag)
                throw std::runtime_error("We don't affine and resize simultaneously.");

            if (crop_param_.start_x < 0 or crop_param_.start_x >= input_w_ or crop_param_.start_x + crop_param_.width > input_w_)
                throw std::runtime_error("Crop param(x) error.");

            if (crop_param_.start_y < 0 or crop_param_.start_y >= input_h_ or crop_param_.start_y + crop_param_.height > input_h_)
                throw std::runtime_error("Crop param(y) error.");

            if (shift_param_.shift_flag)
            {
                if (ai2d_dtype_.src_format != ai2d_format::RAW16)
                    throw std::runtime_error("Only Raw16(src) support shift.");
                if (ai2d_dtype_.dst_format != ai2d_format::RAW16 and ai2d_dtype_.dst_format != ai2d_format::NCHW_FMT)
                    throw std::runtime_error("Only Raw16/NCHW(dst) support shift.");
            }
            return ok();
        }

        result<void> ai2d_builder::build_schedule()
        {
            auto input_shape = input_shape_;
            auto output_shape = output_shape_;
            bool broadcast_in_channel = ai2d_dtype_.src_format == ai2d_format::NCHW_FMT
                and ai2d_dtype_.dst_format == ai2d_format::NCHW_FMT
                and input_shape[1] != output_shape[1];
            if (broadcast_in_channel)
                input_shape[1] = output_shape[1];
            // treat as nchw even rgb_packed
            tensor4d_segment ofmap {
                { 0, static_cast<int32_t>(output_shape[0]), static_cast<int32_t>(output_shape[0]) },
                { 0, static_cast<int32_t>(output_shape[1]), static_cast<int32_t>(output_shape[1]) },
                { pad_param_.paddings[2].before, output_h_ - pad_param_.paddings[2].after, output_h_ - pad_param_.paddings[2].sum() },
                { pad_param_.paddings[3].before, output_w_ - pad_param_.paddings[3].after, output_w_ - pad_param_.paddings[3].sum() }
            };
            tensor4d_segment ifmap {
                { 0, static_cast<int32_t>(input_shape[0]), static_cast<int32_t>(input_shape[0]) },
                { 0, static_cast<int32_t>(input_shape[1]), static_cast<int32_t>(input_shape[1]) },
                { crop_param_.start_y, crop_param_.start_y + crop_param_.height, crop_param_.height },
                { crop_param_.start_x, crop_param_.start_x + crop_param_.width, crop_param_.width }
            };
            if (ai2d_dtype_.dst_format == ai2d_format::RGB_packed)
            {
                ofmap = {
                    { 0, static_cast<int32_t>(output_shape[0]), static_cast<int32_t>(output_shape[0]) },
                    { 0, static_cast<int32_t>(output_shape[3]), static_cast<int32_t>(output_shape[3]) },
                    { pad_param_.paddings[2].before, output_h_ - pad_param_.paddings[2].after, output_h_ - pad_param_.paddings[2].sum() },
                    { pad_param_.paddings[3].before, output_w_ - pad_param_.paddings[3].after, output_w_ - pad_param_.paddings[3].sum() }
                };
            }
            if (ai2d_dtype_.dst_format == ai2d_format::RGB_packed)
            {
                ifmap = {
                    { 0, static_cast<int32_t>(input_shape[0]), static_cast<int32_t>(input_shape[0]) },
                    { 0, static_cast<int32_t>(input_shape[3]), static_cast<int32_t>(input_shape[3]) },
                    { crop_param_.start_y, crop_param_.start_y + crop_param_.height, crop_param_.height },
                    { crop_param_.start_x, crop_param_.start_x + crop_param_.width, crop_param_.width }
                };
            }
            if ((int32_t)ai2d_dtype_.src_format <= 2)
            {
                ifmap.dim_1 = { 0, 3, 3 };
            }
            if ((int32_t)ai2d_dtype_.dst_format <= 2)
            {
                ofmap.dim_1 = { 0, 3, 3 };
            }

            config_ = ai2d_config {};
            ai2d_utils ai2d_util;
            // always nchw
            dims_t in_layout { input_shape[0], static_cast<size_t>(input_c_), static_cast<size_t>(input_h_), static_cast<size_t>(input_w_) };
            dims_t out_layout { output_shape[0], static_cast<size_t>(output_c_), static_cast<size_t>(output_h_), static_cast<size_t>(output_w_) };
            ai2d_util.update_static_param(config_, in_layout, out_layout, ai2d_dtype_, crop_param_, shift_param_, pad_param_, resize_param_, affine_param_);
            ai2d_util.update_M_param(config_, ifmap, ofmap, ai2d_dtype_, resize_param_, affine_param_);

            std::vector<int32_t> dst_hw(2);
            if (affine_param_.affine_flag)
            {
                ai2d_util.affine_sram_search(config_, ofmap, ifmap, pad_param_, dst_hw);
            }
            else
            {
                ai2d_util.resize_sram_search(config_, ofmap, ifmap, dst_hw);
            }

            auto channel_chunk = ai2d_dtype_.src_format == ai2d_format::RAW16 ? 2 : 4;
            std::vector<segment> sram_n_segs = get_segment_start_end_length(0, 1, output_shape[0]);
            std::vector<segment> sram_c_segs = get_segment_start_end_length(0, channel_chunk, out_layout[1]);
            std::vector<segment> sram_h_segs = get_segment_start_end_length(0, dst_hw[0], output_h_ - pad_param_.paddings[2].sum());
            std::vector<segment> sram_w_segs = get_segment_start_end_length(0, dst_hw[1], output_w_ - pad_param_.paddings[3].sum());

            int32_t inst_len = 0;
            split_pos_.push_back(0);
            for (auto sram_on : sram_n_segs)
            {
                for (auto sram_oc : sram_c_segs)
                {
                    bool c_changed = true;
                    for (auto sram_oh : sram_h_segs)
                    {
                        for (auto sram_ow : sram_w_segs)
                        {
                            std::vector<float> dst_00 { static_cast<float>(sram_ow.start), static_cast<float>(sram_oh.start) };
                            std::vector<float> dst_x0 { static_cast<float>(sram_ow.end - 1), static_cast<float>(sram_oh.start) };
                            std::vector<float> dst_0y { static_cast<float>(sram_ow.start), static_cast<float>(sram_oh.end - 1) };
                            std::vector<float> dst_xy { static_cast<float>(sram_ow.end - 1), static_cast<float>(sram_oh.end - 1) };

                            ai2d_util.update_M_param(config_, ifmap, ofmap, ai2d_dtype_, resize_param_, affine_param_);
                            std::vector<float> M_ori_scale { config_.origin_M(config_.M0), config_.origin_M(config_.M1),
                                config_.origin_M(config_.M3), config_.origin_M(config_.M4) };
                            std::vector<float> M_ori_bias { config_.origin_M(config_.M2), config_.origin_M(config_.M5) };

                            segment sram_h, sram_w;
                            tensor4d_segment ifmap_pp, ofmap_pp;
                            int32_t src_x = 0, src_y = 0;
                            float offset_M2 = 0.f, offset_M5 = 0.f;
                            if (affine_param_.affine_flag)
                            {
                                if (sram_h_segs.size() == 1 and sram_w_segs.size() == 1)
                                {
                                    src_x = 0;
                                    src_y = 0;
                                    ifmap_pp = ifmap;
                                    ofmap_pp = ofmap;
                                }
                                else
                                {
                                    auto src_00 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_00);
                                    auto src_x0 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_x0);
                                    auto src_0y = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_0y);
                                    auto src_xy = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_xy);

                                    std::vector<float> xs { src_00[0], src_x0[0], src_0y[0], src_xy[0] };
                                    std::vector<float> ys { src_00[1], src_x0[1], src_0y[1], src_xy[1] };
                                    std::vector<float> min_val { *std::min_element(xs.begin(), xs.end()), *std::min_element(ys.begin(), ys.end()) };
                                    std::vector<float> max_val { *std::max_element(xs.begin(), xs.end()), *std::max_element(ys.begin(), ys.end()) };

                                    src_x = std::max(std::floor(min_val[0]), 0.f);
                                    src_y = std::max(std::floor(min_val[1]), 0.f);
                                    src_x = std::min(src_x, ifmap.dim_3.length - 1);
                                    src_y = std::min(src_y, ifmap.dim_2.length - 1);

                                    if (ai2d_dtype_.src_format <= ai2d_format::YUV420_I420)
                                    {
                                        if (src_x % 2 == 1)
                                            src_x -= 1;
                                        if (src_y % 2 == 1)
                                            src_y -= 1;
                                    }

                                    int32_t src_width_end = std::max(static_cast<int32_t>(std::ceil(max_val[0])), 0);
                                    int32_t src_height_end = std::max(static_cast<int32_t>(std::ceil(max_val[1])), 0);
                                    src_width_end = std::min(src_width_end, static_cast<int32_t>(ifmap.dim_3.length - 1));
                                    src_height_end = std::min(src_height_end, static_cast<int32_t>(ifmap.dim_2.length - 1));

                                    int32_t src_width = src_width_end - src_x + 1;
                                    int32_t src_height = src_height_end - src_y + 1;

                                    if (src_width == 0)
                                        src_width += 1;
                                    if (src_height == 0)
                                        src_height += 1;

                                    if (ai2d_dtype_.src_format <= ai2d_format::YUV420_I420)
                                    {
                                        if (src_width % 2 == 1)
                                        {
                                            src_width_end = std::min(src_width_end + 1, static_cast<int32_t>(ifmap.dim_3.length - 1));
                                            src_width = src_width_end - src_x + 1;
                                            if (src_width % 2 == 1)
                                                throw std::runtime_error("YUV420 width error.");
                                        }
                                        if (src_height % 2 == 1)
                                        {
                                            src_height_end = std::min(src_height_end + 1, static_cast<int32_t>(ifmap.dim_2.length - 1));
                                            src_height = src_height_end - src_y + 1;
                                            if (src_height % 2 == 1)
                                                throw std::runtime_error("YUV420 height error.");
                                        }
                                    }

                                    std::vector<float> ori_00 { 0, 0 };
                                    auto ori_src_00 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, ori_00);
                                    offset_M2 = 0.f;
                                    offset_M2 = src_00[0] - src_x - ori_src_00[0];
                                    offset_M5 = 0.f;
                                    offset_M5 = src_00[1] - src_y - ori_src_00[1];

                                    sram_h = { src_y, src_y + src_height, src_height };
                                    sram_w = { src_x, src_x + src_width, src_width };
                                    ifmap_pp = { sram_on, sram_oc, sram_h, sram_w };
                                    ofmap_pp = { sram_on, sram_oc, sram_oh, sram_ow };
                                }
                            }
                            else
                            {
                                auto src_00 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_00);
                                auto src_xy = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, dst_xy);

                                src_x = std::max(static_cast<int32_t>(std::floor(src_00[0])), 0);
                                src_y = std::max(static_cast<int32_t>(std::floor(src_00[1])), 0);
                                if (ai2d_dtype_.src_format <= ai2d_format::YUV420_I420)
                                {
                                    if (src_x % 2 == 1)
                                        src_x -= 1;
                                    if (src_y % 2 == 1)
                                        src_y -= 1;
                                }

                                int32_t src_width_end = std::min(static_cast<int32_t>(std::ceil(src_xy[0])), ifmap.dim_3.length - 1);
                                int32_t src_height_end = std::min(static_cast<int32_t>(std::ceil(src_xy[1])), ifmap.dim_2.length - 1);

                                int32_t src_width = src_width_end - src_x + 1;
                                int32_t src_height = src_height_end - src_y + 1;

                                if (ai2d_dtype_.src_format <= ai2d_format::YUV420_I420)
                                {
                                    if (src_width % 2 == 1)
                                    {
                                        src_width_end = std::min(static_cast<int32_t>(std::ceil(src_xy[0] + 1)), ifmap.dim_3.length - 1);
                                        src_width = src_width_end - src_x + 1;
                                        if (src_width % 2 == 1)
                                            throw std::runtime_error("YUV420 width error.");
                                    }
                                    if (src_height % 2 == 1)
                                    {
                                        src_height_end = std::min(static_cast<int32_t>(std::ceil(src_xy[1] + 1)), ifmap.dim_2.length - 1);
                                        src_height = src_height_end - src_y + 1;
                                        if (src_height % 2 == 1)
                                            throw std::runtime_error("YUV420 height error.");
                                    }
                                }

                                std::vector<float> ori_00 { 0, 0 };
                                auto ori_src_00 = ai2d_util.M_mul_add(M_ori_scale, M_ori_bias, ori_00);
                                offset_M2 = 0.f;
                                if (src_x != 0)
                                    offset_M2 = src_00[0] - src_x - ori_src_00[0];
                                offset_M5 = 0.f;
                                if (src_y != 0)
                                    offset_M5 = src_00[1] - src_y - ori_src_00[1];

                                sram_h = { src_y, src_y + src_height, src_height };
                                sram_w = { src_x, src_x + src_width, src_width };
                                ifmap_pp = { sram_on, sram_oc, sram_h, sram_w };
                                ofmap_pp = { sram_on, sram_oc, sram_oh, sram_ow };
                            }
                            if (crop_param_.crop_flag)
                            {
                                src_x += crop_param_.start_x;
                                src_y += crop_param_.start_y;
                            }

                            ai2d_util.update_dynamic_param(config_, src_x, src_y, ai2d_dtype_, pad_param_, ifmap_pp, ofmap_pp, ifmap, ofmap, offset_M2, offset_M5, in_layout, out_layout, broadcast_in_channel);

                            bool write_all = false;
                            if (c_changed)
                            {
                                inst_len += 16 * 9;
                                c_changed = false;
                                write_all = true;
                            }
                            else
                            {
                                inst_len += 16 * 2;
                            }

                            bool is_last_hw = sram_oh == sram_h_segs.back() and sram_ow == sram_w_segs.back();
                            int32_t next_inst_len = inst_len;
                            if (!is_last_hw)
                            {
                                next_inst_len += 16 * 2;
                            }
                            else if (sram_oc != sram_c_segs.back())
                            {
                                next_inst_len += 16 * 9;
                            }

                            if (next_inst_len > 1024)
                            {
                                inst_len = 0;
                                config_.intr_mask = 0;
                            }
                            else if (sram_on == sram_n_segs.back() and sram_oc == sram_c_segs.back() and sram_oh == sram_h_segs.back() and sram_ow == sram_w_segs.back())
                            {
                                config_.intr_mask = 0;
                            }
                            else
                            {
                                config_.intr_mask = 1;
                            }

                            // config_ all ext register
                            ai2d_util.update_regs(config_, write_all, regs_);
                            if (config_.intr_mask == 0)
                            {
                                split_pos_.push_back(regs_.size());
                            }

                            if (dump_asm_)
                                dump_asm(write_all);
                        }
                    }
                }
            }

            if (dump_asm_)
                [[maybe_unused]]
                auto ret = dump_gmodel();

            return ok();
        }

        void ai2d_builder::dump_asm([[maybe_unused]] bool write_all)
        {
#ifndef BUILDING_RUNTIME
            auto func_dump_dir = dump_dir_ / "ai2d";
            std::filesystem::create_directories(func_dump_dir);
            std::fstream asm_out(func_dump_dir / "ai2d.asm", std::ios::app);

            for (size_t idx = write_all ? 0 : 28; idx < 36; idx += 4)
            {
                for (size_t reg_id = 0; reg_id < 4; reg_id++)
                {
                    auto str = config_.to_string(idx + reg_id);
                    if (str != "")
                        asm_out << str << std::endl;
                }
            }
            asm_out << std::endl;
#endif
        }

        result<void> ai2d_builder::dump_gmodel()
        {
#ifndef BUILDING_RUNTIME
            auto func_dump_dir = dump_dir_ / "ai2d";
            std::filesystem::create_directories(func_dump_dir);

            // gmodel.bin
            std::fstream gout(func_dump_dir / "gmodel", std::ios::out | std::ios::binary);
            [[maybe_unused]] ai2d_utils ai2d_util;
            // try_var(output_span, get_output_span(input_.impl()));
            // TODO: get_input runtime tensor
            auto input_size = compute_size(input_shape_) * get_bytes_from_type(ai2d_dtype_.src_type);
            gout.write((char *)0, input_size);
            gout.flush();

            // pc_addr_ctrl
            std::fstream reg_out(func_dump_dir / "gmodel.pc_addr_ctrl", std::ios::out | std::ios::binary);
            for (auto &reg : regs_)
            {
                for (size_t idx = 0; idx < reg.size(); idx++)
                {
                    reg_out << std::hex << std::setfill('0') << std::setw(8) << (idx == 0 ? (0x00400c00 + reg[idx]) : reg[idx]);
                    if (idx == 0)
                        reg_out << " ";
                }
                reg_out << std::endl;
            }

            // regs_bin
            std::fstream reg_bin_out(func_dump_dir / "regs.bin", std::ios::out | std::ios::binary);
            for (auto &reg : regs_)
            {
                reg_bin_out.write((char *)reg.data(), sizeof(uint32_t));
                for (size_t idx = 1; idx < reg.size(); idx++)
                {
                    reg_bin_out.write((char *)(reg.data() + reg.size() - idx), sizeof(uint32_t));
                }
            }

            gout.close();
            reg_out.close();
            reg_bin_out.close();
#endif
            return ok();
        }

        result<void> ai2d_builder::invoke([[maybe_unused]] runtime_tensor &input, [[maybe_unused]] runtime_tensor &output)
        {
#ifdef BUILDING_RUNTIME
            // 1. sync inputs
            try_(hrt::sync(input, sync_op_t::sync_write_back));

#if defined(__linux__)
            try_(hrt::sync(output, sync_op_t::sync_invalidate, true));
#endif

            // 2. config ai2d
            volatile uint32_t *ai2d_reg = (volatile uint32_t *)ai2d_get_base();
            for (auto i = 1; i < split_pos_.size(); i++)
            {
                for (auto r = split_pos_[i - 1]; r < split_pos_[i]; r++)
                {
                    auto reg = regs_[r];
                    size_t offset = reg[0] / 4;
                    auto addr_offset = 0;
                    if (offset == 0)
                    {
                        auto input_buffer = get_host_buffer(input.impl()).expect("get input buffer failed");
                        uint32_t basement_in = input_buffer.physical_address().unwrap();
                        addr_offset = basement_in;
                    }
                    else if (offset == 4)
                    {
                        auto output_buffer = get_host_buffer(output.impl()).expect("get output buffer failed");
                        uint32_t basement_out = output_buffer.physical_address().unwrap();
                        addr_offset = basement_out;
                    }

                    *(ai2d_reg + offset + 0) = reg[4] + addr_offset;
                    *(ai2d_reg + offset + 1) = reg[3] + addr_offset;
                    *(ai2d_reg + offset + 2) = reg[2] + addr_offset;
                    *(ai2d_reg + offset + 3) = reg[1] + addr_offset;
                }

                // 3. wait ai2d done
#if !defined(__linux__)
                while (!ai2d_done_)
                {
                }
#else
                struct pollfd fds;
                fds.fd = ai2d_fd_;
                fds.events = POLLIN;
                poll(&fds, 1, -1);
#endif

                // 4. check ai2d status
                ai2d_intr_t intr = ai2d_get_intr_num();
                if (intr == AI2D_INTR_TIME_OUT)
                {
                    std::cerr << "ai2d timeout!" << std::endl;
                    return err(std::errc::timed_out);
                }
                else if (intr == AI2D_INTR_EXCEPTION)
                {
                    std::cerr << "ai2d exception!" << std::endl;
                    return err(std::errc::invalid_argument);
                }
            }

            // 5. invalidate outputs
#if !defined(__linux__)
            try_(hrt::sync(output, sync_op_t::sync_invalidate, true));
#endif

#endif
            return ok();
        }
    }
}
}