/* Copyright 2019-2020 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <nncase/runtime/allocator.h>
#include <nncase/runtime/dbg.h>
#include <nncase/runtime/host_buffer.h>

#if !defined(__linux__)
#include <core_rv64.h>
#define IOMEM 0
#else
#include "mmz_allocator.h"
#include <cstring>
#include <fcntl.h>
#include <poll.h>
#include <sys/ioctl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#endif

#ifdef DUMP_MEM
#include <iomanip>
#include <iostream>
static uint64_t used_mem = 0;
static uint64_t max_mem = 0;
#endif

using namespace nncase;
using namespace nncase::runtime;

namespace
{
class host_buffer_impl : public host_buffer_node
{
public:
    host_buffer_impl(gsl::byte *data, size_t paddr, size_t bytes,
        std::function<void(gsl::byte *)> deleter,
        buffer_allocator &allocator,
        host_sync_status_t host_sync_status, [[maybe_unused]] bool collect = false)
        : host_buffer_node(bytes, allocator, host_sync_status), vaddr_(std::move(data)), paddr_(paddr), deleter_(std::move(deleter))
    {
#ifdef DUMP_MEM
        bytes_size_ = bytes;
        collect_ = collect;
#endif
    }

    ~host_buffer_impl()
    {
        deleter_(vaddr_);
#ifdef DUMP_MEM
        if (collect_)
        {
            if (max_mem < used_mem)
                max_mem = used_mem;
            std::cout
                << "[Used_mem]:" << std::setw(16) << std::setfill(' ')
                << used_mem << "\t[deleter ]:" << std::setw(16)
                << std::setfill(' ') << bytes_size_ << "\t[Max_mem]: " << max_mem
                << std::endl;
            used_mem -= bytes_size_;
        }
#endif
    }

    bool has_physical_address() const noexcept override { return paddr_ != 0; }

    result<uintptr_t> physical_address() noexcept override
    {
        if (paddr_ != 0)
        {
            return ok(paddr_);
        }
        else
        {
            return err(std::errc(std::errc::invalid_argument));
        }
    }

    result<gsl::span<gsl::byte>> map_core([[maybe_unused]] map_access_t access) override
    {
        return ok(gsl::span<gsl::byte>(vaddr_, size_bytes()));
    }

    result<void> unmap_core([[maybe_unused]] map_access_t access) override { return ok(); }

    result<void> sync_core([[maybe_unused]] sync_op_t op) override
    {
#if !defined(__linux__)
        if (op == sync_op_t::sync_invalidate)
        {
            cache_flush();
        }
        else if (op == sync_op_t::sync_write_back)
        {
            csi_dcache_clean();
            csi_l2cache_clean();
        }
#else
#if USE_CACHE
        int ret = 0;
        if (op == sync_op_t::sync_invalidate)
        {
            ret = kd_mpi_sys_mmz_flush_cache(paddr_, vaddr_, size_bytes());
            if (ret)
            {
                std::cerr << "invalidate failed: ret = " << ret << std::endl;
                std::abort();
            }
        }
        else if (op == sync_op_t::sync_write_back)
        {
            ret = kd_mpi_sys_mmz_flush_cache(physical_address().unwrap(), vaddr_, size_bytes());
            if (ret)
            {
                std::cerr << "write back failed: ret = " << ret << std::endl;
                std::abort();
            }
        }
#endif
#endif
        return ok();
    }

private:
    gsl::byte *vaddr_;
    size_t paddr_;
    std::function<void(gsl::byte *)> deleter_;
#ifdef DUMP_MEM
    size_t bytes_size_;
    bool collect_;
#endif
};

class host_buffer_allocator : public buffer_allocator
{
public:
    result<buffer_t>
    allocate([[maybe_unused]] size_t bytes,
        [[maybe_unused]] const buffer_allocate_options &options) override
    {
#ifdef DUMP_MEM
        std::cout << "[Used_mem]:" << std::setw(16) << std::setfill(' ')
                  << used_mem << "\t[allocate]:" << std::setw(16)
                  << std::setfill(' ') << bytes << std::endl;
        used_mem += bytes;
#endif
        uintptr_t paddr = 0;
        std::function<void(gsl::byte *)> deleter;
#if !defined(__linux__)
        auto buffer = new (std::nothrow) gsl::byte[bytes];
        paddr = (size_t)buffer;
        CHECK_WITH_ERR(buffer, std::errc::not_enough_memory);
        deleter = [](gsl::byte *p)
        { delete[] p; };
#else
        void *vaddr = nullptr;
        if (options.flags & HOST_BUFFER_ALLOCATE_SHARED)
        {
            try_(mmz_allocator_.allocate(bytes, vaddr, paddr));
            deleter = [](gsl::byte *p)
            { static_cast<host_buffer_allocator &>(buffer_allocator::host()).free_mmz_memory(p); };
        }
        else
        {
            vaddr = new (std::nothrow) gsl::byte[bytes];
            CHECK_WITH_ERR(vaddr, std::errc::not_enough_memory);
            deleter = [](gsl::byte *p)
            { delete[] p; };
        }

        auto buffer = reinterpret_cast<gsl::byte *>(vaddr);
#endif

        return ok<buffer_t>(object_t<host_buffer_impl>(
            std::in_place, buffer, paddr, bytes, deleter, *this, host_sync_status_t::valid, true));
    }

    result<buffer_t>
    attach([[maybe_unused]] gsl::span<gsl::byte> data,
        [[maybe_unused]] const buffer_attach_options &options) override
    {
#if !defined(__linux__)
        auto vaddr = data.data();
        auto paddr = options.flags & HOST_BUFFER_ATTACH_SHARED ? vaddr + IOMEM : 0;
        auto deleter = options.deleter != nullptr ? options.deleter : []([[maybe_unused]] gsl::byte *p) {};
        return ok<buffer_t>(object_t<host_buffer_impl>(
            std::in_place, vaddr, paddr, data.size_bytes(),
            deleter, *this,
            host_sync_status_t::valid));
#else

#ifdef DEBUG
        CHECK_WITH_ERR(!(options.flags & HOST_BUFFER_ATTACH_SHARED) || options.physical_address, std::errc::invalid_argument);
#endif
        auto vaddr = data.data();
        auto paddr = options.flags & HOST_BUFFER_ATTACH_SHARED ? options.physical_address : 0;
        auto deleter = options.deleter != nullptr ? options.deleter : []([[maybe_unused]] gsl::byte *p) {};
        return ok<buffer_t>(object_t<host_buffer_impl>(
            std::in_place, vaddr, paddr, data.size_bytes(),
            deleter, *this,
            host_sync_status_t::valid));
#endif
    }

#if defined(__linux__)
    void free_mmz_memory(gsl::byte *p) noexcept
    {
        mmz_allocator_.free(p);
    }

    void shrink_memory_pool() override
    {
        mmz_allocator_.destory();
    }
#else
    void shrink_memory_pool() override { }
#endif

private:
#if defined(__linux__)
    k230::mmz_allocator mmz_allocator_;
#endif
};

host_buffer_allocator host_allocator;
} // namespace

buffer_allocator &buffer_allocator::host() { return host_allocator; }
