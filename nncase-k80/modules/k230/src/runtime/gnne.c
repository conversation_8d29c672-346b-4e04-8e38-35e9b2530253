/* Copyright 2020 Canaan Inc.
*
* Licensed under the Apache License, Version 2.0 (the "License");
* you may not use this file except in compliance with the License.
* You may obtain a copy of the License at
*
*     http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing, software
* distributed under the License is distributed on an "AS IS" BASIS,
* WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
* See the License for the specific language governing permissions and
* limitations under the License.
*/

#include <nncase/runtime/k230/gnne.h>
#include <stddef.h>
#if !defined(__linux__)
#include "core_rv64.h"
#endif

volatile gnne_reg_file_t *gnne_regs = NULL;

#if !defined(__linux__)
void flush_cache()
{
   cache_flush();
}

void clean_cache()
{
   csi_dcache_clean();
   csi_l2cache_clean();
}

void invalidate_cache()
{
   csi_dcache_invalid();
   csi_l2cache_invalid();
}
#endif

void gnne_set_base(volatile void *addr)
{
   gnne_regs = (volatile gnne_reg_file_t *)(addr + GNNE_ICACHE_CFG_OFFSET);
}

void gnne_ctrl_set(gnne_ctrl_function_t func)
{
   gnne_regs->ctrl.data[1] = func;
}

void gnne_clear_cpu_intr()
{
   gnne_ctrl_set(GNNE_CTRL_CPU_INTR_CLEAR);
}

void gnne_init()
{
   gnne_disable();
}

void gnne_disable()
{
   gnne_ctrl_set(GNNE_CTRL_ENABLE_CLEAR);
   while (gnne_regs->status.bits.reset_status != GNNE_RESET_STATUS_NORMAL)
       ;
}

int gnne_enable(uint64_t pc_start, uint64_t pc_end, uint64_t pc_breakpoint)
{
   if (gnne_regs->status.bits.kpu_work_status != GNNE_STATUS_IDLE)
       return 1;

   // set pc
   gnne_regs->pc_cfg.bits.start_pc_addr_reg = pc_start;
   gnne_regs->pc_cfg.bits.end_pc_addr_reg = pc_end;
   gnne_regs->pc_cfg.bits.breakpoint_pc_addr_reg = pc_breakpoint;
   gnne_ctrl_set(GNNE_CTRL_ENABLE_SET | GNNE_CTRL_DEBUG_MODE_SET);
   return 0;
}

int gnne_resume(gnne_ctrl_function_t resume_mode, uint64_t pc_start)
{
   if (gnne_regs->status.bits.kpu_work_status != GNNE_STATUS_PENDING)
       return 1;

   switch (resume_mode)
   {
   case GNNE_CTRL_CPU_RESUME_MODE_0:
   {
       gnne_ctrl_set(GNNE_CTRL_CPU_RESUME_MODE_0);
       break;
   }
   case GNNE_CTRL_CPU_RESUME_MODE_1:
   case GNNE_CTRL_CPU_RESUME_MODE_3:
   {
       // set pc
       gnne_regs->pc_cfg.bits.start_pc_addr_reg = pc_start;
       gnne_ctrl_set(resume_mode);
       break;
   }
   default:
   {
       printf("the resume mode(0x%lx) is not supported\n", resume_mode);
       return 2;
   }
   }

   return 0;
}

gnne_status gnne_get_status()
{
   return gnne_regs->status;
}

void gnne_dump_status()
{
   volatile gnne_status *status = &gnne_regs->status;
   printf("load_que_status = %u\n", status->bits.load_que_satus);
   printf("store_que_status = %u\n", status->bits.store_que_status);
   printf("dm_que_status = %u\n", status->bits.dm_que_status);
   printf("pu_que_status = %u\n", status->bits.pu_que_status);
   printf("mfu_que_status = %u\n", status->bits.mfu_que_status);
   printf("load_module_status = %u\n", status->bits.load_module_status);
   printf("store_module_status = %u\n", status->bits.store_module_status);
   printf("dm_module_status = %u\n", status->bits.dm_module_status);
   printf("pu_module_status = %u\n", status->bits.pu_module_status);
   printf("mfu_module_status = %u\n", status->bits.mfu_module_status);
   printf("version = %u\n", status->bits.version);
   printf("kpu_work_status = %u\n", status->bits.kpu_work_status);
   printf("exception_status = %u\n", status->bits.exception_status);
   printf("reset_status = %u\n", status->bits.reset_status);
   printf("intr_status = %u\n", status->bits.intr_status);
   printf("intr_num = %u\n", status->bits.intr_num);
}

void gnne_dump_pc()
{
   printf("dec_pc = 0x%08x\n", gnne_regs->dec_ld_st_mfu_pc.bits.dec_pc);
   printf("load_pc = 0x%08x\n", gnne_regs->dec_ld_st_mfu_pc.bits.load_pc);
   printf("store_pc = 0x%08x\n", gnne_regs->dec_ld_st_mfu_pc.bits.store_pc);
   printf("mfu_pc = 0x%08x\n", gnne_regs->dec_ld_st_mfu_pc.bits.mfu_pc);
   printf("pu_pc = 0x%08x\n", gnne_regs->pu_pc.bits.pu_pc);
   printf("dw_pc = 0x%08x\n", gnne_regs->pu_pc.bits.dw_pc);
   printf("act0_pc = 0x%08x\n", gnne_regs->pu_pc.bits.act0_pc);
   printf("act1_pc = 0x%08x\n", gnne_regs->pu_pc.bits.act1_pc);
   printf("dm_w_pc = 0x%08x\n", gnne_regs->dm_pc.bits.dm_w_pc);
   printf("dm_if_pc = 0x%08x\n", gnne_regs->dm_pc.bits.dm_if_pc);
   printf("dm_psum_pc = 0x%08x\n", gnne_regs->dm_pc.bits.dm_psum_pc);
   printf("dm_act_pc = 0x%08x\n", gnne_regs->dm_pc.bits.dm_act_pc);
}

void gnne_dump_ccr()
{
   volatile gnne_ccr_status status = gnne_regs->ccr_status;
   printf("ccr0 = %u\n", status.bits.ccr0);
   printf("ccr1 = %u\n", status.bits.ccr1);
   printf("ccr2 = %u\n", status.bits.ccr2);
   printf("ccr3 = %u\n", status.bits.ccr3);
   printf("ccr4 = %u\n", status.bits.ccr4);
   printf("ccr5 = %u\n", status.bits.ccr5);
   printf("ccr6 = %u\n", status.bits.ccr6);
   printf("ccr7 = %u\n", status.bits.ccr7);
   printf("ccr8 = %u\n", status.bits.ccr8);
   printf("ccr9 = %u\n", status.bits.ccr9);
   printf("ccr10 = %u\n", status.bits.ccr10);
   printf("ccr11 = %u\n", status.bits.ccr11);
   printf("ccr12 = %u\n", status.bits.ccr12);
   printf("ccr13 = %u\n", status.bits.ccr13);
   printf("ccr14 = %u\n", status.bits.ccr14);
   printf("ccr15 = %u\n", status.bits.ccr15);
   printf("ccr16 = %u\n", status.bits.ccr16);
   printf("ccr17 = %u\n", status.bits.ccr17);
   printf("ccr18 = %u\n", status.bits.ccr18);
   printf("ccr19 = %u\n", status.bits.ccr19);
   printf("ccr20 = %u\n", status.bits.ccr20);
   printf("ccr21 = %u\n", status.bits.ccr21);
   printf("ccr22 = %u\n", status.bits.ccr22);
   printf("ccr23 = %u\n", status.bits.ccr23);
   printf("ccr24 = %u\n", status.bits.ccr24);
   printf("ccr25 = %u\n", status.bits.ccr25);
   printf("ccr26 = %u\n", status.bits.ccr26);
   printf("ccr27 = %u\n", status.bits.ccr27);
   printf("ccr28 = %u\n", status.bits.ccr28);
   printf("ccr29 = %u\n", status.bits.ccr29);
   printf("ccr30 = %u\n", status.bits.ccr30);
   printf("ccr31 = %u\n", status.bits.ccr31);
}

uint64_t gnne_get_pc()
{
   return gnne_regs->dec_ld_st_mfu_pc.bits.dec_pc;
}

gnne_ctrl gnne_get_ctrl()
{
   return gnne_regs->ctrl;
}

gnne_dec_ld_st_mfu_pc gnne_get_dec_ld_st_mfu_pc()
{
   return gnne_regs->dec_ld_st_mfu_pc;
}

gnne_pu_pc gnne_get_pu_pc()
{
   return gnne_regs->pu_pc;
}

gnne_dm_pc gnne_get_dm_pc()
{
   return gnne_regs->dm_pc;
}

gnne_ccr_status gnne_get_ccr_status()
{
   return gnne_regs->ccr_status;
}

uint32_t gnne_get_time_out()
{
   return gnne_regs->time_out.bits.time_out_value;
}

void gnne_set_time_out(uint32_t val)
{
   gnne_regs->time_out.bits.time_out_value = val;
}

uint32_t gnne_get_ai2d_pc()
{
   return gnne_regs->ai2d_pc.bits.ai2d_pc_addr;
}

void gnne_set_ai2d_pc(uint32_t val)
{
   gnne_regs->ai2d_pc.bits.ai2d_pc_addr = val;
}