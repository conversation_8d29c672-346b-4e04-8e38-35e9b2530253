/* Copyright 2019-2020 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "runtime_module.h"
#include "runtime_function.h"
#include <cstring>
#include <nncase/functional/k230/dynamic_gnne_matmul.h>
#include <nncase/runtime/host_buffer.h>
#include <nncase/runtime/k230/gnne.h>
#include <nncase/runtime/runtime_loader.h>
#include <nncase/runtime/runtime_tensor.h>
#include <nncase/runtime/util.h>
#if !defined(NNCASE_SIMULATOR) && defined(__linux__)
#include <fcntl.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>
#endif

using namespace nncase;
using namespace nncase::runtime;
using namespace nncase::runtime::k230;

result<void> k230_runtime_module::initialize_before_functions(runtime_module_init_context &context) noexcept
{
    try_set(rdata_, context.get_or_read_section(".rdata", rdata_storage_, true));
    try_set(text_, context.get_or_read_section(".text", text_storage_, true));
    return ok();
}

result<std::unique_ptr<runtime_function>> k230_runtime_module::create_function() noexcept
{
    std::unique_ptr<runtime_function> mod(new (std::nothrow) k230_runtime_function(*this));
    if (mod)
        return ok(std::move(mod));
    return err(std::errc::not_enough_memory);
}

k230_runtime_module::k230_runtime_module()
    : runtime_module()
{
#if !defined(NNCASE_SIMULATOR)
    l2_addr_.paddr = (volatile void *)L2_BASE_ADDR;
    gnne_addr_.paddr = (volatile void *)GNNE_BASE_ADDR;

#if !defined(__linux__)
    gnne_set_base(gnne_addr_.paddr);
#else
    // open gnne dev
    char gnne_name[] = "/dev/gnne_device";
    int fd = open(gnne_name, O_RDWR);
    if (fd < 0)
    {
        std::cerr << "open " << gnne_name << " failed: " << strerror(errno) << std::endl;
        std::abort();
    }
    gnne_fd_ = fd;

    // open mem dev
    char mem_name[] = "/dev/mem";
    fd = open(mem_name, O_RDWR | O_SYNC);
    if (fd < 0)
    {
        std::cerr << "open " << mem_name << " failed: " << strerror(errno) << std::endl;
        std::abort();
    }
    mem_fd_ = fd;

    // mmap l2
    l2_addr_.mmap_size = 0x200;
    void *vaddr = mmap(NULL, l2_addr_.mmap_size, PROT_READ | PROT_WRITE, MAP_SHARED, mem_fd_, (off_t)l2_addr_.paddr);
    if (vaddr == MAP_FAILED)
    {
        std::cerr << "mmap failed: " << strerror(errno) << std::endl;
        std::abort();
    }
    l2_addr_.vaddr = (volatile void *)vaddr;

    // mmap gnne
    gnne_addr_.mmap_size = 0x210;
    vaddr = mmap(NULL, gnne_addr_.mmap_size, PROT_READ | PROT_WRITE, MAP_SHARED, mem_fd_, (off_t)gnne_addr_.paddr);
    if (vaddr == MAP_FAILED)
    {
        std::cerr << "mmap failed: " << strerror(errno) << std::endl;
        std::abort();
    }
    gnne_addr_.vaddr = (volatile void *)vaddr;

    gnne_set_base(gnne_addr_.vaddr);
#endif
#endif
}

k230_runtime_module::~k230_runtime_module()
{
#if !defined(NNCASE_SIMULATOR)
    l2_addr_.paddr = nullptr;
    gnne_addr_.paddr = nullptr;

#if defined(__linux__)
    // munmap l2
    int ret = munmap((void *)l2_addr_.vaddr, l2_addr_.mmap_size);
    if (ret != 0)
    {
        std::cerr << "munmap l2 base vaddr_ failed: " << strerror(errno);
        std::abort();
    }
    l2_addr_.vaddr = nullptr;
    l2_addr_.mmap_size = 0;

    // munmap gnne
    ret = munmap((void *)gnne_addr_.vaddr, gnne_addr_.mmap_size);
    if (ret != 0)
    {
        std::cerr << "munmap gnne base vaddr_ ailed: " << strerror(errno);
        std::abort();
    }
    gnne_addr_.vaddr = nullptr;
    gnne_addr_.mmap_size = 0;

    // close mem dev
    if (mem_fd_ > 0)
    {
        close(mem_fd_);
        mem_fd_ = -1;
    }

    // close gnne dev
    if (gnne_fd_ > 0)
    {
        close(gnne_fd_);
        gnne_fd_ = -1;
    }
#endif
#endif
}

result<std::unique_ptr<runtime_module>> k230::create_k230_runtime_module()
{
    std::unique_ptr<runtime_module> mod(new (std::nothrow) k230_runtime_module());
    if (mod)
        return ok(std::move(mod));
    return err(std::errc::not_enough_memory);
}

result<std::vector<std::pair<std::string, runtime_module::custom_call_type>>> k230::create_k230_custom_calls()
{
    std::vector<std::pair<std::string, runtime_module::custom_call_type>> calls {
        { "K230DynamicGNNEMatMul", functional::k230::dynamic_gnne_matmul }
    };
    return ok(calls);
}

extern "C"
{
    NNCASE_MODULES_K230_API void RUNTIME_MODULE_ACTIVATOR_NAME(result<std::unique_ptr<runtime_module>> &result)
    {
        result = create_k230_runtime_module();
    }

    NNCASE_MODULES_K230_API void RUNTIME_MODULE_COLLECTOR_NAME(result<std::vector<std::pair<std::string, runtime_module::custom_call_type>>> &result)
    {
        result = create_k230_custom_calls();
    }
}

#ifndef NNCASE_SIMULATOR
runtime_registration nncase::runtime::builtin_runtimes[] = {
    { k230_module_type, RUNTIME_MODULE_ACTIVATOR_NAME, RUNTIME_MODULE_COLLECTOR_NAME }, {}
};
#endif
