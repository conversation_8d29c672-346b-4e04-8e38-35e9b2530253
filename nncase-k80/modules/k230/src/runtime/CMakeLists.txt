﻿cmake_minimum_required (VERSION 3.13)

set(SRCS runtime_module.cpp
         runtime_function.cpp)

if (BUILDING_RUNTIME)
    if (ENABLE_K230_RUNTIME)
        list(APPEND SRCS host_allocator.cpp
                dynamic_function.device.cpp
                runtime_function.device.cpp
                gnne_tile_utils.cpp)

        if (K230_LINUX_SDK_DIR)
            list(APPEND SRCS mmz_allocator.cpp)
        endif()

        set_source_files_properties(host_allocator.cpp PROPERTIES COMPILE_FLAGS "-w -fpermissive")
        set_source_files_properties(dynamic_function.device.cpp PROPERTIES COMPILE_FLAGS "-w -fpermissive")
        set_source_files_properties(runtime_function.device.cpp PROPERTIES COMPILE_FLAGS "-w -fpermissive")

        list(APPEND SRCS gnne.c)
        set_source_files_properties(gnne.c PROPERTIES COMPILE_FLAGS "-w")

        add_library(runtime_k230 OBJECT ${SRCS})

        target_compile_definitions(runtime_k230 PRIVATE -D__GNNE_STACK_SIZE__=${K230_GNNE_STACK_SIZE})

        if (NOT MSVC)
            target_compile_options(runtime_k230 PRIVATE -Wno-pedantic -Wno-builtin-declaration-mismatch)
        endif()

        if (K230_SDK_DIR)
            target_include_directories(runtime_k230 PRIVATE ${K230_SDK_DIR}/bsp/cpu/include ${K230_SDK_DIR}/bsp/utils/include ${K230_SDK_DIR}/bsp/controler/include)
        elseif(K230_LINUX_SDK_DIR)
            target_include_directories(runtime_k230 PRIVATE ${K230_LINUX_SDK_DIR}/src/big/mpp/userapps/api ${K230_LINUX_SDK_DIR}/src/big/mpp/include ${K230_LINUX_SDK_DIR}/src/big/mpp/include/comm/)
            target_link_directories(runtime_k230 PUBLIC ${K230_LINUX_SDK_DIR}/src/big/mpp/userapps/lib ${K230_LINUX_SDK_DIR}/src/big/rt-smart/userapps/sdk/lib/risc-v/rv64 ${K230_LINUX_SDK_DIR}/src/big/rt-smart/userapps/sdk/rt-thread/lib/risc-v/rv64)
        endif()
        if (ENABLE_K230_RUNTIME_TRACE)
            target_compile_definitions(runtime_k230 PRIVATE -DENABLE_K230_RUNTIME_TRACE)
        endif()

        target_link_libraries(runtime_k230 PUBLIC nncaseruntime)
        set_target_properties(runtime_k230 PROPERTIES POSITION_INDEPENDENT_CODE ON)
        install(TARGETS runtime_k230 EXPORT nncase_rt_modules_k230Targets)
        if (K230_LINUX_SDK_DIR)
            target_link_libraries(runtime_k230 PUBLIC sys)
        endif()
    endif()
else()
    add_subdirectory(kpu_cmodel)
    list(APPEND SRCS dynamic_function.simulator.cpp
                     runtime_function.simulator.cpp
                     shared_memory.cpp
                     gnne_tile_utils.cpp)
    # if(MSVC)
    #     set(CMODEL_COMPILE_OPTIONS /wd4018 /wd4101 /wd4244 /wd4805)
    # else()
    #     set(CMODEL_COMPILE_OPTIONS -Wno-unused-parameter -Wno-sign-compare -Wno-unused-variable)
    # endif()
    # set_source_files_properties(runtime_function.simulator.cpp PROPERTIES
    #     COMPILE_OPTIONS "${CMODEL_COMPILE_OPTIONS}")

    add_library(simulator_k230 OBJECT ${SRCS})
    target_link_libraries(simulator_k230 PUBLIC nncasebase)
    target_link_libraries(simulator_k230 PRIVATE nncaseruntime stduuid::stduuid)
    target_compile_definitions(simulator_k230 PUBLIC -DNNCASE_MODULES_K230_DLL -DNNCASE_SIMULATOR)
    target_compile_definitions(simulator_k230 PRIVATE -D__GNNE_STACK_SIZE__=${K230_GNNE_STACK_SIZE} -DUUID_SYSTEM_GENERATOR)
    set_target_properties(simulator_k230 PROPERTIES POSITION_INDEPENDENT_CODE ON)
    if(APPLE)
      target_compile_options(simulator_k230 PRIVATE -Wno-deprecated-declarations)
      target_link_options(simulator_k230 PUBLIC -Wl,-framework -Wl,CoreFoundation)
    elseif(MSVC)
      target_compile_options(simulator_k230 PRIVATE /wd4244)
    else()
      target_link_libraries(simulator_k230 PRIVATE uuid rt)
    endif(APPLE)

endif()
