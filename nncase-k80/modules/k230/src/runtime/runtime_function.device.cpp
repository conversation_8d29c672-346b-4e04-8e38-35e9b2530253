/* Copyright 2019-2022 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "runtime_function.h"
#include "util.h"
#include <filesystem>
#include <fstream>
#include <iostream>
#include <nncase/object.h>
#include <nncase/runtime/dbg.h>
#include <nncase/runtime/k230/gnne.h>
#include <nncase/runtime/runtime_tensor.h>
#include <nncase/runtime/span_reader.h>
#include <nncase/tensor.h>
#include <sstream>

#if !defined(__linux__)
#include <interrupt.h>
#include <nncase/runtime/dbg.h>
#include <sleep.h>
#else
#include <sys/poll.h>
#endif

using namespace nncase;
using namespace nncase::runtime;
using namespace nncase::runtime::detail;
using namespace nncase::runtime::k230;

static volatile int g_ai_done = 0;
static gnne_status gnne_last_status;
static volatile uint64_t gnne_last_pc;
static volatile gnne_ctrl gnne_last_ctrl;
static int gmodel_count = 0;

#ifdef ENABLE_K230_RUNTIME_TRACE
#define read_cycle() __get_MCYCLE()
#define REAL_CPUFREQ (1600 * 1000000)
#define FREQ_MICRO (REAL_CPUFREQ / 1e3f)
#define TRACE_ENTRIES_MAX 1024 * 128 * 1

typedef struct _trace_entry
{
    uint64_t timestamp;
    gnne_dec_ld_st_mfu_pc dec_ld_st_mfu_pc;
    gnne_pu_pc pu_pc;
    gnne_dm_pc dm_pc;
    uint64_t status0;
    uint64_t status1;
} trace_entry_t;

static trace_entry_t test_perf_trace_entries[TRACE_ENTRIES_MAX];

static char encoding_table[] = { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H',
    'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P',
    'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X',
    'Y', 'Z', 'a', 'b', 'c', 'd', 'e', 'f',
    'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n',
    'o', 'p', 'q', 'r', 's', 't', 'u', 'v',
    'w', 'x', 'y', 'z', '0', '1', '2', '3',
    '4', '5', '6', '7', '8', '9', '+', '/' };

static uint32_t mod_table[] = { 0, 2, 1 };

char *base64_encode(const unsigned char *data,
    size_t input_length,
    size_t *output_length)
{
    *output_length = 4 * ((input_length + 2) / 3);

    char *encoded_data = (char *)malloc(*output_length + 1);
    if (encoded_data == NULL)
        return NULL;

    for (size_t i = 0, j = 0; i < input_length;)
    {
        uint32_t octet_a = i < input_length ? (unsigned char)data[i++] : 0;
        uint32_t octet_b = i < input_length ? (unsigned char)data[i++] : 0;
        uint32_t octet_c = i < input_length ? (unsigned char)data[i++] : 0;

        uint32_t triple = (octet_a << 0x10) + (octet_b << 0x08) + octet_c;

        encoded_data[j++] = encoding_table[(triple >> 3 * 6) & 0x3F];
        encoded_data[j++] = encoding_table[(triple >> 2 * 6) & 0x3F];
        encoded_data[j++] = encoding_table[(triple >> 1 * 6) & 0x3F];
        encoded_data[j++] = encoding_table[(triple >> 0 * 6) & 0x3F];
    }

    for (size_t i = 0; i < mod_table[input_length % 3]; i++)
    {
        encoded_data[*output_length - 1 - i] = '=';
    }

    encoded_data[*output_length] = '\0';
    return encoded_data;
}
#endif

static int done_thunk(NNCASE_UNUSED void *userdata)
{
    gnne_last_status = gnne_get_status();
    //    printf("intr_num: %d", gnne_last_status.intr_num);
    gnne_last_pc = gnne_get_pc();
    gnne_clear_cpu_intr();
    g_ai_done = 1;
    return 0;
}

result<tensor> alloc_new_device_tensor([[maybe_unused]] tensor param, const host_buffer_slice &input_buffer)
{
    try_var(host_input, hrt::create(dt_uint8, { input_buffer.size_bytes() }));
    try_var(host_input_buffer, host_input.impl()->buffer().as_host());

    try_var(mapped_input_buffer, input_buffer.map(map_read));
    try_var(mapped_host_input_buffer, host_input_buffer.map(map_write));

    memcpy(mapped_host_input_buffer.buffer().data(), mapped_input_buffer.buffer().data(), mapped_input_buffer.buffer().size_bytes());
    try_(host_input_buffer.sync(sync_write_back, true));

    return ok(host_input.impl());
}

result<value_t> k230_runtime_function::invoke_core([[maybe_unused]] gsl::span<value_t> parameters,
    [[maybe_unused]] value_t return_value) noexcept
{
#define SYNC(_tensor, _sync_op)                     \
    try_var(buffer, (_tensor)->buffer().as_host()); \
    buffer.sync(sync_op_t::_sync_op);

    gnne_init();

    // 1. Load sections
    std::vector<uint32_t> input_basement;
    std::vector<uint32_t> output_basement;
    uint32_t rdata_basement = 0;
    uint32_t data_basement = 0;
    // 1.1. .input
    {
        for (size_t i = 0; i < input_descs_.size(); i++)
        {
            auto input_tensor = parameters[i].as<tensor>().expect("input " + std::to_string(i) + " is not a tensor");
            try_var(buffer, input_tensor->buffer().as_host());
            if (!buffer.has_physical_address())
            {
                try_var(tensor, alloc_new_device_tensor(input_tensor, buffer));
                parameters[i] = tensor;
                buffer = tensor->buffer().as_host().unwrap();
            }
            buffer.sync(sync_op_t::sync_write_back);
            input_basement.push_back(buffer.physical_address().unwrap());
        }
    }

    // 1.2. .rdata
    {
        auto rdata = module().rdata_physical();
        rdata_basement = (uintptr_t)rdata.data();
    }

    // // 1.3. .text
    // {
    //     PC = (uint32_t)text_.start();
    // }

    // 1.4. .data
    {
        auto data = module().data_physical();
        data_basement = (uintptr_t)data.data();
    }

    // 1.5. .output
    {
        for (size_t i = 0; i < output_descs_.size(); ++i)
        {
            auto output = parameters[input_descs_.size() + i];
            if (output.is_a<tensor>())
            {
                try_var(output_tensor, output.as<tensor>());
                try_var(buffer, output_tensor->buffer().as_host());
#if defined(__linux__)
                buffer.sync(sync_op_t::sync_invalidate, true);
#endif
                output_basement.push_back(buffer.physical_address().unwrap());
            }
            else
            {
                return err(std::errc::not_supported);
            }
        }
    }
    // write gnne glb content.
    {
        // 1. write the basement
        volatile auto *glb_base = reinterpret_cast<volatile uint32_t *>(module().l2_base_addr());
        for (int i = 0; i < input_basement.size(); i++)
        {
            ((volatile uint32_t *)glb_base)[i] = input_basement[i];
        }
        for (int o = 0; o < output_basement.size(); o++)
        {
            ((volatile uint32_t *)glb_base)[input_basement.size() + o] = output_basement[o];
        }
        ((volatile uint32_t *)glb_base)[input_basement.size() + output_basement.size()] = rdata_basement;
        ((volatile uint32_t *)glb_base)[input_basement.size() + output_basement.size() + 1] = data_basement;
    }
    // set time out
    gnne_set_time_out(200000);

#if !defined(__linux__)
    plic_interrupt_enable();
    plic_set_priority(IRQN_GNNE_INTERRUPT, 1);
    plic_irq_register(IRQN_GNNE_INTERRUPT, done_thunk, this);
    plic_irq_enable(IRQN_GNNE_INTERRUPT);

    clean_cache();

    //    printf("run\n");
    // 2. Run
    g_ai_done = 0;
    CHECK_WITH_ERR(gnne_enable((uint64_t)text_.begin(), (uint64_t)text_.end(), 0) == 0, std::errc::device_or_resource_busy);

#ifdef ENABLE_K230_RUNTIME_TRACE
    uint64_t pc_begin = (uint64_t)text_.begin();
    // wait gnne return
    [[maybe_unused]] uint64_t cycle_begin = read_cycle();
    trace_entry_t last_entry;
    static size_t entry_cur = 0;
#endif
    std::vector<gnne_pu_pc> pu_pcs;
    std::vector<gnne_dm_pc> dm_pcs;
    std::vector<gnne_dec_ld_st_mfu_pc> decs;
    while (!g_ai_done)
    {
#ifdef ENABLE_K230_RUNTIME_TRACE
        trace_entry_t new_entry;
        // new_entry.timestamp = read_cycle() - cycle_begin;
        new_entry.timestamp = read_cycle();
        new_entry.dec_ld_st_mfu_pc = gnne_get_dec_ld_st_mfu_pc();
        new_entry.pu_pc = gnne_get_pu_pc();
        new_entry.dm_pc = gnne_get_dm_pc();
        new_entry.status0 = gnne_get_status().data[0];
        new_entry.status1 = gnne_get_status().data[1];

        // new_entry.dec_ld_st_mfu_pc.dec_pc -= pc_begin;
        // new_entry.dec_ld_st_mfu_pc.load_pc -= pc_begin;
        // new_entry.dec_ld_st_mfu_pc.store_pc -= pc_begin;
        // new_entry.dec_ld_st_mfu_pc.mfu_pc -= pc_begin;

        // new_entry.pu_pc.pu_pc -= pc_begin;
        // new_entry.pu_pc.dw_pc -= pc_begin;
        // new_entry.pu_pc.act0_pc -= pc_begin;
        // new_entry.pu_pc.act1_pc -= pc_begin;

        // new_entry.dm_pc.dm_w_pc -= pc_begin;
        // new_entry.dm_pc.dm_if_pc -= pc_begin;
        // new_entry.dm_pc.dm_psum_pc -= pc_begin;
        // new_entry.dm_pc.dm_act_pc -= pc_begin;

        if (entry_cur < TRACE_ENTRIES_MAX && memcmp(&new_entry.dec_ld_st_mfu_pc, &last_entry.dec_ld_st_mfu_pc, sizeof(new_entry) - sizeof(uint64_t)))
        {
            test_perf_trace_entries[entry_cur++] = new_entry;
            last_entry = new_entry;
        }
#endif
    }

    if (gnne_last_status.bits.kpu_work_status != GNNE_STATUS_IDLE)
    {
        std::cerr << "kpu_work_status = " << gnne_last_status.bits.kpu_work_status << std::endl;
        std::cerr << "kpu_exception_status = " << gnne_last_status.bits.exception_status << std::endl;
        std::cerr << "axi_bresp_error = " << gnne_last_status.bits.axi_bresp_error << std::endl;

        if (gnne_last_status.bits.exception_status == GNNE_EXCEPTION_CCR_ERROR)
        {
            gnne_dump_ccr();
        }
        exit(1);
    }

#ifdef ENABLE_K230_RUNTIME_TRACE
    trace_entry_t new_entry;
    memset(&new_entry, sizeof(new_entry), 0);
    // new_entry.timestamp = read_cycle() - cycle_begin;
    new_entry.timestamp = read_cycle();
    printf("\n");
    printf("kpu time: %f,%f,%f\n", cycle_begin / (float)FREQ_MICRO, new_entry.timestamp / (float)FREQ_MICRO, (new_entry.timestamp - cycle_begin) / (float)FREQ_MICRO);
    test_perf_trace_entries[entry_cur++] = new_entry;
    // if (gmodel_count == 0)
    //     printf("PC_BEGIN %ld\n", pc_begin);
    gmodel_count++;
    //printf("TRACE %d %p %d:", REAL_CPUFREQ, test_perf_trace_entries, (int)entry_cur);
    size_t out_len = 0;
    char *encoded_entry = base64_encode((unsigned char *)test_perf_trace_entries, entry_cur * sizeof(trace_entry_t), &out_len);
    cache_flush();
    //printf("TRACE %d %ld %ld %d %d\n", REAL_CPUFREQ, encoded_entry, encoded_entry + (int)out_len - 1, (int)entry_cur, (int)out_len);
    // wait for memdump cmd qel file generate
    //msleep(30);
    // signal for sarun.tcl or run.qel
    //printf("{");
    //puts(encoded_entry);
    printf("\n");
#endif
#else
    // 2. Run
#ifdef ENABLE_K230_RUNTIME_TRACE
    double begin_time = (double)clock() / 1000;
#endif
    CHECK_WITH_ERR(gnne_enable((uint64_t)text_.begin(), (uint64_t)text_.end(), 0) == 0, std::errc::device_or_resource_busy);

    // wait for
    pollfd fds;
    fds.fd = module().gnne_fd();
    fds.events = POLLIN;
    poll(&fds, 1, -1);
#ifdef ENABLE_K230_RUNTIME_TRACE    
    double end_time = (double)clock() / 1000;
    printf("kpu time: %f,%f,%f\n", begin_time, end_time, end_time - begin_time);
#endif    
#endif

    // 3. Invalidate outputs
#if !defined(__linux__)
    {
        for (size_t i = 0; i < output_descs_.size(); i++)
        {
            auto output = parameters[input_descs_.size() + i];
            if (output.is_a<tensor>())
            {
                try_var(output_tensor, output.as<tensor>());
                try_var(buffer, output_tensor->buffer().as_host());
                buffer.sync(sync_op_t::sync_invalidate, true);
            }
            else
            {
                return err(std::errc::not_supported);
            }
        }
    }
#endif
    return ok<value_t>(tuple(std::in_place));
}
