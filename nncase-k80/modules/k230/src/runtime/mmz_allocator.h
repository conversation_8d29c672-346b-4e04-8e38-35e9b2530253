/* Copyright 2019-2020 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include <atomic>
#include <nncase/runtime/k230/compiler_defs.h>
#include <nncase/runtime/result.h>
#include "mpi_sys_api.h"
#include <mutex>
BEGIN_NS_NNCASE_RT_K230

#define USE_CACHE 1

class mmz_allocator
{
    struct mmz_segment
    {
        uintptr_t paddr;
        size_t bytes;

        uint8_t *body() noexcept { return reinterpret_cast<uint8_t *>(this) + sizeof(mmz_segment); }
    };

    struct free_heap_node
    {
        mmz_segment *segment;
        size_t bytes;
        free_heap_node *next;

        void *end() noexcept
        {
            return reinterpret_cast<uint8_t *>(this) + bytes;
        }
    };

    struct alloc_header
    {
        mmz_segment *segment;
        size_t bytes;

        uint8_t *body() noexcept { return reinterpret_cast<uint8_t *>(this) + sizeof(alloc_header); }
        size_t content_len() const noexcept { return bytes - sizeof(alloc_header); }

        void *end() noexcept
        {
            return reinterpret_cast<uint8_t *>(this) + bytes;
        }
    };

public:
    mmz_allocator() noexcept
        : avail_bytes_(0) { }

    result<void> allocate(size_t bytes, void *&vaddr, uintptr_t &paddr) noexcept;
    void free(void *ptr) noexcept;
    void destory();
    void destory_node(free_heap_node *pre, free_heap_node *node);

private:
    static alloc_header *header(void *ptr) noexcept
    {
        return reinterpret_cast<alloc_header *>(reinterpret_cast<uint8_t *>(ptr) - sizeof(alloc_header));
    }

    void insert_free_node(void *base, mmz_segment *segment, size_t count);
    void merge_node(free_heap_node *prev, free_heap_node *cnt, free_heap_node *next) noexcept;
    void try_free_segment(free_heap_node *node) noexcept;
    result<mmz_segment *> allocate_segment(size_t pages) noexcept;
    void free_segment(mmz_segment *segment) noexcept;
    void sanity_check();

private:
    std::atomic<size_t> avail_bytes_;
    free_heap_node *head_ = nullptr;
    std::mutex poolMutex;
};

END_NS_NNCASE_RT_K230
