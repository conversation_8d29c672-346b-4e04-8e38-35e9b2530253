/* Copyright 2019-2020 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "mmz_allocator.h"
#include <assert.h>
#include <nncase/runtime/dbg.h>

using namespace nncase;
using namespace nncase::runtime;
using namespace nncase::runtime::k230;

namespace
{
#define ALLOCATE_ALIGNMENT 16
#define PAGE_SIZE 4096

template <class T, class U, class = std::enable_if_t<std::is_integral_v<T> && std::is_integral_v<U>>>
constexpr T align(T value, U alignment) noexcept
{
    auto rem = value % alignment;
    return rem ? value + (alignment - rem) : value;
}

template <class T, class U, class = std::enable_if_t<std::is_integral_v<T> && std::is_integral_v<U>>>
constexpr T align_down(T value, U alignment) noexcept
{
    return value / alignment * alignment;
}

template <class T, class U, class = std::enable_if_t<std::is_integral_v<T> && std::is_integral_v<U>>>
constexpr T ceil_div(T numerator, U denominator) noexcept
{
    return (numerator + (denominator - 1)) / denominator;
}
}

result<void> mmz_allocator::allocate(size_t bytes, void *&vaddr, uintptr_t &paddr) noexcept
{
    std::lock_guard<std::mutex> lock(poolMutex);
    if (!bytes)
    {
        vaddr = nullptr;
        paddr = 0;
        return ok();
    }

    alloc_header *header = nullptr;
    auto required_bytes = align(bytes, ALLOCATE_ALIGNMENT) + sizeof(alloc_header);

    // 1. Allocate from free nodes
    if (avail_bytes_ >= bytes)
    {
        sanity_check();
        free_heap_node *prev = nullptr;
        free_heap_node *cnt = head_;
        while (cnt)
        {
            auto avail = cnt->bytes;

            // 1.1. Split
            if (avail >= required_bytes + sizeof(free_heap_node))
            {
                cnt->bytes -= required_bytes;
                header = reinterpret_cast<alloc_header *>(cnt->end());
                header->segment = cnt->segment;
                header->bytes = required_bytes;
                avail_bytes_ -= required_bytes;
                break;
            }
            // 1.2. Remove
            else if (avail >= required_bytes)
            {
                if (prev)
                    prev->next = cnt->next;
                else
                    head_ = cnt->next;
                auto bytes = cnt->bytes;
                header = reinterpret_cast<alloc_header *>(cnt);
                header->segment = cnt->segment;
                header->bytes = bytes;
                avail_bytes_ -= bytes;
                break;
            }

            prev = cnt;
            cnt = cnt->next;
        }
    }

    // 2. Allocate from new page
    if (!header)
    {
        auto page_num = ceil_div(required_bytes + sizeof(mmz_segment), PAGE_SIZE);
        try_var(segment, allocate_segment(page_num));
        // 2.1. Init allocate hader
        header = reinterpret_cast<alloc_header *>(segment->body());
        header->segment = segment;
        header->bytes = required_bytes;
        // 2.2. Rest free space
        auto rest_free = segment->bytes - required_bytes;
        if (rest_free >= sizeof(free_heap_node))
            insert_free_node(reinterpret_cast<uint8_t *>(segment->body()) + required_bytes, segment, rest_free);
        else
            header->bytes = segment->bytes;
    }

    if (header)
    {
        vaddr = header->body();
        auto diff = reinterpret_cast<uintptr_t>(vaddr) - reinterpret_cast<uintptr_t>(header->segment);
        paddr = header->segment->paddr + diff;
        return ok();
    }

    return err(std::errc::not_enough_memory);
}

void mmz_allocator::destory_node(free_heap_node* prev, free_heap_node* node)
{
    if(node != nullptr)
    {
        destory_node(node, node->next);
        if (node->bytes == node->segment->bytes)
        {
            // Free segment
            if (prev)
                prev->next = node->next;
            else
                head_ = nullptr;

            avail_bytes_ -= node->bytes;
            free_segment(node->segment);

        }
        // else{
        //     std::cout <<__FUNCTION__<<":"<<__LINE__<< " destory mem node fail" << std::endl;
        // }
    }
    sanity_check();
}

void mmz_allocator::destory()
{
    destory_node(nullptr, head_);
}

void mmz_allocator::free(void *ptr) noexcept
{
    std::lock_guard<std::mutex> lock(poolMutex);
    if (!ptr)
        return;

    auto h = header(ptr);
    insert_free_node(h, h->segment, h->bytes);
}

void mmz_allocator::insert_free_node(void *base, mmz_segment *segment, size_t bytes)
{
    assert(bytes >= sizeof(free_heap_node));
    sanity_check();
    avail_bytes_ += bytes;

    // 1. No free
    if (!head_)
    {
        auto node = reinterpret_cast<free_heap_node *>(base);
        node->segment = segment;
        node->bytes = bytes;
        node->next = nullptr;
        head_ = node;
        sanity_check();
    }
    else
    {
        free_heap_node *prev = nullptr;
        free_heap_node *cnt = head_;
        // 2.1. Skip previous
        while (cnt && base > cnt)
        {
            prev = cnt;
            cnt = cnt->next;
        }

        auto node = reinterpret_cast<free_heap_node *>(base);
        node->segment = segment;
        node->bytes = bytes;
        node->next = cnt;
        if (prev)
            prev->next = node;
        else
            head_ = node;
        sanity_check();
        merge_node(prev, node, cnt);
    }
}

void mmz_allocator::merge_node(free_heap_node *prev, free_heap_node *cnt, free_heap_node *next) noexcept
{
    if (cnt->end() == next)
    {
        cnt->bytes += next->bytes;
        cnt->next = next->next;
    }

    if (prev && prev->end() == cnt)
    {
        prev->bytes += cnt->bytes;
        prev->next = cnt->next;
        try_free_segment(prev);
    }
    else
    {
        try_free_segment(cnt);
    }

    sanity_check();
}

void mmz_allocator::try_free_segment([[maybe_unused]] free_heap_node *node) noexcept
{
#if 0
    if (node->bytes == node->segment->bytes)
    {
        // Find prev node
        free_heap_node *prev = nullptr, *cnt = head_;
        while (cnt != node)
        {
            prev = cnt;
            cnt = cnt->next;
        }

        if (prev)
            prev->next = node->next;
        else
            head_ = node->next;

        // Free segment
        avail_bytes_ -= node->bytes;
        free_segment(node->segment);
    }
#endif
}

result<mmz_allocator::mmz_segment *> mmz_allocator::allocate_segment(size_t pages) noexcept
{
    uintptr_t paddr;
    void *vaddr;
    size_t bytes = pages * PAGE_SIZE;
#if USE_CACHE
    int ret = kd_mpi_sys_mmz_alloc_cached(&paddr, &vaddr, "allocate", "anonymous", bytes);
#else
    int ret = kd_mpi_sys_mmz_alloc(&paddr, &vaddr, "allocate", "anonymous", bytes);
#endif
    if (ret)
    {
        std::cerr << __FUNCTION__ << "failed: ret = " << ret << std::endl;
        std::abort();
    }
    CHECK_WITH_ERR(!ret, std::errc::not_enough_memory);
    auto segment = reinterpret_cast<mmz_segment *>(vaddr);
    segment->paddr = paddr;
    segment->bytes = bytes - sizeof(mmz_segment);
    return ok(segment);
}

void mmz_allocator::free_segment(mmz_segment *segment) noexcept
{
    int ret = kd_mpi_sys_mmz_free(segment->paddr, reinterpret_cast<void *>(segment));
    if (ret)
    {
        std::cerr << __FUNCTION__ << "failed: ret = " << ret << std::endl;
        std::abort();
    }
    assert(!ret);
}

void mmz_allocator::sanity_check()
{
#if 1
    size_t avail = 0;
    free_heap_node *cnt = head_;
    // 2.1. Skip previous
    while (cnt)
    {
        avail += cnt->bytes;
        cnt = cnt->next;
    }
    assert(avail_bytes_ == avail);

#endif
}
