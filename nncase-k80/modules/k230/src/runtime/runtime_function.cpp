/* Copyright 2019-2020 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include "runtime_function.h"
#include <nncase/runtime/dbg.h>
#include <nncase/runtime/k230/error.h>
#include <nncase/runtime/runtime_loader.h>
#include <nncase/runtime/runtime_tensor.h>
#include <nncase/runtime/span_reader.h>

using namespace nncase;
using namespace nncase::runtime;
using namespace nncase::runtime::k230;

k230_runtime_module &k230_runtime_function::module() const noexcept
{
    return static_cast<k230_runtime_module &>(runtime_function::module());
}

result<void> k230_runtime_function::initialize_core(runtime_function_init_context &context) noexcept
{
    text_ = module().text_physical().subspan(context.header().entrypoint, context.header().text_size);
    try_(context.read_section(".desc", [this](auto sr, size_t) -> result<void>
        {
        auto header = sr.template read<desc_header>();
        if (parameters_size() != header.inputs + header.outputs)
            return nncase::err(std::errc::invalid_argument);
        
        for (uint32_t i = 0; i < header.inputs; i++)
        {
            input_descs_.emplace_back(sr.template read<memory_range>());
        }

        for (uint32_t i = 0; i < header.outputs; i++)
        {
            output_descs_.emplace_back(sr.template read<memory_range>());
        }

        return ok(); }));

    return ok();
}

const memory_range &k230_runtime_function::input_desc(size_t i) const noexcept
{
    return input_descs_[i];
}
const memory_range &k230_runtime_function::output_desc(size_t i) const noexcept
{
    return output_descs_[i];
}
