#ifndef _FP16_H_
#define _FP16_H_

#include "lib_arith.h"
#include <cmath>
#include <iostream>
#include <vector>
#include "Eigen/Dense"
#include <cfloat>

#define SAT(x, min, max) (((x) < (min)) ? (min) : (((x) > (max)) ? (max) : (x)))
#define BITSEL(x, pos) ((x >> (pos)) & 0x1)

namespace FP16
{

inline int32_t component(uint32_t v, uint8_t s)
{
    return (1 - 2 * s) * (int32_t)v;
}
struct fp16
{
    struct F32_BITS
    {
        uint32_t fraction : 23;
        uint32_t exponent : 8;
        uint32_t sign : 1;
    };
    struct F16_BITS
    {
        uint16_t fraction : 10;
        uint16_t exponent : 5;
        uint16_t sign : 1;
    };
    union FP32
    {
        unsigned int u;
        float f;
        F32_BITS f32_bits;
    };
    union F16_UNION
    {
        uint16_t value;
        F16_BITS f16_bits;
    };
    fp16() : value(ZERO_VALUE) {}

    explicit fp16(const float v)
    {
        value = round_to_fp16(v).value;
    }

    explicit fp16(const double val)
        : fp16(static_cast<float>(val)) {}

    explicit fp16(const unsigned short val)
        : fp16(static_cast<float>(val)) {}

    explicit fp16(const unsigned int val)
        : fp16(static_cast<float>(val)) {}

    explicit fp16(const int val)
        : fp16(static_cast<float>(val)) {}

    explicit fp16(const int val, int shift)
    {
        auto Msb = [](uint32_t x)
        {
            int msb_x = 0;
            while (1)
            {
                x >>= 1;
                if (x == 0)
                {
                    break;
                }
                ++msb_x;
            }

            return msb_x;
        };
        uint32_t x = *((uint32_t *)(&val));
        if (x == 0)
        {
            this->value = 0;
        }
        else
        {
            int sign_x = (val < 0 ? 1 : 0);
            if (sign_x == 1)
            {
                x = (x ^ 0xFFFFFFFF) + 1;
            }
            int msb_x = Msb(x);
            int32_t expo = msb_x + 15 - shift;
            uint32_t frac = msb_x > 10 ? x >> (msb_x - 10) : x << (10 - msb_x);
            if (expo>30)
            {
                expo = 30;
                frac = (1<<10)-1;
            }
            
            if(expo <= 0) // subnormal
            {
                frac = frac>>(1-expo); 
                expo = 0;
            }

            this->value = (sign_x << 15) | (expo << 10) | (frac & 0x3FF);
        }
    }

    explicit fp16(const long val)
        : fp16(static_cast<float>(val)) {}

    explicit fp16(const long long val)
        : fp16(static_cast<float>(val)) {}

    template <class T>
    explicit fp16(const T &val)
        : fp16(static_cast<float>(val)) {}

    explicit operator float() const
    {
        FP32 result;
        result.f = 0;
        F16_UNION tmp_f16;
        tmp_f16.value = this->value;
        result.f32_bits.sign = tmp_f16.f16_bits.sign;
        result.f32_bits.fraction = tmp_f16.f16_bits.fraction << 13;
        if (tmp_f16.f16_bits.exponent == 0)
        {
            result.f32_bits.exponent = 0;
            if(tmp_f16.f16_bits.fraction!=0)
            {
                int lead_one = 32-norm_uint(tmp_f16.f16_bits.fraction);
                int shift = 24 - lead_one;
                result.f32_bits.fraction = (tmp_f16.f16_bits.fraction << shift)&(0x7FFFFFF);
                result.f32_bits.exponent = 126-shift;
            }
        }
        else if (tmp_f16.f16_bits.exponent == 31)
        {
            result.f32_bits.exponent = 255;
        }
        else
        {
            result.f32_bits.exponent = tmp_f16.f16_bits.exponent - 15 + 127;
        }
        return result.f;
    }

    float f_value();

    explicit operator bool() const
    {
        return static_cast<bool>(float(*this));
    }

    explicit operator short() const
    {
        // return static_cast<short>(float(*this));
        F16_UNION tmp_f16;
        short res;
        tmp_f16.value = this->value;
        res = component((1 << 10) + tmp_f16.f16_bits.fraction, tmp_f16.f16_bits.sign);
        if (int32_t(tmp_f16.f16_bits.exponent) - 15 > 14)
        {
            res = (tmp_f16.f16_bits.sign) ? (MIN_INT(16)+1) : (MAX_INT(16));
        }
        else if (int32_t(tmp_f16.f16_bits.exponent) - 15 <= -2)
        {
            res = 0;
        }
        else if (int32_t(tmp_f16.f16_bits.exponent) - 15 >= 10 && int32_t(tmp_f16.f16_bits.exponent) - 15 <= 14)
        {
            res <<= (tmp_f16.f16_bits.exponent - 15 - 10);
        }
        else
        {
            int16_t fwl = 10 - (tmp_f16.f16_bits.exponent - 15);
            int16_t last_int_bit = BITSEL(res, fwl);
            res = (res + (1 << (fwl - 1)) - 1 + last_int_bit) >> fwl;
        }
        return res;
    }

    explicit operator int() const
    {
        fp16 tmp_res = *this;
        if (std::isnan(float(*this)) or std::isinf(float(*this)))
        {
            if (tmp_res.value>>15 == 1)
            {
                tmp_res.value = MIN_VALUE;
            }
            else
            {
                tmp_res.value = MAX_VALUE;
            }
        }
        return static_cast<int>(float(tmp_res));
    }

    explicit operator long() const
    {
        return static_cast<long>(float(*this));
    }

    explicit operator char() const
    {
        // return static_cast<char>(float(*this));
        short tmp_i16 = short(*this);
        char res;
        res = (char)SAT(tmp_i16, MIN_INT(8), MAX_INT(8));
        return res;
    }

    explicit operator signed char() const
    {
        short tmp_i16 = short(*this);
        signed char res;
        res = (signed char)SAT(tmp_i16, MIN_INT(8)+1, MAX_INT(8));
        return res;
    }

    explicit operator unsigned char() const
    {
        short tmp_i16 = short(*this);
        unsigned char res;
        res = (unsigned char)SAT(tmp_i16, short(0), short(MAX_UINT(8)));
        return res;
    }

    explicit operator unsigned short() const
    {
        return static_cast<unsigned short>(float(*this));
    }

    explicit operator unsigned int() const
    {
        return static_cast<unsigned int>(float(*this));
    }

    explicit operator unsigned long() const
    {
        return static_cast<unsigned long>(float(*this));
    }

    explicit operator unsigned long long() const
    {
        return static_cast<unsigned long long>(float(*this));
    }

    explicit operator long long() const
    {
        return static_cast<long long>(float(*this));
    }

    explicit operator double() const
    {
        return static_cast<double>(float(*this));
    }

    // Converts a float point to fp16, with round-nearest-to-even as rounding
    // method.
    static fp16 round_to_fp16(float v)
    {
        FP32 f;
        f.f = v;
        fp16 output;
        F16_UNION tmp_f16;

        if (float_isnan(v))
        {
            tmp_f16.value = NAN_VALUE;
            tmp_f16.f16_bits.sign = f.f32_bits.sign;
        }
        else if (std::isinf(v))
        {
            tmp_f16.value = INF_VALUE;
            tmp_f16.f16_bits.sign = f.f32_bits.sign;
        }
        else if ((int32_t(f.f32_bits.exponent) - 127 + 15) < -10)
        {
            tmp_f16.value = 0;
            tmp_f16.f16_bits.sign = f.f32_bits.sign;
        }
        else if ((int32_t(f.f32_bits.exponent) - 127 + 15) == 0)
        {
            int32_t shift = 126-int32_t(f.f32_bits.exponent);
            tmp_f16.value = 0;
            tmp_f16.f16_bits.sign = f.f32_bits.sign;
            uint32_t frac = f.f32_bits.fraction + (1<<23);// 24bit frac+heading
            frac += (1<<(shift-1))-1+BITSEL(frac, shift);
            tmp_f16.f16_bits.fraction = (frac>>shift)&0x3FF;
            tmp_f16.f16_bits.exponent = BITSEL(frac,24);
        }
        else if ((int32_t(f.f32_bits.exponent) - 127 + 15) < 0)// subnormal
        {
            int32_t shift = 126-int32_t(f.f32_bits.exponent);
            tmp_f16.value = 0;
            tmp_f16.f16_bits.sign = f.f32_bits.sign;
            uint32_t frac = f.f32_bits.fraction + (1<<23);// 24bit frac+heading
            frac += (1<<(shift-1))-1+BITSEL(frac, shift);
            tmp_f16.f16_bits.fraction = (frac>>shift)&0x3FF;
        }
        else if ((int32_t(f.f32_bits.exponent) - 127 + 15) >= 31)
        {
            tmp_f16.value = 0x7C00;
            tmp_f16.f16_bits.sign = f.f32_bits.sign;
        }
        else
        {
            bool s_y = f.f32_bits.sign;
            uint8_t e_y = f.f32_bits.exponent - 127 + 15;
            uint32_t f_y = (1 << 23) + f.f32_bits.fraction;
            f_y += (1 << 12) - 1 + BITSEL(f_y, 13);
            if (BITSEL(f_y, 24))
            {
                e_y = e_y + 1;

                if (e_y >=31)
                {
                    e_y = 31;
                    f_y = 0;
                }
                else
                {
                    f_y = (f_y >> 14) & 0x3FF;
                }
            }
            else
            {
                f_y = (f_y >> 13) & 0x3FF;
            }
            tmp_f16.f16_bits.sign = s_y;
            tmp_f16.f16_bits.exponent = e_y;
            tmp_f16.f16_bits.fraction = f_y;
        }
        output.value = tmp_f16.value;

        return output;
    }

    static fp16 highest()
    {
        fp16 x;
        x.value = 0x7BFF; // 0x1.3FFp15
        return x;
    }

    static fp16 lowest()
    {
        fp16 x;
        x.value = 0xFBFF; // -0x1.3FFp15
        return x;
    }

    static fp16 min_positive_normal()
    {
        fp16 x;
        x.value = 0x0400; // 0x1p-14
        return x;
    }

    fp16 fp16_2exp_shift(int8_t shift_value)
    {
        fp16 result;
        F16_UNION tmp_f16;
        int8_t exponent_value;

        tmp_f16.value = this->value;
        exponent_value = tmp_f16.f16_bits.exponent;
        exponent_value = exponent_value + shift_value;
        int lead_one = 32-norm_uint(tmp_f16.f16_bits.fraction);// 0~10

        if(tmp_f16.f16_bits.exponent == 31)
        {
            result.value = tmp_f16.value;
        }
        else if (tmp_f16.f16_bits.exponent == 0 and exponent_value<=0)// subnormal >> subnormal
        {
            tmp_f16.f16_bits.fraction >>= (-shift_value);
            result.value = tmp_f16.value;
        }
        else if(tmp_f16.f16_bits.exponent == 0 and exponent_value>=(11-lead_one) and lead_one!=0)// subnormal << normal
        {
            tmp_f16.f16_bits.exponent = exponent_value-(10-lead_one);
            if(tmp_f16.f16_bits.exponent==31)
            {
                tmp_f16.f16_bits.exponent= 30;
                tmp_f16.f16_bits.fraction = 0x3FF;
            }
            else
            {
                tmp_f16.f16_bits.fraction = (tmp_f16.f16_bits.fraction<<(11-lead_one))&0x3FF;
            }
            result.value = tmp_f16.value;
        }
        else if(tmp_f16.f16_bits.exponent == 0)// subnormal << subnormal
        {
            tmp_f16.f16_bits.fraction = tmp_f16.f16_bits.fraction << shift_value;
            result.value = tmp_f16.value;
        }
        else if (exponent_value<=0)// normal >> subnormal
        {
            tmp_f16.f16_bits.fraction = (tmp_f16.f16_bits.fraction+(1<<10))>>(-shift_value-tmp_f16.f16_bits.exponent+1);
            tmp_f16.f16_bits.exponent = 0;
            result.value = tmp_f16.value;
        }
        else if (exponent_value >= 31)
        {
            result.value = (this->value & 0x8000) + 0x7BFF;
        }
        else
        {
            tmp_f16.f16_bits.exponent = exponent_value;
            result.value = tmp_f16.value;
        }

        return result;
    }

    bool IsZero() const { return (value & 0x7FFF) == ZERO_VALUE; }

    uint16_t value;

    // A value that represents "not a number".
    static const uint16_t NAN_VALUE = 0x7E00;
    static const uint16_t INF_VALUE = 0x7C00;
    static const uint16_t NEGINF_VALUE = 0xFC00;
    static const uint16_t MAX_VALUE = 0x7BFF;
    static const uint16_t MIN_VALUE = 0xFBFF;

private:
    // A value that represents "zero".
    static const uint16_t ZERO_VALUE = 0;

    static bool float_isnan(const float &x)
    {
        return std::isnan(x);
    }
};

inline std::ostream &operator<<(std::ostream &os, const fp16 &dt)
{
    os << static_cast<float>(dt);
    return os;
}

inline fp16 AddTwoFp16Simp(fp16 a, fp16 b)
{

    fp16 ret;
    if (std::isnan(float(a)) || std ::isnan(float(b)))
    {
        ret.value = ret.NAN_VALUE;
    }
    else if (std::isinf(float(a)) || std::isinf(float(b)))
    {
        ret.value = ret.INF_VALUE;
    }
    else if (((a.value & 0x7C00) == 0) && ((b.value & 0x7C00) == 0))
    {
        ret.value = 0;
    }
    else if ((a.value & 0x7C00) == 0)
    {
        ret.value = b.value;
    }
    else if ((b.value & 0x7C00) == 0)
    {
        ret.value = a.value;
    }
    else
    {
        fp16::F16_UNION tmp_a;
        fp16::F16_UNION tmp_b;
        fp16::F16_UNION tmp_res;
        tmp_a.value = a.value;
        tmp_b.value = b.value;
        // swap(a,b)
        if (tmp_a.f16_bits.exponent < tmp_b.f16_bits.exponent)
        {
            tmp_a.value = b.value;
            tmp_b.value = a.value;
        }
        int16_t t1 = component(((1 << 10) + tmp_a.f16_bits.fraction) << 1, tmp_a.f16_bits.sign);
        int16_t t2 = component(
            (((1 << 10) + tmp_b.f16_bits.fraction) << 1) >> (tmp_a.f16_bits.exponent - tmp_b.f16_bits.exponent),
            tmp_b.f16_bits.sign);
        int16_t z = t1 + t2;
        tmp_res.f16_bits.sign = (z < 0);
        int16_t e_y = tmp_a.f16_bits.exponent;
        if (z == 0)
        {
            tmp_res.value = 0;
            ret.value = tmp_res.value;
            return ret;
        }
        else
        {
            uint8_t k = norm_uint(abs(z)) - (32 - 13); // 12bit的leadingzeros
            z = abs(z) << k;
            e_y -= k;
            // norm
            e_y += 1;
            z >>= 2;
            if (e_y >= 31) //inf
            {   
                e_y = 30;
                tmp_res.f16_bits.fraction = 0x3FF;
            }
            else if (e_y <= 0)
            {
                e_y = 0;
                tmp_res.f16_bits.fraction = 0;
            }
            else
            {
                tmp_res.f16_bits.fraction = z & 0x3FF;
            }
            tmp_res.f16_bits.exponent = e_y;
            ret.value = tmp_res.value;
        }
    }

    return ret;
}
inline fp16 operator+(fp16 a, fp16 b)
{
    // return fp16(static_cast<float>(a) - static_cast<float>(b));
    Eigen::half src1_eig_f16;
    Eigen::half src2_eig_f16;
    Eigen::half dst_eig_f16;
    fp16 dst_f16;
    src1_eig_f16 = Eigen::half_impl::raw_uint16_to_half(a.value);
    src2_eig_f16 = Eigen::half_impl::raw_uint16_to_half(b.value);
    dst_eig_f16 = src1_eig_f16 + src2_eig_f16;
    dst_f16.value = Eigen::numext::bit_cast<uint16_t>(dst_eig_f16.x);

    if (std::isinf(float(a)) && std::isinf(float(b))) 
    {
        if ((a.value & 0x8000) != (b.value & 0x8000))
        {
            dst_f16.value = 0x7E00;
        }
    }

    if (std::isnan(float(dst_f16)))
    {
        dst_f16.value = dst_f16.value&0x7fff;
    }

    return dst_f16;
}
inline fp16 operator+(fp16 a, int b)
{
    return fp16(static_cast<float>(a) + static_cast<float>(b));
}
inline fp16 operator+(int a, fp16 b)
{
    return fp16(static_cast<float>(a) + static_cast<float>(b));
}

inline fp16 operator-(fp16 a, fp16 b)
{
    // return fp16(static_cast<float>(a) - static_cast<float>(b));
    b.value = b.value ^ 0x8000;
    return a+b;
    // return AddTwoFp16Simp(a, b);
}

inline fp16 operator*(fp16 a, fp16 b)
{
    Eigen::half src1_eig_f16;
    Eigen::half src2_eig_f16;
    Eigen::half dst_eig_f16;
    fp16 dst_f16;
    src1_eig_f16 = Eigen::half_impl::raw_uint16_to_half(a.value);
    src2_eig_f16 = Eigen::half_impl::raw_uint16_to_half(b.value);
    dst_eig_f16 = src1_eig_f16 * src2_eig_f16;
    dst_f16.value = Eigen::numext::bit_cast<uint16_t>(dst_eig_f16.x);

    if (((std::isinf(float(a)) && ((b.value & 0x7FFF) == 0))) || (((a.value & 0x7FFF) == 0) && std::isinf(float(b))))
    {
        dst_f16.value = 0x7E00;
    }
    
    if (std::isinf(float(a)) && std::isinf(float(b))) 
    {
        if ((a.value & 0x8000) != (b.value & 0x8000))
        {
            dst_f16.value = 0xFC00;
        }
    }

    if (std::isnan(float(dst_f16)))
    {
        dst_f16.value = dst_f16.value&0x7fff;
    }

    return dst_f16;
}

inline fp16 operator/(fp16 a, fp16 b)
{
    return fp16(static_cast<float>(a) / static_cast<float>(b));
}

inline fp16 operator-(fp16 a)
{
    a.value ^= 0x8000;
    return a;
}

inline bool operator<(fp16 a, fp16 b)
{
    return static_cast<float>(a) < static_cast<float>(b);
}

inline bool operator<=(fp16 a, fp16 b)
{
    return static_cast<float>(a) <= static_cast<float>(b);
}

inline bool operator==(fp16 a, fp16 b)
{
    return static_cast<float>(a) == static_cast<float>(b);
}

inline bool operator!=(fp16 a, fp16 b)
{
    return static_cast<float>(a) != static_cast<float>(b);
}

inline bool operator>(fp16 a, fp16 b)
{
    return static_cast<float>(a) > static_cast<float>(b);
}

inline bool operator>=(fp16 a, fp16 b)
{
    return static_cast<float>(a) >= static_cast<float>(b);
}

inline fp16 &operator+=(fp16 &a, fp16 b)
{
    a = a + b;
    return a;
}

inline fp16 &operator-=(fp16 &a, fp16 b)
{
    a = a - b;
    return a;
}

inline fp16 operator++(fp16 &a)
{
    a += fp16(1);
    return a;
}

inline fp16 operator--(fp16 &a)
{
    a -= fp16(1);
    return a;
}

inline fp16 operator++(fp16 &a, int)
{
    fp16 original_value = a;
    ++a;
    return original_value;
}

inline fp16 operator--(fp16 &a, int)
{
    fp16 original_value = a;
    --a;
    return original_value;
}

inline fp16 &operator*=(fp16 &a, fp16 b)
{
    a = a * b;
    return a;
}

inline fp16 &operator/=(fp16 &a, fp16 b)
{
    a = a / b;
    return a;
}

inline fp16 fp16_max(fp16 a, fp16 b)
{
    fp16 t;
    if (std::isnan(float(a)))
    {
        t = b;
        return t;
    }
    if (std::isnan(float(b)))
    {
        t = a;
        return t;
    }

    if (a > b) // align with asic
    {
        t = a;
    }
    else
    {
        t = b;
    }

    return t;
}

inline fp16 fp16_max(fp16 *a, int len)
{
    fp16 t;

    t = t.lowest();

    for (int i = 0; i < len; i++)
    {
        if (std::isnan(float(a[i])))
        {
            continue;
        }
        else
        {
            if (*(a + i) > t)
                t = *(a + i);
        }
    }

    return t;
}

inline fp16 fp16_max(std::vector<fp16> a, int len)
{
    fp16 t;

    t = t.lowest();

    for (int i = 0; i < len; i++)
    {
        if (std::isnan(float(a[i])))
        {
            continue;
        }
        else
        {
            if (a[i] > t)
                t = a[i];
        }
    }

    return t;
}

inline fp16 fp16_min(fp16 a, fp16 b)
{
    fp16 t;
    if (std::isnan(float(a)))
    {
        t = b;
        return t;
    }
    if (std::isnan(float(b)))
    {
        t = a;
        return t;
    }

    if (a > b) // align with asic
    {
        t = b;
    }
    else
    {
        t = a;
    }

    return t;
}

inline fp16 fp16_min(fp16 *a, int len)
{
    fp16 t;

    t = t.highest();

    for (int i = 0; i < len; i++)
    {
        if (std::isnan(float(a[i])))
        {
            continue;
        }
        else
        {
            if (*(a + i) < t)
                t = *(a + i);
        }
    }

    return t;
}

inline fp16 fp16_min(std::vector<fp16> a, int len)
{
    fp16 t;

    t = t.highest();

    for (int i = 0; i < len; i++)
    {
        if (std::isnan(float(a[i])))
        {
            continue;
        }
        else
        {
            if (a[i] < t)
                t = a[i];
        }
    }

    return t;
}

} // namespace FP16

namespace std
{
template <>
struct hash<FP16::fp16>
{
    size_t operator()(const FP16::fp16 &v) const
    {
        return hash<float>()(static_cast<float>(v));
    }
};

using FP16::fp16;
inline bool isinf(const fp16 &a) { return std::isinf(float(a)); }
inline bool isnan(const fp16 &a) { return std::isnan(float(a)); }
inline bool isfinite(const fp16 &a) { return std::isfinite(float(a)); }
inline fp16 abs(const fp16 &a) { return fp16(std::abs(float(a))); }
inline fp16 exp(const fp16 &a) { return fp16(std::exp(float(a))); }
inline fp16 log(const fp16 &a) { return fp16(std::log(float(a))); }
inline fp16 log10(const fp16 &a)
{
    return fp16(std::log10(float(a)));
}
inline fp16 sqrt(const fp16 &a)
{
    return fp16(std::sqrt(float(a)));
}
inline fp16 pow(const fp16 &a, const fp16 &b)
{
    return fp16(std::pow(float(a), float(b)));
}
inline fp16 sin(const fp16 &a) { return fp16(std::sin(float(a))); }
inline fp16 cos(const fp16 &a) { return fp16(std::cos(float(a))); }
inline fp16 tan(const fp16 &a) { return fp16(std::tan(float(a))); }
inline fp16 tanh(const fp16 &a)
{
    return fp16(std::tanh(float(a)));
}
inline fp16 floor(const fp16 &a)
{
    return fp16(std::floor(float(a)));
}
inline fp16 ceil(const fp16 &a)
{
    return fp16(std::ceil(float(a)));
}
inline fp16 round(const fp16 &a)
{
    // return fp16(std::round(float(a)));
    return fp16(std::nearbyint(float(a)));
}
} // namespace std

#endif //_FP16_H_