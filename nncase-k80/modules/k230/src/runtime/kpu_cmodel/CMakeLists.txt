cmake_minimum_required(VERSION 3.18)

find_package(Eigen3 REQUIRED)


if(SPU_CKP)
  add_definitions(-DSPU_CKP)# if need spu_ckp type: cmake -DSCLOG=on ../
  message(STATUS "spu checkpoint output")
endif(SPU_CKP)

if(PU_CKP)
  add_definitions(-DPU_CKP)# if need spu_ckp type: cmake -DPU_CKP ../
  message(STATUS "pu checkpoint output")
endif(PU_CKP)

if(LS_CKP)
  add_definitions(-DLS_CKP)# if need load store checkpoint: cmake -DLS_CKP=on ../
  message(STATUS "load store checkpoint output")
endif(LS_CKP)

if(MFU_CKP)
  add_definitions(-DMFU_CKP)# if need mfu checkpoint: cmake -DMFU_CKP=on ../
  message(STATUS "mfu checkpoint output")
endif(MFU_CKP)

if(MMU0_NO_CHECK)
  add_definitions(-DMMU0_NO_CHECK)# if need mmu0 no check: cmake -DMMU0_NO_CHECK=on ../
  message(STATUS "mmu0 no check used in random test")
endif(MMU0_NO_CHECK)

if(AI2D_CKP)
  add_definitions(-DAI2D_CKP)# if need AI2D_CKP type: cmake -DAI2D_CKP ../
  message(STATUS "ai2d checkpoint output")
endif(AI2D_CKP)

if(IDEAL_DDR)
  add_definitions(-DIDEAL_DDR)# if need debug.log: cmake -DIDEAL_DDR=on ../
  message(STATUS "ideal ddr")
endif(IDEAL_DDR)

add_library(k230_gnne_cmodel STATIC
        src/bfloat16.cc
        src/fp16.cc
        src/conv2d.cc
        src/environment.cc
        src/fp24.cc
        src/lib_arith.cc
        src/pdp0.cc
        src/tcu.cc
        src/TileHelper.cc
        src/isa.cc
        src/global.cc
        src/load.cc
        src/store.cc
        src/mfu.cc
        src/act0.cc
        src/dm.cc
        src/checkpoint.cc
        src/instCheck.cc
        src/ai2d.cc
        src/arithmatic.cc
        )

target_include_directories(k230_gnne_cmodel PUBLIC include)
set_target_properties(k230_gnne_cmodel PROPERTIES POSITION_INDEPENDENT_CODE ON)
target_link_libraries(k230_gnne_cmodel PUBLIC Eigen3::Eigen)
        
if(MSVC)
    target_compile_options(k230_gnne_cmodel PRIVATE /wd4018 /wd4101 /wd4244 /wd4805)
else()
  if (APPLE)
    target_compile_options(k230_gnne_cmodel PRIVATE -Wno-uninitialized -Wno-self-assign-field -Wno-unused-private-field)
  else()
    target_compile_options(k230_gnne_cmodel PRIVATE -Wno-maybe-uninitialized)
  endif()
endif(MSVC)

# old k230 cmodel cli
add_executable(k230_cmodel_cli src/gnne_cmodel.cc)
target_link_libraries(k230_cmodel_cli PRIVATE k230_gnne_cmodel)
install(TARGETS k230_cmodel_cli COMPONENT nncase-runtime)

# new k230 cmodel cli
add_executable(k230_cmodel_sc src/gnne_cmodel.simulator.cc ../shared_memory.cpp)
target_link_libraries(k230_cmodel_sc PUBLIC k230_gnne_cmodel)
target_link_libraries(k230_cmodel_sc PRIVATE nncasebase)
if(NOT APPLE AND NOT MSVC)
  target_link_libraries(k230_cmodel_sc PRIVATE rt)
endif()
set_target_properties(k230_cmodel_sc PROPERTIES POSITION_INDEPENDENT_CODE ON
  OUTPUT_NAME "nncase.simulator.k230.sc")
install(TARGETS k230_cmodel_sc COMPONENT nncase-runtime)


