/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include <nncase/runtime/runtime_module.h>
#include "compiler_defs.h"
#include "elfloader.h"

BEGIN_NS_NNCASE_RT_MODULE(xpu)

NNCASE_INLINE_VAR constexpr module_kind_t xpu_module_type =
    to_module_kind("xpu");
NNCASE_INLINE_VAR constexpr uint32_t xpu_module_version = 0;

NNCASE_MODULES_XPU_API result<std::unique_ptr<runtime_module>>
create_xpu_runtime_module();

END_NS_NNCASE_RT_MODULE

#include "duca_launcher.h"
BEGIN_NS_NNCASE_RT_MODULE(xpu)
class xpu_runtime_module : public runtime_module {
  public:
    virtual ~xpu_runtime_module();

    result<uintptr_t> native_handle(uint32_t flags) const noexcept override;

    uint64_t tdim() const noexcept { return tdim_; }
    uint64_t bdim() const noexcept { return bdim_; }
    uint64_t ddim() const noexcept { return ddim_; }
    uint64_t cdim() const noexcept { return cdim_; }

    std::span<const std::byte> rdata() noexcept { return rdata_; }
    std::span<std::byte> data() noexcept { return data_; }
    std::span<const std::byte> text() noexcept { return text_; }
    const std::span<const std::byte> thread_local_rdata() const noexcept {
        return thread_local_rdata_;
    }

    const uint64_t *thread_local_rdata_header(size_t offset) const noexcept {
        return reinterpret_cast<const uint64_t *>(thread_local_rdata_.data()) +
               offset * 2;
    }

    const std::span<const std::byte> thread_local_rdata_content() const noexcept {
        return thread_local_rdata_.subspan(cdim_ * ddim_ * bdim_ * tdim_ * 2 *
                                    sizeof(uint64_t));
    }

    const std::span<const std::byte> block_local_rdata() const noexcept {
        return block_local_rdata_;
    }

    const uint64_t *block_local_rdata_header(size_t offset) const noexcept {
        return reinterpret_cast<const uint64_t *>(block_local_rdata_.data()) +
               offset * 2;
    }
    
    const std::span<const std::byte> block_local_rdata_content() const noexcept {
        return block_local_rdata_.subspan(cdim_ * ddim_ * bdim_ * 2 *
                                          sizeof(uint64_t));
    }

    size_t func_cnt() noexcept { return func_cnt_; }
    void func_cnt(size_t incr) noexcept { func_cnt_ += incr; }
    size_t func_num() noexcept { return functions().size(); }
    bool has_stream() noexcept { return has_stream_; }
#ifdef SYS_MODE
    duca_launcher &launcher() noexcept { return launcher_; }
    std::vector<DUdeviceptr> &kv_cache_storage_ptrs() noexcept {
        return kv_cache_storage_ptrs_;
    }
#else
    elfloader &launcher() noexcept { return launcher_; }
#endif


  protected:
    result<void> initialize_before_functions(
        runtime_module_init_context &context) noexcept override;
    result<std::unique_ptr<runtime_function>>
    create_function() noexcept override;

private:
    uint64_t tdim_;
    uint64_t bdim_;
    uint64_t ddim_;
    uint64_t cdim_;
    std::span<std::byte> data_;
    std::span<const std::byte> text_;
    std::span<const std::byte> rdata_;
    std::span<const std::byte> thread_local_rdata_;
    std::span<const std::byte> block_local_rdata_;
    host_buffer_t text_storage_;
    host_buffer_t rdata_storage_;
    host_buffer_t data_storage_;
    host_buffer_t thread_local_rdata_storage_;
    host_buffer_t block_local_rdata_storage_;
    size_t func_cnt_ = 0;
    bool has_stream_ = false;
#ifdef SYS_MODE
    duca_launcher launcher_;
    std::vector<DUdeviceptr> kv_cache_storage_ptrs_;
#else
    elfloader launcher_;
#endif      
};

END_NS_NNCASE_RT_MODULE
