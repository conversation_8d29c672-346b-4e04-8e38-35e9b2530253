/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "runtime_module.h"
#include <nncase/ntt/runtime.h>
#include <nncase/runtime/host_buffer.h>
#include <nncase/runtime/device_buffer.h>
#include <nncase/runtime/runtime_function.h>
#include <nncase/tensor.h>
#include <string>
#include <vector>

using namespace nncase::runtime;

BEGIN_NS_NNCASE_RT_MODULE(xpu)

class xpu_runtime_function : public runtime_function
{
public:
    using runtime_function::runtime_function;

    xpu_runtime_module &module() const noexcept;

protected:
    result<void>
    initialize_core(runtime_function_init_context &context) noexcept override;
    result<value_t> invoke_core(std::span<value_t> parameters,
        value_t return_value) noexcept override;

private:
    result<tensor> create_output_tensor(size_t output_id,
                                    std::span<value_t> parameters,
                                    std::byte *output_data) noexcept;

private:
    std::span<const std::byte> text_;
    std::span<const std::byte> rdata_;
    size_t input_size_;
    size_t output_size_;
    size_t thread_local_data_usage_;
    size_t block_local_data_usage_;
    char name_[64];
    std::string mangled_name();
    nncase::runtime::buffer_t output_buffer_;
    std::vector<ntt::runtime::thread_inout_desc> input_descs_;
    std::vector<ntt::runtime::thread_inout_desc> output_descs_;
    std::vector<dims_t> input_shapes_;
    std::vector<dims_t> input_strides_;
    std::vector<dims_t> output_shapes_;
    std::vector<dims_t> output_strides_;
    void *descs_ptr_;
};

END_NS_NNCASE_RT_MODULE
