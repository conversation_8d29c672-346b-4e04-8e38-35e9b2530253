#pragma once
#include "compiler_defs.h"
#include "elfload.h"
#include "hardware_def.h"
#include "method_table_impl.h"
#include <inttypes.h>
#include <math.h>
#include <nncase/ntt/runtime.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#if defined(__linux__)
#include <sys/mman.h>
#endif

BEGIN_NS_NNCASE_RT_MODULE(xpu)

using namespace nncase::ntt::runtime;
typedef void (*entrypoint_t)(char *name, hardware_context_mt *hw_ctx_impl,
    runtime_util_mt *rt_util_mt,
    nncase_mt_t *nncase_mt_impl, std::vector<thread_inout_desc> &input_descs,
    std::vector<thread_inout_desc> &output_descs,
    uint8_t *rdata, const uint64_t *thread_local_rdata_header, uint8_t *thread_local_rdata_content, const uint64_t *block_local_rdata_header, uint8_t *block_local_rdata_content, uint8_t *output_data);

class elfloader
{
public:
    elfloader(char *elf, size_t size)
        : elf_path_()
    {
        ctx_.pread = bpread;
        ctx_.elf = elf;
        ctx_.size = size;
    }

    elfloader(const std::string &elf_path)
        : ctx_(), elf_path_(elf_path)
    {
    }

    elfloader()
        : ctx_(), elf_path_()
    {
    }

    static bool bpread(el_ctx *ctx, void *dest, size_t nb, size_t offset)
    {
        (void)ctx;

        memcpy(dest, (char *)ctx->elf + offset, nb);

        return true;
    }

    static void *alloccb(el_ctx *ctx, Elf_Addr phys, Elf_Addr virt,
        Elf_Addr size)
    {
        (void)ctx;
        (void)phys;
        (void)size;
        return (void *)virt;
    }

    static void check(el_status stat, const char *expln)
    {
        if (stat)
        {
            fprintf(stderr, "%s: error %d\n", expln, stat);
            exit(1);
        }
    }

    int invoke_elf(char *name, hardware_context_mt *hw_ctx_impl,
        runtime_util_mt *rt_util_mt, nncase_mt_t *nncase_mt_impl,
        std::vector<thread_inout_desc> &input_descs, std::vector<thread_inout_desc> &output_descs,
        uint8_t *rdata, const uint64_t *thread_local_rdata_header, uint8_t *thread_local_rdata_content, const uint64_t *block_local_rdata_header, uint8_t *block_local_rdata_content, uint8_t *output_data);

private:
    void *ptr_;
    void *buf_;
    el_ctx ctx_;
    std::string elf_path_;
};

END_NS_NNCASE_RT_MODULE