/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifdef SYS_MODE
#include <cstddef>
#include <nncase/ntt/runtime.h>
extern "C"
{
#include <duca.h>
#include <duca_runtime_api.h>
}
#define MAX_DEV_MEM_PACKET (64 * 1024 * 1024)

using namespace nncase;
using namespace nncase::ntt::runtime;
using namespace nncase::runtime;
using namespace nncase::runtime::xpu;

// namespace
// {
class duca_launcher
{
public:
    duca_launcher() { }

    DUmodule module() const noexcept { return module_; }

    duca_launcher(char *text, size_t text_size, char *rdata, size_t rdata_size, const uint64_t *thread_local_rdata_header, const char *thread_local_rdata, const uint64_t *block_local_rdata_header, const char *block_local_rdata)
    {
        text_ = text;
        text_size_ = text_size;
        rdata_ = rdata;
        rdata_size_ = rdata_size;
        thread_local_rdata_header_ = (uint64_t *)thread_local_rdata_header;
        block_local_rdata_header_ = (uint64_t *)block_local_rdata_header;
        thread_local_rdata_ = (char *)thread_local_rdata;
        block_local_rdata_ = (char *)block_local_rdata;
    }

    void init_duca(DUstream outerside_stream, uint64_t cdim, uint64_t ddim, uint64_t bdim, uint64_t tdim)
    {
        tdim_ = tdim;
        bdim_ = bdim;
        ddim_ = ddim;
        cdim_ = cdim;

        DUmodulefile loadfile;

        // create context
        duca_environment_prepare(outerside_stream);

        if (rdata_size_ > 0)
        {
            CHECK(duMemAllocUma(&rdata_ptr_, rdata_size_, DUCA_DEVICE_DIE_ID_ANY));
            for (size_t j = 0; j < rdata_size_; j += MAX_DEV_MEM_PACKET)
            {
                CHECK(duMemcpyHtoD(((char *)rdata_ptr_) + j, ((char *)rdata_) + j, j + MAX_DEV_MEM_PACKET > rdata_size_ ? rdata_size_ % MAX_DEV_MEM_PACKET : MAX_DEV_MEM_PACKET, sm_));
            }
        }

        // 1st thread has max size.
        if (thread_local_rdata_header_[1] > 0)
        {
            CHECK(duMemAllocNumaUniform(&uniform_thread_local_rdata_ptr_, thread_local_rdata_header_[1] * tdim_, sm_));

            for(auto b = 0UL; b < ddim_ * bdim_; b++)
            {
                for (auto t = 0UL; t < tdim_; t++)
                {
                    auto offset = thread_local_rdata_header_[(b * tdim_ + t) * 2];
                    auto len = thread_local_rdata_header_[(b * tdim_ + t) * 2 + 1];
                    auto ret = duMemcpyNumaUniformHtoD(((char *)uniform_thread_local_rdata_ptr_) + thread_local_rdata_header_[1] * t, thread_local_rdata_ + offset, len, sm_, b);
                    if (ret != DUCA_SUCCESS)
                    {
                        printf("duMemcpyNumaUniformHtoD failed: %d\n", ret);
                    }
                }
            }
        }

        // 1st block has max size.
        if (block_local_rdata_header_[1] > 0)
        {
            CHECK(duMemAllocNumaUniform(&uniform_block_local_rdata_ptr_, block_local_rdata_header_[1], sm_));

            for(auto b = 0UL; b < ddim_ * bdim_; b++)
            {
                auto offset = block_local_rdata_header_[b * 2];
                auto len = block_local_rdata_header_[b * 2 + 1];
                auto ret = duMemcpyNumaUniformHtoD((char *)uniform_block_local_rdata_ptr_, block_local_rdata_ + offset, len, sm_, b);
                if (ret != DUCA_SUCCESS)
                {
                    printf("duMemcpyNumaUniformHtoD failed: %d\n", ret);
                }
            }
        }
        
        // load module
        loadfile.bin = text_;
        loadfile.size = text_size_;
        strcpy(loadfile.name, "nouse");
        CHECK(duModuleLoad(&module_, sm_, &loadfile));
    }

    void alloc_numa_local_data(size_t thread_local_data_pool_size, size_t block_local_data_pool_size)
    {
        if (thread_local_data_pool_size > 0) {
          CHECK(duMemAllocNumaUniform(&uniform_thread_local_data_ptr_, thread_local_data_pool_size * tdim_, sm_));
        }
        if (block_local_data_pool_size > 0) {
          CHECK(duMemAllocNumaUniform(&uniform_block_local_data_ptr_, block_local_data_pool_size, sm_));
        }
    }

    void alloc_uma_desc(std::vector<thread_inout_desc> &input_descs, std::vector<thread_inout_desc> &output_descs)
    {
        in_size_ = input_descs.size();
        out_size_ = output_descs.size();
        size_ = in_size_ + out_size_;
        input_desc_valid_ = new bool[in_size_];
        output_desc_valid_ = new bool[out_size_];
        dev_data_ptr_ = new DUdeviceptr[size_];
        dev_shape_ptr_ = new DUdeviceptr[size_];
        dev_strides_ptr_ = new DUdeviceptr[size_];
        CHECK(duMemAllocUma(&input_desc_ptr_, in_size_ * sizeof(thread_inout_desc), DUCA_DEVICE_DIE_ID_ANY));
        CHECK(duMemAllocUma(&output_desc_ptr_, out_size_ * sizeof(thread_inout_desc), DUCA_DEVICE_DIE_ID_ANY));

        for (size_t i = 0; i < in_size_; i++)
        {
            input_desc_valid_[i] = false;
            if (input_descs[i].rank > 0)
            {
                input_desc_valid_[i] = true;
                CHECK(duMemAllocUma(&dev_shape_ptr_[i], input_descs[i].rank * sizeof(size_t), DUCA_DEVICE_DIE_ID_ANY));
                CHECK(duMemAllocUma(&dev_strides_ptr_[i], input_descs[i].rank * sizeof(size_t), DUCA_DEVICE_DIE_ID_ANY));
            }
        }

        for (size_t o = 0; o < out_size_; o++)
        {
            output_desc_valid_[o] = false;
            if (output_descs[o].rank > 0)
            {
                output_desc_valid_[o] = true;
                CHECK(duMemAllocUma(&dev_shape_ptr_[in_size_ + o], output_descs[o].rank * sizeof(size_t), DUCA_DEVICE_DIE_ID_ANY));
                CHECK(duMemAllocUma(&dev_strides_ptr_[in_size_ + o], output_descs[o].rank * sizeof(size_t), DUCA_DEVICE_DIE_ID_ANY));
            }
        }
    }

    void get_function(char *name)
    {
        // get kernel symbol
        CHECK(duModuleGetFunction(&kernelFunc_, module_, name));
    }

    void invoke_kernel_func(std::vector<thread_inout_desc> &input_descs, std::vector<thread_inout_desc> &output_descs, uint8_t *output_data, NNCASE_UNUSED size_t output_data_size)
    {
        auto in_size = in_size_;
        auto out_size = out_size_;
        DUdeviceptr dev_output_data_ptr = NULL;
        DUdeviceptr args[8];
        std::vector<thread_inout_desc> dev_input_descs;
        std::vector<thread_inout_desc> dev_output_descs;

        // allocate desc device memory
        for (size_t i = 0; i < in_size; i++)
        {
            dev_data_ptr_[i] = NULL;
        }

        for (size_t o = 0; o < out_size; o++)
        {
            dev_data_ptr_[in_size + o] = NULL;
        }

        dev_output_data_ptr = (DUdeviceptr)output_data;

        for (size_t i = 0; i < in_size; i++)
        {
            if (input_descs[i].size > 0)
            {
                dev_data_ptr_[i] = (DUdeviceptr)input_descs[i].data;
            }

            if (input_descs[i].rank > 0)
            {
                CHECK(duMemcpyHtoD(((char *)dev_shape_ptr_[i]), ((char *)input_descs[i].shape), input_descs[i].rank * sizeof(size_t), sm_));
                CHECK(duMemcpyHtoD(((char *)dev_strides_ptr_[i]), ((char *)input_descs[i].strides), input_descs[i].rank * sizeof(size_t), sm_));
            }
        }

        for (size_t i = 0; i < in_size; i++)
        {
            dev_input_descs.emplace_back(thread_inout_desc {
                .data = (std::byte *)dev_data_ptr_[i],
                .size = input_descs[i].size,
                .shape = (size_t *)dev_shape_ptr_[i],
                .strides = (size_t *)dev_strides_ptr_[i],
                .rank = input_descs[i].rank,
            });
        }

        CHECK(duMemcpyHtoD(input_desc_ptr_, dev_input_descs.data(), in_size * sizeof(thread_inout_desc), sm_));

        for (size_t o = 0; o < out_size; o++)
        {
            dev_output_descs.emplace_back(thread_inout_desc {
                .data = output_descs[o].data,
                .size = output_descs[o].size,
                .shape = (size_t *)dev_shape_ptr_[in_size + o],
                .strides = (size_t *)dev_strides_ptr_[in_size + o],
                .rank = output_descs[o].rank,
            });
        }

        CHECK(duMemcpyHtoD(output_desc_ptr_, dev_output_descs.data(), out_size * sizeof(thread_inout_desc), sm_));

        // args follow kernel def
        args[0] = &input_desc_ptr_;
        args[1] = &output_desc_ptr_;
        args[2] = &rdata_ptr_;
        args[3] = &uniform_thread_local_rdata_ptr_;
        args[4] = &uniform_block_local_rdata_ptr_;
        args[5] = &dev_output_data_ptr;
        args[6] = &uniform_thread_local_data_ptr_;
        args[7] = &uniform_block_local_data_ptr_;

        // load kernel
        CHECK(duLaunchKernel(kernelFunc_,
            sm_,
            (void **)args,
            NULL));
        
        // start to process output
        // output_data = (unsigned char *)dev_output_data_ptr;
        CHECK(duMemcpyDtoH(dev_output_descs.data(), output_desc_ptr_, out_size * sizeof(thread_inout_desc), sm_));

        for (size_t o = 0; o < out_size; o++)
        {
            if (output_descs[o].rank > 0)
            {
                CHECK(duMemcpyDtoH(((char *)output_descs[o].shape), ((char *)dev_shape_ptr_[in_size + o]), output_descs[o].rank * sizeof(size_t), sm_));
                CHECK(duMemcpyDtoH(((char *)output_descs[o].strides), ((char *)dev_strides_ptr_[in_size + o]), output_descs[o].rank * sizeof(size_t), sm_));
            }

            output_descs[o].data = dev_output_descs[o].data;
            output_descs[o].size = dev_output_descs[o].size;
        }
    }

    void release_duca()
    {
        if (rdata_size_ > 0)
            CHECK(duMemFree(rdata_ptr_));
        else if (sm_ != NULL)
            rdata_ptr_ = NULL;

        if(input_desc_ptr_)
            CHECK(duMemFree(input_desc_ptr_));
        if(output_desc_ptr_)
            CHECK(duMemFree(output_desc_ptr_));

        
        // free memory
        for (size_t i = 0; i < in_size_; i++)
        {
            dev_data_ptr_[i] = NULL;
            if (input_desc_valid_[i])
            {
                CHECK(duMemFree(dev_shape_ptr_[i]));
                CHECK(duMemFree(dev_strides_ptr_[i]));
            }
        }

        for (size_t o = 0; o < out_size_; o++)
        {
            if (output_desc_valid_[o])
            {
                CHECK(duMemFree(dev_shape_ptr_[in_size_ + o]));
                CHECK(duMemFree(dev_strides_ptr_[in_size_ + o]));
            }
        }
        delete[] input_desc_valid_;
        delete[] output_desc_valid_;
        delete[] dev_data_ptr_;
        delete[] dev_shape_ptr_;
        delete[] dev_strides_ptr_;

        if (uniform_thread_local_rdata_ptr_)
            duMemFreeNumaUniform(uniform_thread_local_rdata_ptr_, sm_);
        if (uniform_block_local_rdata_ptr_)
            duMemFreeNumaUniform(uniform_block_local_rdata_ptr_, sm_);

        // destroy context
        duca_environment_destroy();

        if (uniform_thread_local_data_ptr_)
            duMemFreeNumaUniform(uniform_thread_local_data_ptr_, sm_);
        if (uniform_block_local_data_ptr_)
            duMemFreeNumaUniform(uniform_block_local_data_ptr_, sm_);
    }

    // Allocate UMA memory and copy data from host
    DUdeviceptr alloc_and_copy_to_device(const void *host_ptr, size_t size)
    {
        if (size == 0)
            return NULL;

        DUdeviceptr dev_ptr = NULL;
        CHECK(duMemAllocUma(&dev_ptr, size, DUCA_DEVICE_DIE_ID_ANY));

        // Copy data in chunks to handle large buffers
        for (size_t offset = 0; offset < size; offset += MAX_DEV_MEM_PACKET)
        {
            size_t chunk_size = std::min((size_t)MAX_DEV_MEM_PACKET, size - offset);
            CHECK(duMemcpyHtoD(
                ((char *)dev_ptr) + offset,
                ((char *)host_ptr) + offset,
                chunk_size,
                sm_));
        }

        return dev_ptr;
    }

    void copy_to_device(const void *host_ptr, DUdeviceptr dev_ptr, size_t size)
    {
        // Copy data in chunks to handle large buffers
        for (size_t offset = 0; offset < size; offset += MAX_DEV_MEM_PACKET)
        {
            size_t chunk_size = std::min((size_t)MAX_DEV_MEM_PACKET, size - offset);
            CHECK(duMemcpyHtoD(
                ((char *)dev_ptr) + offset,
                ((char *)host_ptr) + offset,
                chunk_size,
                sm_));
        }
    }

    DUdeviceptr alloc_device(size_t size)
    {
        if (size == 0)
            return NULL;

        DUdeviceptr dev_ptr = NULL;
        CHECK(duMemAllocUma(&dev_ptr, size, DUCA_DEVICE_DIE_ID_ANY));

        return dev_ptr;
    }

    void print_meminfo()
    {
        ducaMemInfo info;
        CHECK(ducaMemGetInfo(&info) == ducaSuccess);
        for (int i = 0; i < DUCA_CLUSTER_COUNT; i++)
        {
            printf("cluster %d: totalUmaMem=%ld, freeUmaMem=%ld\n", i, info.totalUmaMem[i], info.freeUmaMem[i]);
            // for (int j = 0; j < DUCA_CLUSTER_BLOCK_COUNT; j++)
            for (int j = 0; j < 2; j++)
            {
                if (info.totalNumaMem[i][j] != 0)
                    printf("    block %d: totalNumaMem=%ld, freeNumaMem=%ld\n", j, info.totalNumaMem[i][j], info.freeNumaMem[i][j]);
            }
        }
    }

    // Copy data from device to host
    void copy_to_host(void *host_ptr, DUdeviceptr dev_ptr, size_t size)
    {
        if (size == 0)
            return;

        // Copy data in chunks
        for (size_t offset = 0; offset < size; offset += MAX_DEV_MEM_PACKET)
        {
            size_t chunk_size = std::min((size_t)MAX_DEV_MEM_PACKET, size - offset);
            CHECK(duMemcpyDtoH(
                ((char *)host_ptr) + offset,
                ((char *)dev_ptr) + offset,
                chunk_size,
                sm_));
        }
    }

    // Free device memory
    void free_device_memory(DUdeviceptr dev_ptr)
    {
        if (dev_ptr)
        {
            CHECK(duMemFree(dev_ptr));
        }
        dev_ptr = NULL;
    }

private:
    char *text_;
    size_t text_size_;
    char *rdata_;
    uint64_t *thread_local_rdata_header_;
    uint64_t *block_local_rdata_header_;
    char *thread_local_rdata_;
    char *block_local_rdata_;
    size_t rdata_size_;
    DUdeviceptr rdata_ptr_;
    DUfunction kernelFunc_;
    size_t in_size_;
    size_t out_size_;
    size_t size_;
    bool *input_desc_valid_;
    bool *output_desc_valid_;
    DUdeviceptr input_desc_ptr_;
    DUdeviceptr output_desc_ptr_;
    DUdeviceptr *dev_data_ptr_;
    DUdeviceptr *dev_shape_ptr_;
    DUdeviceptr *dev_strides_ptr_;
    DUuniformptr uniform_thread_local_data_ptr_;
    DUuniformptr uniform_block_local_data_ptr_;
    DUuniformptr uniform_thread_local_rdata_ptr_;
    DUuniformptr uniform_block_local_rdata_ptr_;
    DUmodule module_;
    DUcontext ctx_;
    DUstream sm_;
    DUcompute compute_;
    bool outerside_stream_;
    uint64_t tdim_;
    uint64_t bdim_;
    uint64_t ddim_;
    uint64_t cdim_;

    long duca_environment_prepare(DUstream outerside_stream)
    {
        if (outerside_stream)
        {
            ctx_ = NULL;
            sm_ = outerside_stream;
            outerside_stream_ = true;
            // FIXME: SDK not ready
            // CHECK(duStreamGetCompute(sm_, compute_));
        }
        else
        {
            int devCnt;
            (void)duDeInit();

            CHECK(duInit(0));

            CHECK(duDeviceGetCount(&devCnt));

            CHECK(duCtxCreate(&ctx_, 0, 0));

            compute_.dieAuto = 0;
            compute_.dieMask = 0x3;
            compute_.xpuAuto = 0;
            compute_.xpuMask = 0xFFFFFFFF;
            CHECK(duStreamCreate(&sm_, &compute_, 0));
            outerside_stream_ = false;
        }

        return 0;
    }

    void duca_environment_destroy(void)
    {
        CHECK(duModuleUnload(module_, sm_));

        if (ctx_ != NULL && sm_ != NULL)
        {
            // using outsize stream.
            CHECK(duStreamDestroy(sm_));
            CHECK(duCtxDestroy(ctx_));
        }
    }
};
// }
#endif