#pragma once
#include <nncase/half.h>
#include "compiler_defs.h"
#include "hardware_context.h"
#include "method_table_def.h"
#include "runtime_utils.h"
#include <chrono>
#include <cstring>
#include <functional>
#include <math.h>
#include <stdarg.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

BEGIN_NS_NNCASE_RT_MODULE(xpu)

inline float float_unary_logical_not(float x) { return !x; }
inline float float_unary_neg(float x) { return std::negate<float>()(x); }
inline float float_unary_rsqrt(float x) { return 1.f / sqrtf(x); }
inline float float_unary_sign(float x) { return (0.f < x) - (x < 0.f); }
inline float float_unary_square(float x) { return x * x; }

inline float float_binary_add(float x, float y) { return x + y; }
inline float float_binary_sub(float x, float y) { return x - y; }
inline float float_binary_mul(float x, float y) { return x * y; }
inline float float_binary_div(float x, float y) { return x / y; }
inline float float_binary_min(float x, float y) { return std::min(x, y); }
inline float float_binary_max(float x, float y) { return std::max(x, y); }
inline float float_binary_pow(float x, float y) { return powf(x, y); }
inline float float_binary_logical_and(float x, float y) { return x && y; }
inline float float_binary_mod(float x, float y) { return std::fmod(x, y); }

inline int32_t int32_binary_add(int32_t x, int32_t y) { return x + y; }
inline int32_t int32_binary_sub(int32_t x, int32_t y) { return x - y; }
inline int32_t int32_binary_mul(int32_t x, int32_t y) { return x * y; }
inline int32_t int32_binary_div(int32_t x, int32_t y) { return x / y; }
inline int32_t int32_binary_min(int32_t x, int32_t y) { return std::min(x, y); }
inline int32_t int32_binary_max(int32_t x, int32_t y) { return std::max(x, y); }
#if defined(__APPLE__)
inline int32_t int32_binary_pow(int32_t x, int32_t y)
{
    return (int32_t)pow(x, y);
}
#else
inline int32_t int32_binary_pow(int32_t x, int32_t y)
{
    return std::pow(x, y);
}
#endif
inline int32_t int32_binary_logical_and(int32_t x, int32_t y)
{
    return x && y;
}
inline int32_t int32_binary_mod(int32_t x, int32_t y) { return x % y; }

inline int64_t int64_binary_add(int64_t x, int64_t y) { return x + y; }
inline int64_t int64_binary_sub(int64_t x, int64_t y) { return x - y; }
inline int64_t int64_binary_mul(int64_t x, int64_t y) { return x * y; }
inline int64_t int64_binary_div(int64_t x, int64_t y) { return x / y; }
inline int64_t int64_binary_min(int64_t x, int64_t y) { return std::min(x, y); }
inline int64_t int64_binary_max(int64_t x, int64_t y) { return std::max(x, y); }
#if defined(__APPLE__)
inline int64_t int64_binary_pow(int64_t x, int64_t y)
{
    return (int64_t)pow(x, y);
}
#else
inline int64_t int64_binary_pow(int64_t x, int64_t y)
{
    return std::pow(x, y);
}
#endif
inline int64_t int64_binary_logical_and(int64_t x, int64_t y)
{
    return x && y;
}
inline int64_t int64_binary_mod(int64_t x, int64_t y) { return x % y; }

inline bool bool_binary_logical_and(bool x, bool y) { return x && y; }
inline bool bool_binary_logical_or(bool x, bool y) { return x || y; }
inline bool bool_binary_logical_xor(bool x, bool y) { return x ^ y; }

[[maybe_unused]] static nncase_mt_t nncase_mt_impl = {
    fabsf,
    acosf,
    acoshf,
    asinf,
    asinhf,
    ceilf,
    cosf,
    coshf,
    expf,
    floorf,
    logf,
    float_unary_logical_not,
    float_unary_neg,
    roundf,
    float_unary_rsqrt,
    float_unary_sign,
    sinf,
    sinhf,
    sqrtf,
    float_unary_square,
    tanhf,
    erff,
    float_binary_add,
    float_binary_sub,
    float_binary_mul,
    float_binary_div,
    float_binary_min,
    float_binary_max,
    float_binary_pow,
    float_binary_logical_and,
    float_binary_mod,
    int32_binary_add,
    int32_binary_sub,
    int32_binary_mul,
    int32_binary_div,
    int32_binary_min,
    int32_binary_max,
    int32_binary_pow,
    int32_binary_logical_and,
    int32_binary_mod,
    int64_binary_add,
    int64_binary_sub,
    int64_binary_mul,
    int64_binary_div,
    int64_binary_min,
    int64_binary_max,
    int64_binary_pow,
    int64_binary_logical_and,
    int64_binary_mod,
    bool_binary_logical_and,
    bool_binary_logical_or,
    bool_binary_logical_xor,
    [](half x) -> half { return (half)fabsf(x); },
    [](half x) -> half { return (half)acosf(x); },
    [](half x) -> half { return (half)acoshf(x); },
    [](half x) -> half { return (half)asinf(x); },
    [](half x) -> half { return (half)asinhf(x); },
    [](half x) -> half { return (half)ceilf(x); },
    [](half x) -> half { return (half)cosf(x); },
    [](half x) -> half { return (half)coshf(x); },
    [](half x) -> half { return (half)expf(x); },
    [](half x) -> half { return (half)floorf(x); },
    [](half x) -> half { return (half)logf(x); },
    [](half x) -> half { return (half)float_unary_logical_not(x); },
    [](half x) -> half { return (half)float_unary_neg(x); },
    [](half x) -> half { return (half)roundf(x); },
    [](half x) -> half { return (half)float_unary_rsqrt(x); },
    [](half x) -> half { return (half)float_unary_sign(x); },
    [](half x) -> half { return (half)sinf(x); },
    [](half x) -> half { return (half)sinhf(x); },
    [](half x) -> half { return (half)sqrtf(x); },
    [](half x) -> half { return (half)float_unary_square(x); },
    [](half x) -> half { return (half)tanhf(x); },
    [](half x) -> half { return (half)erff(x); },
    [](half x, half y) -> half { return (half)float_binary_add(x, y); },
    [](half x, half y) -> half { return (half)float_binary_sub(x, y); },
    [](half x, half y) -> half { return (half)float_binary_mul(x, y); },
    [](half x, half y) -> half { return (half)float_binary_div(x, y); },
    [](half x, half y) -> half { return (half)float_binary_min(x, y); },
    [](half x, half y) -> half { return (half)float_binary_max(x, y); },
    [](half x, half y) -> half { return (half)float_binary_pow(x, y); },
    [](half x, half y) -> half { return (half)float_binary_logical_and(x, y); },
    [](half x, half y) -> half { return (half)float_binary_mod(x, y); },
};

inline void *rt_malloc(size_t bytes, size_t alignment)
{
    #ifdef WIN32
    return _aligned_malloc(bytes, alignment);
#else
    size_t mask = alignment - 1;
    size_t aligned_bytes = bytes + (-bytes & mask);
    auto ptr = aligned_alloc(alignment, aligned_bytes);
    if (!ptr) {
        std::terminate();
    }
    return ptr;
#endif
}

#ifndef SYS_MODE
inline void bind_thread_to_cpu(int cpu_id, pthread_t pt)
{
    cpu_set_t cpu_mask;
    CPU_ZERO(&cpu_mask);

    CPU_SET(cpu_id, &cpu_mask);
    sched_setaffinity(pt, sizeof(cpu_mask), &cpu_mask);
}
#endif

inline double time_now()
{
    auto now = std::chrono::steady_clock::now();
    return now.time_since_epoch() / std::chrono::milliseconds(1);
}

inline int printf_with_flush(const char *__restrict __format, ...)
{
    // pthread_mutex_t mutex;
    // pthread_mutex_lock(&mutex);
    va_list args;
    va_start(args, __format);
    vprintf(__format, args);
    va_end(args);
    fflush(stdout);
    // pthread_mutex_unlock(&mutex);
    return 0;
}

[[maybe_unused]] static runtime_util_mt rt_util_mt_impl = {
    printf, rt_malloc, free, rt_assert, memcpy, memset, time_now,
#ifndef SYS_MODE
    bind_thread_to_cpu,
    create_thread,
    join_thread
#endif
};

END_NS_NNCASE_RT_MODULE