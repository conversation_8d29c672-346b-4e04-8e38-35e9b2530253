#include <cstdint>
#include <cstring>
#include <dlfcn.h>
#include <fstream>
#include <nncase/runtime/xpu/elfloader.h>

using namespace nncase;
using namespace nncase::runtime;
using namespace nncase::runtime::xpu;

void _start(char *name, hardware_context_mt *hw_ctx_impl,
    runtime_util_mt *rt_util_mt,
    nncase_mt_t *nncase_mt_impl, std::vector<thread_inout_desc> &input_descs,
    std::vector<thread_inout_desc> &output_descs,
    uint8_t *rdata, const uint64_t *thread_local_rdata_header, uint8_t *thread_local_rdata_content,
    const uint64_t *block_local_rdata_header, uint8_t *block_local_rdata_content, uint8_t *output_data);

int elfloader::invoke_elf(char *name,
    hardware_context_mt *hw_ctx_impl,
    runtime_util_mt *rt_util_mt,
    nncase_mt_t *nncase_mt_impl,
    std::vector<thread_inout_desc> &input_descs,
    std::vector<thread_inout_desc> &output_descs,
    uint8_t *rdata, const uint64_t *thread_local_rdata_header,
    uint8_t *thread_local_rdata_content, const uint64_t *block_local_rdata_header,
    uint8_t *block_local_rdata_content, uint8_t *output_data)
{
#if 1
    std::string kernel_so;
    if (elf_path_.empty())
    {
        char kernel_so_template[] = "/tmp/kernelXXXXXX.so";
        if (mkstemps(kernel_so_template, 3) == -1)
        {
            std::cerr << "cannot create temp kernel.so file " << std::endl;
            return 1;
        }

        kernel_so = kernel_so_template;
        std::ofstream outputFile(kernel_so, std::ios::out | std::ios::binary);
        if (!outputFile)
        {
            std::cerr << "cannot create file:" << kernel_so << std::endl;
            return 1;
        }

        outputFile.write((char *)ctx_.elf, ctx_.size);

        if (!outputFile.good())
        {
            std::cerr << "error writing file" << std::endl;
            outputFile.close();
            return 1;
        }
    }
    else
    {
      kernel_so = elf_path_;
    }

    void *handle = dlopen(kernel_so.c_str(), RTLD_LAZY);
    if (!handle)
    {
        fprintf(stderr, "Error: %s\n", dlerror());
        exit(EXIT_FAILURE);
    }

    entrypoint_t _start = (entrypoint_t)dlsym(handle, "_Z6_startPcPN6nncase7runtime3xpu19hardware_context_mtEPNS2_15runtime_util_mtEPNS2_19nncase_method_tableERSt6vectorINS0_3ntt7runtime17thread_inout_descESaISC_EESF_PhPmSG_SH_SG_SG_");
    const char *dlsym_error = dlerror();
    if (dlsym_error)
    {
        dlclose(handle);
        return 1;
    }

    // invoke _start
    _start(name, hw_ctx_impl, rt_util_mt, nncase_mt_impl, input_descs, output_descs, rdata, thread_local_rdata_header, thread_local_rdata_content, block_local_rdata_header, block_local_rdata_content, output_data);

    // clear
    dlclose(handle);
#else
    check(el_init(&ctx_), "initialising");

    // align to ctx.align
    ptr_ = malloc(ctx_.memsz + ctx_.align);
    buf_ = (void *)(((size_t)ptr_ + (ctx_.align - 1)) & ~(ctx_.align - 1));

#if defined(__linux__)
    if (mprotect(buf_, ctx_.memsz, PROT_READ | PROT_WRITE | PROT_EXEC))
    {
        perror("mprotect");
        return 1;
    }
#endif

    ctx_.base_load_vaddr = ctx_.base_load_paddr = (uintptr_t)buf_;

    check(el_load(&ctx_, alloccb), "loading");
    check(el_relocate(&ctx_), "relocating");

    uintptr_t epaddr = ctx_.ehdr.e_entry + (uintptr_t)buf_;

    entrypoint_t ep = (entrypoint_t)epaddr;

    // printf("Binary entrypoint is %" PRIxPTR "; invoking %p\n",
    //        (uintptr_t)ctx_.ehdr.e_entry, (void *)epaddr);

#if defined(__riscv)
    Elf_Shdr sym_shdr;
    for (size_t i = 0; i < ctx_.ehdr.e_shnum; i++)
    {
        memcpy(&sym_shdr,
            (void *)((uintptr_t)ctx_.elf + ctx_.ehdr.e_shoff + ctx_.ehdr.e_shentsize * i),
            ctx_.ehdr.e_shentsize);
        if (sym_shdr.sh_type == SHT_SYMTAB)
        {
            break;
        }
    }

    Elf_Shdr str_shdr;
    memcpy(&str_shdr,
        (void *)((uintptr_t)ctx_.elf + ctx_.ehdr.e_shoff + ctx_.ehdr.e_shentsize * sym_shdr.sh_link),
        ctx_.ehdr.e_shentsize);

    size_t sym_num = sym_shdr.sh_size / sym_shdr.sh_entsize;
    Elf_Sym symbol;
    for (size_t i = 0; i < sym_num; i++)
    {
        memcpy(&symbol,
            (void *)((uintptr_t)ctx_.elf + sym_shdr.sh_offset + sym_shdr.sh_entsize * i),
            sym_shdr.sh_entsize);
        if (!strncmp((char *)((uintptr_t)ctx_.elf + str_shdr.sh_offset + symbol.st_name),
                "__global_pointer$", 19))
        {
            break;
        }
    }
    /*
    asm volatile("addi sp, sp, -8;\
        sd gp, 0(sp);\
        mv gp, %0\
        "
                 :
                 : "r"(symbol.st_value + (uintptr_t)buf_)
                 :);
*/
#endif
    ep(name, hw_ctx_impl, rt_util_mt, nncase_mt_impl, inputs, rdata);
#if defined(__riscv)
    /*
        asm volatile("ld gp, 0(sp);\
            addi sp, sp, 8;\
            "
                     :
                     :
                     :);
    */
#endif
    free(ptr_);
#endif
    return 0;
}
