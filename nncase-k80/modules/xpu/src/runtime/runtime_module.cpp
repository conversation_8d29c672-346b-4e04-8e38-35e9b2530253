/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <fcntl.h>
#include <nncase/runtime/dbg.h>
#include <nncase/runtime/interpreter.h>
#include <nncase/runtime/runtime_loader.h>
#include <nncase/runtime/runtime_op_utility.h>
#include <nncase/runtime/xpu/runtime_function.h>
#include <nncase/runtime/xpu/runtime_module.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <system_error>
#include <unistd.h>

using namespace nncase;
using namespace nncase::runtime;
using namespace nncase::runtime::xpu;

typedef struct
{
    uint32_t tdim;
    uint32_t bdim;
    uint32_t ddim;
    uint32_t cdim;
} module_desc_header;

void *external_module_file_ = nullptr;
size_t external_module_size_ = 0;

result<void> xpu_runtime_module::initialize_before_functions(
    runtime_module_init_context &context) noexcept
{
    try_(context.read_section(
        ".desc", [this](auto reader, size_t) -> result<void>
        {
            auto header = reader.template read<module_desc_header>();
            this->tdim_ = header.tdim;
            this->bdim_ = header.bdim;
            this->ddim_ = header.ddim;
            this->cdim_ = header.cdim;
            return ok(); }));
    try_set(rdata_,
        context.get_or_read_section(".rdata", rdata_storage_, true));
    try_set(thread_local_rdata_, context.get_or_read_section(".thread_local_rdata", thread_local_rdata_storage_, false));
    try_set(block_local_rdata_, context.get_or_read_section(".block_local_rdata", block_local_rdata_storage_, false));

    auto xpu_external_module_path = context.interp().options().get<std::string>("xpu_external_module_path");
    if (xpu_external_module_path.is_ok() && !xpu_external_module_path.unwrap().empty())
    {
        auto path = xpu_external_module_path.unwrap();
        int fd = open(path.c_str(), O_RDONLY);
        if (fd == -1)
        {
            printf("plugin module open failed\n");
            return nncase::err(std::errc::no_such_file_or_directory);
        }

        struct stat file_stat;
        if (fstat(fd, &file_stat) == -1)
        {
            printf("plugin module stat failed\n");
            close(fd);
            return nncase::err(std::errc::io_error);
        }

        external_module_size_ = static_cast<size_t>(file_stat.st_size);
        external_module_file_ = mmap(nullptr, external_module_size_, PROT_READ | PROT_WRITE | PROT_EXEC, MAP_PRIVATE, fd, 0);
        close(fd);
        if (external_module_file_ == MAP_FAILED)
        {
            printf("plugin module map failed\n");
            return nncase::err(std::errc::operation_not_permitted);
        }

        printf("plugin module load success %p %ld\n", external_module_file_, external_module_size_);
        text_ = { reinterpret_cast<const std::byte *>(external_module_file_), external_module_size_ };
#ifndef SYS_MODE
        launcher_ = elfloader(path);
#endif
    }
    else
    {
        try_set(text_,
            context.get_or_read_section(".text", text_storage_, false));
#ifndef SYS_MODE
        launcher_ = elfloader((char *)text_.data(), text_.size());
#endif
    }

#ifdef SYS_MODE
    DUstream outerside_stream = NULL;
    {
        auto stream = interp().options().get_scalar_opt<int64_t>("stream");
        if (stream.is_ok())
        {
            outerside_stream = (DUstream)stream.unwrap();
            has_stream_ = true;
        }
    }

    launcher_ = duca_launcher { (char *)text_.data(), text_.size(), (char *)rdata_.data(), rdata_.size(), thread_local_rdata_header(0), (char *)thread_local_rdata_content().data(), block_local_rdata_header(0), (char *)block_local_rdata_content().data() };
    launcher_.init_duca(outerside_stream, cdim(), ddim(), bdim(), tdim());
#endif

    return ok();
}

xpu_runtime_module::~xpu_runtime_module()
{
#ifdef SYS_MODE
    for (size_t i = 0; i < kv_cache_storage_ptrs_.size(); i++)
    {
        CHECK(duMemFree(kv_cache_storage_ptrs_[i]));
    }
    launcher_.release_duca();
    if (external_module_file_)
    {
        munmap(external_module_file_, external_module_size_);
    }
#endif
}

result<uintptr_t>
xpu_runtime_module::native_handle(uint32_t flags) const noexcept
{
    CHECK_WITH_ERR(flags == 0, std::errc::invalid_argument);
#ifdef SYS_MODE
    return ok((uintptr_t)launcher_.module());
#else
    return err(std::errc::not_supported);
#endif
}

result<std::unique_ptr<runtime_function>>
xpu_runtime_module::create_function() noexcept
{
    std::unique_ptr<runtime_function> mod(new (std::nothrow)
            xpu_runtime_function(*this));
    if (mod)
        return ok(std::move(mod));
    return err(std::errc::not_enough_memory);
}

result<std::unique_ptr<runtime_module>> xpu::create_xpu_runtime_module()
{
    std::unique_ptr<runtime_module> mod(new (std::nothrow)
            xpu_runtime_module());
    if (mod)
        return ok(std::move(mod));
    return err(std::errc::not_enough_memory);
}

extern "C"
{
    NNCASE_MODULES_XPU_API void
    RUNTIME_MODULE_ACTIVATOR_NAME(result<std::unique_ptr<runtime_module>> &result)
    {
        result = create_xpu_runtime_module();
    }
}

#ifndef NNCASE_SIMULATOR
runtime_registration nncase::runtime::builtin_runtimes[] = {
    { xpu_module_type, RUNTIME_MODULE_ACTIVATOR_NAME }, {}
};
#endif
