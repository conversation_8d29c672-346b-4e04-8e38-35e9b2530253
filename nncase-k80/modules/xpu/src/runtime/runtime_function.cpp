/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include <nncase/llm/paged_attention_kv_cache.h>
#include <nncase/runtime/dbg.h>
#include <nncase/runtime/runtime_loader.h>
#include <nncase/runtime/runtime_op_utility.h>
#include <nncase/runtime/util.h>
#include <nncase/runtime/xpu/elfloader.h>
#include <nncase/runtime/xpu/runtime_function.h>

#ifdef SYS_MODE
#include <nncase/runtime/buffer.h>
#include <nncase/runtime/xpu/device_allocator.h>
#endif
using namespace nncase;
using namespace nncase::ntt::runtime;
using namespace nncase::runtime;
using namespace nncase::runtime::xpu;

namespace
{
typedef struct memory_range
{
    uint32_t start;
    uint32_t size;
} memory_range_t;

typedef struct desc_header
{
    uint32_t input_pool_size;

    uint32_t output_pool_size;

    uint32_t inputs;

    uint32_t outputs;

    uint32_t thread_local_data_usage;

    uint32_t block_local_data_usage;

    uint32_t output_align;

    char name[64];
} desc_header_t;

} // namespace

xpu_runtime_module &xpu_runtime_function::module() const noexcept
{
    return static_cast<xpu_runtime_module &>(runtime_function::module());
}

result<void> xpu_runtime_function::initialize_core(
    NNCASE_UNUSED runtime_function_init_context &context) noexcept
{
    
#ifdef SYS_MODE
    // FIXME: change this constant while we suport TP.
    size_t TP_size = 1;
    descs_ptr_ = module().launcher().alloc_device(sizeof(thread_paged_attention_kv_cache_desc) * TP_size);
#endif

    text_ = module().text().subspan(context.header().entrypoint,
        context.header().text_size);
    rdata_ = module().rdata();
#ifdef SYS_MODE
    auto lambda = [this](auto sr, size_t) -> result<void>
    {
        auto header = sr.template read<desc_header>();
        buffer_allocate_options options {};
        options.flags = DEVICE_BUFFER_ALLOCATE_UMA_ANY;
        options.alignment = header.output_align;
        try_var(output_buffer, buffer_allocator::device().allocate(header.output_pool_size, options));
        try_set(this->output_buffer_, output_buffer.template as<device_buffer_t>());
        thread_local_data_usage_ = header.thread_local_data_usage;
        block_local_data_usage_ = header.block_local_data_usage;
        std::memcpy(name_, header.name, sizeof(name_));
        return ok();
    };
#else
    auto lambda = [this](auto sr, size_t) -> result<void>
    {
        auto header = sr.template read<desc_header>();
        buffer_allocate_options options {};
        options.flags = HOST_BUFFER_ALLOCATE_CPU_ONLY;
        options.alignment = header.output_align;
        try_var(output_buffer, buffer_allocator::host().allocate(header.output_pool_size, options));
        try_set(this->output_buffer_, output_buffer.template as<host_buffer_t>());
        thread_local_data_usage_ = header.thread_local_data_usage;
        block_local_data_usage_ = header.block_local_data_usage;
        std::memcpy(name_, header.name, sizeof(name_));
        return ok();
    };
#endif
    try_(context.read_section(".desc", lambda));

    // Allocate input descs
    auto input_size = parameters_size();
    input_descs_.resize(input_size);
    input_shapes_.resize(input_size);
    input_strides_.resize(input_size);
    for (size_t i = 0; i < input_size; i++)
    {
        try_var(type, parameter_type(i));
        try_var(ttype, type.as<tensor_type>());
        auto rank = ttype->shape().rank();
        CHECK_WITH_ERR(rank.has_value(), std::errc::invalid_argument);
        input_shapes_[i].resize(*rank);
        input_strides_[i].resize(*rank);
        input_descs_[i] = thread_inout_desc {
            .data = nullptr,
            .size = 0,
            .shape = input_shapes_[i].data(),
            .strides = input_strides_[i].data(),
            .rank = *rank,
        };
    }

    // Allocate output descs
    auto output_size = return_size();
    output_descs_.resize(output_size);
    output_shapes_.resize(output_size);
    output_strides_.resize(output_size);
    for (size_t i = 0; i < output_size; i++)
    {
        try_var(type, return_type(i));
        try_var(ttype, type.as<tensor_type>());
        auto rank = ttype->shape().rank();
        CHECK_WITH_ERR(rank.has_value(), std::errc::invalid_argument);
        output_shapes_[i].resize(*rank);
        output_strides_[i].resize(*rank);
        output_descs_[i] = thread_inout_desc {
            .data = nullptr,
            .size = 0,
            .shape = output_shapes_[i].data(),
            .strides = output_strides_[i].data(),
            .rank = *rank,
        };
    }
#ifdef SYS_MODE
    module().launcher().alloc_numa_local_data(thread_local_data_usage_, block_local_data_usage_);
    module().launcher().alloc_uma_desc(input_descs_, output_descs_);
    module().launcher().get_function((char *)mangled_name().c_str());
#endif

    return ok();
}

std::string xpu_runtime_function::mangled_name()
{
    // only support function like name(nncase::ntt::runtime::thread_inout_desc const*, nncase::ntt::runtime::thread_inout_desc*, unsigned char*, unsigned char*)
    std::ostringstream result;
    result << "_Z" << strlen(name_) << name_ << "PKN6nncase3ntt7runtime17thread_inout_descEPS2_PhPvS7_S6_S7_S7_";
    return result.str();
}

result<value_t>
xpu_runtime_function::invoke_core(NNCASE_UNUSED std::span<value_t> parameters,
    NNCASE_UNUSED value_t return_value) noexcept
{
    size_t input_id = 0;
    std::vector<thread_paged_attention_kv_cache_desc *> inout_paged_kvcaches;
#ifdef SYS_MODE
    std::vector<DUdeviceptr> inout_host_to_phy_ptrs;
#endif
    for (auto arg : parameters)
    {
        try_var(t, arg.as<tensor>());

        if (t->dtype().is_a<reference_type_t>())
        {
            try_var(hb, t->buffer().as_host());
            try_var(m, hb.map(map_read_write));
            auto rt = t->dtype().as<reference_type_t>().expect(
                "now only support reference value type!");
            auto vt = rt->elemtype().as<value_type_t>().expect(
                "now only support reference value type!");
            if (vt->uuid() == datatype_t::paged_attention_kv_cache->uuid())
            {

                auto refspan = as_span<llm::paged_attention_kv_cache_node *>(m.buffer());
                thread_paged_attention_kv_cache_desc *descs = new thread_paged_attention_kv_cache_desc[refspan.size()];
                for (size_t i = 0; i < refspan.size(); i++)
                {
                    auto &node = refspan[i];
                    auto &desc = descs[i];
                    {
                        desc.num_seqs = node->num_seqs();
                        desc.num_tokens = node->num_tokens();
                        size_t buffer_size = 0;
                        {
#ifdef SYS_MODE
                            // DUdeviceptr context_lens_dev_ptr = NULL;
                            if (node->context_lens()->buffer().buffer().is_a<host_buffer_t>())
                            {
                                try_var(bf, node->context_lens()->buffer().as_host());
                                try_var(mbf, bf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                auto context_lens_dev_ptr = module().launcher().alloc_and_copy_to_device((const void *)mbf_span.data(), mbf_span.size_bytes());
                                desc.context_lens = (int64_t *)context_lens_dev_ptr;
                                inout_host_to_phy_ptrs.push_back(context_lens_dev_ptr);
                                buffer_size = mbf.buffer().size_bytes();
                            }
                            else
                            {
                                try_var(dbf, node->context_lens()->buffer().as_device());
                                try_var(mbf, dbf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                desc.context_lens = (int64_t *)mbf_span.data();
                                buffer_size = mbf.buffer().size_bytes();
                            }
                            // desc.context_lens = (int64_t *)context_lens_dev_ptr;
#else
                            try_var(bf, node->context_lens()->buffer().as_host());
                            try_var(mbf, bf.map(map_read_write));
                            desc.context_lens = (int64_t *)mbf.buffer().data();
                            buffer_size = mbf.buffer().size_bytes();
#endif
                            desc.context_lens_size = buffer_size / sizeof(int64_t);
                        }

                        {
#ifdef SYS_MODE
                            if (node->seq_lens()->buffer().buffer().is_a<host_buffer_t>())
                            {
                                try_var(bf, node->seq_lens()->buffer().as_host());
                                try_var(mbf, bf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                auto seq_lens_dev_ptr = module().launcher().alloc_and_copy_to_device((const void *)mbf_span.data(), mbf_span.size_bytes());
                                desc.seq_lens = (int64_t *)seq_lens_dev_ptr;
                                inout_host_to_phy_ptrs.push_back(seq_lens_dev_ptr);
                                buffer_size = mbf.buffer().size_bytes();
                            }
                            else
                            {
                                try_var(dbf, node->seq_lens()->buffer().as_device());
                                try_var(mbf, dbf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                desc.seq_lens = (int64_t *)mbf_span.data();
                                buffer_size = mbf.buffer().size_bytes();
                            }
#else
                            try_var(bf, node->seq_lens()->buffer().as_host());
                            try_var(mbf, bf.map(map_read_write));
                            desc.seq_lens = (int64_t *)mbf.buffer().data();
                            buffer_size = mbf.buffer().size_bytes();
#endif
                            desc.seq_lens_size = buffer_size / sizeof(int64_t);
                        }

                        // Paged attention specific parameters
                        {
#ifdef SYS_MODE
                            if (node->block_tables()->buffer().buffer().is_a<host_buffer_t>())
                            {
                                try_var(bf, node->block_tables()->buffer().as_host());
                                try_var(mbf, bf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                auto block_table_dev_ptr = module().launcher().alloc_and_copy_to_device((const void *)mbf_span.data(), mbf_span.size_bytes());
                                desc.block_table = (int64_t *)block_table_dev_ptr;
                                inout_host_to_phy_ptrs.push_back(block_table_dev_ptr);
                            }
                            else
                            {
                                try_var(dbf, node->block_tables()->buffer().as_device());
                                try_var(mbf, dbf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                desc.block_table = (int64_t *)mbf_span.data();
                            }
#else
                            try_var(bf, node->block_tables()->buffer().as_host());
                            try_var(mbf, bf.map(map_read_write));
                            desc.block_table = (int64_t *)mbf.buffer().data();
#endif
                            desc.block_table_shape[0] = node->block_tables()->shape()[0];
                            desc.block_table_shape[1] = node->block_tables()->shape()[1];
                            desc.block_table_shape[2] = node->block_tables()->shape()[2];
                        }

                        {
#ifdef SYS_MODE
                            if (node->slot_mapping()->buffer().buffer().is_a<host_buffer_t>())
                            {
                                try_var(bf, node->slot_mapping()->buffer().as_host());
                                try_var(mbf, bf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                auto slot_mapping_dev_ptr = module().launcher().alloc_and_copy_to_device((const void *)mbf_span.data(), mbf_span.size_bytes());
                                desc.slot_mapping = (int64_t *)slot_mapping_dev_ptr;
                                inout_host_to_phy_ptrs.push_back(slot_mapping_dev_ptr);
                            }
                            else
                            {
                                try_var(dbf, node->slot_mapping()->buffer().as_device());
                                try_var(mbf, dbf.map(map_read_write));
                                auto mbf_span = mbf.buffer();
                                desc.slot_mapping = (int64_t *)mbf_span.data();
                            }
#else
                            try_var(bf, node->slot_mapping()->buffer().as_host());
                            try_var(mbf, bf.map(map_read_write));
                            desc.slot_mapping = (int64_t *)mbf.buffer().data();
#endif
                            desc.slot_mapping_shape[0] = node->slot_mapping()->shape()[0];
                            desc.slot_mapping_shape[1] = node->slot_mapping()->shape()[1];
                        }
                        {
                            // FIXME: TP is not supported yet
                            if (node->kv_caches().size() == 1)
                            {
                                if (!node->kv_caches()[0]->dtype().equals(datatype_t::int64))
                                {
                                    return err(std::errc::not_supported);
                                }

                                try_var(hbf, node->kv_caches()[0]->buffer().as_host());
                                try_var(mbf, hbf.map(map_read_write));
                                auto kv_cache_addrs_span = runtime::as_span<const intptr_t>(mbf.buffer());

                                std::copy(kv_cache_addrs_span.begin(),
                                    kv_cache_addrs_span.end(),
                                    desc.kv_cache_addrs.begin());
                            }
                            else if (node->kv_caches().size() > 1) // for test_cli only, we have to mlloc device memory and copy kv storages into device.
                            {
                                if (node->kv_caches()[0]->dtype().equals(datatype_t::int64))
                                { // the kv storage's dtype can't be an address!
                                    return err(std::errc::not_supported);
                                }
#ifdef SYS_MODE
                                for (size_t j = 0; j < node->kv_caches().size(); j++)
                                {
                                    intptr_t kv_storage_dev_ptr = 0;

                                    if (node->kv_caches()[j]->buffer().buffer().is_a<host_buffer_t>())
                                    {
                                        try_var(bf, node->kv_caches()[j]->buffer().as_host());
                                        try_var(mbf, bf.map(map_read_write));
                                        auto mbf_span = mbf.buffer();
                                        kv_storage_dev_ptr = (intptr_t)module().launcher().alloc_and_copy_to_device((const void *)mbf_span.data(), mbf_span.size_bytes());
                                        desc.kv_cache_addrs[j] = kv_storage_dev_ptr;
                                        inout_host_to_phy_ptrs.push_back((DUdeviceptr)kv_storage_dev_ptr);
                                        buffer_size = mbf_span.size_bytes();
                                    }
                                    else
                                    {
                                        try_var(dbf, node->kv_caches()[j]->buffer().as_device());
                                        try_var(mbf, dbf.map(map_read_write));
                                        auto mbf_span = mbf.buffer();
                                        kv_storage_dev_ptr = (intptr_t)mbf_span.data();
                                        desc.kv_cache_addrs[j] = kv_storage_dev_ptr;
                                        buffer_size = mbf_span.size_bytes();
                                    }

                                    printf("alloc paged kv cache[%ld]: %ld %p\n", j, buffer_size, (void *)kv_storage_dev_ptr);
                                }

#else
                                for (size_t j = 0; j < node->kv_caches().size(); j++)
                                {

                                    if (node->kv_caches()[j]->buffer().buffer().is_a<host_buffer_t>())
                                    {
                                        try_var(bf, node->kv_caches()[j]->buffer().as_host());
                                        try_var(mbf, bf.map(map_read_write));
                                        auto mbf_span = mbf.buffer();
                                        desc.kv_cache_addrs[j] = reinterpret_cast<intptr_t>(mbf_span.data());
                                    }
                                    else
                                    {
                                        // can't be device buffer.
                                        return err(std::errc::not_supported);
                                    }
                                }
#endif
                            }
                        }
                    }
                }

                inout_paged_kvcaches.push_back(descs);
#ifdef SYS_MODE
                // descs must a host buffer, convert to device buffer before duca_launch.
                // auto pa_kvcache_desc = module().launcher().alloc_and_copy_to_device((const void *)descs, sizeof(thread_paged_attention_kv_cache_desc) * refspan.size());
                // inout_host_to_phy_ptrs.push_back(pa_kvcache_desc);
                module().launcher().copy_to_device((const void *)descs, descs_ptr_, sizeof(thread_paged_attention_kv_cache_desc) * refspan.size());
                input_descs_[input_id++] = thread_inout_desc {
                    .data = (std::byte *)descs_ptr_,
                    .size = sizeof(thread_paged_attention_kv_cache_desc) * refspan.size(),
                    .shape = const_cast<size_t *>(t->shape().data()),
                    .strides = const_cast<size_t *>(t->strides().data()),
                };
#else
                input_descs_[input_id++] = thread_inout_desc {
                    .data = (std::byte *)descs,
                    .size = sizeof(thread_paged_attention_kv_cache_desc) * refspan.size(),
                    .shape = const_cast<size_t *>(t->shape().data()),
                    .strides = const_cast<size_t *>(t->strides().data()),
                };
#endif
            }
            else
            {
                return err(std::errc::not_supported);
            }
            m.release();
        }
        else
        {
            int device_flag = t->buffer().buffer().is_a<device_buffer_t>() ? 1 : 0;

            if (device_flag)
            {
                try_var(db, t->buffer().as_device());
                try_var(m, db.map(map_read_write));
                input_descs_[input_id++] = thread_inout_desc {
                    .data = m.buffer().data(),
                    .size = m.buffer().size(),
                    .shape = const_cast<size_t *>(t->shape().data()),
                    .strides = const_cast<size_t *>(t->strides().data()),
                    .rank = t->shape().size(),
                };
                m.release();
            }
            else
            {

                try_var(hb, t->buffer().as_host());
                try_var(m, hb.map(map_read_write));

#ifdef SYS_MODE
                auto input_device_ptr = module().launcher().alloc_and_copy_to_device((const void *)m.buffer().data(), m.buffer().size());
                inout_host_to_phy_ptrs.push_back(input_device_ptr);

                input_descs_[input_id++] = thread_inout_desc {
                    .data = (std::byte *)input_device_ptr,
                    .size = m.buffer().size(),
                    .shape = const_cast<size_t *>(t->shape().data()),
                    .strides = const_cast<size_t *>(t->strides().data()),
                    .rank = t->shape().size(),
                };
#else
                input_descs_[input_id++] = thread_inout_desc {
                    .data = m.buffer().data(),
                    .size = m.buffer().size(),
                    .shape = const_cast<size_t *>(t->shape().data()),
                    .strides = const_cast<size_t *>(t->strides().data()),
                    .rank = t->shape().size(),
                };
#endif
                m.release();
            }
        }
    }

#ifdef SYS_MODE
    checked_try_var(output_buffer, output_buffer_.as<device_buffer_t>());
    try_var(mapped_output, output_buffer->map(map_read_write));
    std::byte *output_data_ptr = mapped_output.buffer().data();
    NNCASE_UNUSED size_t output_data_size_bytes = output_buffer_->size_bytes();
#else
    checked_try_var(output_buffer, output_buffer_.as<host_buffer_t>());
    try_var(mapped_output, output_buffer->map(map_read_write));
    std::byte *output_data_ptr = mapped_output.buffer().data();
    NNCASE_UNUSED size_t output_data_size_bytes = output_buffer_->size_bytes();
#endif

    module().func_cnt(1);
    printf("total func num=%ld, curr func id=%ld, func name=%s\n", module().func_num(), module().func_cnt(), name_);
#ifndef SYS_MODE
    auto thread_local_rdata_header = module().thread_local_rdata_header(0);
    auto thread_local_rdata = module().thread_local_rdata_content();
    auto block_local_rdata_header = module().block_local_rdata_header(0);
    auto block_local_rdata = module().block_local_rdata_content();
    module().launcher().invoke_elf(name_, &hw_ctx_mt_impl, &rt_util_mt_impl, &nncase_mt_impl, input_descs_, output_descs_,
        (uint8_t *)rdata_.data(), thread_local_rdata_header, (uint8_t *)thread_local_rdata.data(), block_local_rdata_header, (uint8_t *)block_local_rdata.data(), (uint8_t *)output_data_ptr);
#else
    module().launcher().invoke_kernel_func(input_descs_, output_descs_, (uint8_t *)output_data_ptr, output_data_size_bytes);
    for (size_t i = 0; i < inout_host_to_phy_ptrs.size(); i++)
    {
        CHECK(duMemFree(inout_host_to_phy_ptrs[i]));
    }

#endif

    std::vector<value_t> outputs(return_size());
    for (size_t i = 0; i < outputs.size(); i++)
    {
        try_set(outputs[i], create_output_tensor(i, parameters, output_data_ptr));
    }

    for (auto arg : parameters)
    {
        try_var(t, arg.as<tensor>());
        if (t->dtype().is_a<reference_type_t>())
        {
            try_var(b, t->buffer().as_host());
            try_(b.unmap());
        }
        else
        {
            if (t->buffer().buffer().is_a<host_buffer_t>())
            {
                try_var(b, t->buffer().as_host());
                try_(b.unmap());
            }
            else
            {
                try_var(b, t->buffer().as_device());
                try_(b.unmap());
            }
        }
    }

    for (auto ptrs : inout_paged_kvcaches)
    {
        delete[] ptrs;
    }

    auto output_value = outputs.size() == 1
        ? outputs[0]
        : tuple(std::in_place, std::move(outputs));
    return ok(output_value);
}

result<tensor>
xpu_runtime_function::create_output_tensor(size_t output_id,
    std::span<value_t> parameters,
    std::byte *output_data) noexcept
{
    auto &output_desc = output_descs_[output_id];
    buffer_slice buffer;
    intptr_t offset;
    // 1. Find in inputs
    for (size_t i = 0; i < input_descs_.size(); i++)
    {
        auto &candidate_desc = input_descs_[i];
        if (candidate_desc.data <= output_desc.data && candidate_desc.data + candidate_desc.size >= output_desc.data + output_desc.size)
        {
            try_var(t, parameters[i].as<tensor>());
            buffer = t->buffer();
            offset = output_desc.data - candidate_desc.data;
            break;
        }
    }

    // 2. Find in output buffer
    if (buffer.buffer().empty())
    {
        if (output_data <= output_desc.data && output_data + output_buffer_->size_bytes() >= output_desc.data + output_desc.size)
        {
            buffer = buffer_slice(output_buffer_);
            offset = output_desc.data - output_data;
        }
    }

    if (buffer.buffer().empty())
    {
        return err(std::errc::invalid_argument);
    }

    // 2. Fix offset & size
    buffer = buffer_slice(buffer.buffer(), buffer.start() + offset,
        output_desc.size);
    try_var(output_type, return_type(output_id));
    try_var(ttype, output_type.as<tensor_type>());
    return ok(tensor(std::in_place, ttype->dtype(), output_shapes_[output_id],
        output_strides_[output_id], buffer));
}