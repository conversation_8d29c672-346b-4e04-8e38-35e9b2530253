/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#ifdef SYS_MODE
#pragma once
#include <nncase/ntt/primitive_ops.h>
#include "../isa.h"

namespace nncase::ntt {
namespace ukernels {
template <class T> struct u_unary<ops::copy<T>, T, true> {
public:
    constexpr void operator()(const ops::copy<T> &, const T *input, size_t input_stride, T *output,
                              size_t output_stride, size_t count) noexcept {
        if (input_stride == 1 && output_stride == 1) {
            auto total_size = count * sizeof(T);
            if ((uintptr_t)input % 128 == 0 && (uintptr_t)output % 128 == 0 && total_size % 128 == 0 && total_size > 0) {
                size_t division_factor = 128;
                while (true) {
                    if (division_factor >= 16383)
                        break;
                    size_t next_division = division_factor * 2;            
                    if (total_size % next_division == 0)
                        division_factor = next_division;
                    else
                        break;
                }

                if (total_size / division_factor <= 32767)
                {
                    auto shape = ntt::make_shape(total_size / division_factor, division_factor);
                    auto stride = ntt::make_shape(total_size / division_factor, division_factor);
                    tdma_vhm2vhm<tdma_mode::tile, mem_format::ndhwc, mem_format::ndhwc, tdma_dtype_t::b8, int8_t>(shape, stride, stride, reinterpret_cast<const char *>(input), reinterpret_cast<char *>(output));
                } else {                    
                    memcpy(output, input, total_size);
                }
            } else {
                memcpy(output, input, count * sizeof(T));
            }
        } else {
            for (size_t i = 0; i < count; i++) {
                *output = *input;
                input += input_stride;
                output += output_stride;
            }
        }
    }
};
} // namespace ukernels
} // namespace nncase::ntt
#endif