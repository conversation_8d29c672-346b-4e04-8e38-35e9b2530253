#pragma once
#include <nano_kernel_lib.h>
#include <riscv_cop.h>
#include <riscv_duca_builtin_vars.h>
#include <string.h>

#ifndef INT64_C
#define INT64_C(c) c##L
#endif
#define KiB (INT64_C(1) << 10)
#define MiB (INT64_C(1) << 20)

/* k80 xpu registers */
#define K80_XPU_GLOBAL_EVENT_CNT 256
#define K80_XPU_EVENT_CNT 16
#define K80_HARTS_PER_XPU 4
#define K80_XPU_EVENT_REG_WIDTH_SHIFTS 3

#define K80_XPU_EVENT_IO_SIZE (16 * MiB)
#define K80_XPU_TOTAL_EVENT_REG_SIZE (4 * MiB)
#define K80_XPU_PER_EVENT_REG_SIZE (4 * KiB)
#define K80_XPU_GLOBAL_REG_OFFSET (2 * MiB)
#define K80_XPU_GLOBAL_REG_SIZE (4 * KiB)

#define K80_XPU_GLOBAL_REG_SIZE_SHIFTS 12
#define K80_XPU_EVENT_REG_SIZE_SHIFTS 12

#define XPU_EVENT_RESET 0x0
#define XPU_EVENT_CONFIG 0x8
#define XPU_EVENT_STATE 0x10
#define XPU_EVENT_TARGET_VALUE 0x18
#define XPU_EVENT_TARGET_BITMAP 0x20
#define XPU_EVENT_CURRENT_VALUE 0x28
#define XPU_EVENT_PENDING_BITMAP 0x30
#define XPU_EVENT_HART0_PENDING_BIT 0x38
#define XPU_EVENT_HART1_PENDING_BIT 0x40
#define XPU_EVENT_HART2_PENDING_BIT 0x48
#define XPU_EVENT_HART3_PENDING_BIT 0x50
#define XPU_EVENT_EVENT_HART0_WIRE 0x58
#define XPU_EVENT_EVENT_HART1_WIRE 0x60
#define XPU_EVENT_EVENT_HART2_WIRE 0x68
#define XPU_EVENT_EVENT_HART3_WIRE 0x70

#define XPU_GLOBAL_PENDING_BITMAP 0x0
#define XPU_GLOBAL_HART0_PENDING_BIT 0x8
#define XPU_GLOBAL_HART1_PENDING_BIT 0x10
#define XPU_GLOBAL_HART2_PENDING_BIT 0x18
#define XPU_GLOBAL_HART3_PENDING_BIT 0x20
#define XPU_GLOBAL_EVENT_HART0_WIRE 0x28
#define XPU_GLOBAL_EVENT_HART1_WIRE 0x30
#define XPU_GLOBAL_EVENT_HART2_WIRE 0x38
#define XPU_GLOBAL_EVENT_HART3_WIRE 0x40

/* k80 soc mailbox registers */
#define K80_SOC_CHIP_CNT 8
#define K80_DIES_PER_CHIP 2
#define K80_XPUS_PER_DIE 32
#define K80_XPUS_PER_BITMAP 8
#define K80_HARTS_PER_XPU 4
#define K80_MAILBOX_IRQ 4

#define K80_SOC_EVENT_CNT 256
#define K80_SOC_SPINLOCK_CNT 256
#define K80_SOC_MAILBOX_REG_WIDTH 32
#define K80_SOC_MAILBOX_REG_WIDTH_SHIFTS 2
#define K80_SOC_MAILBOX_EVENT_OFFSET_SHIFTS 8

#define K80_SOC_TOTAL_MAILBOX_REG_SIZE (132 * KiB)
#define K80_SOC_MAILBOX_GLOBAL_REG_SIZE 0x100
#define K80_SOC_MAILBOX_EVENT_OFFSET 0x100
#define K80_SOC_MAILBOX_EVENT_REG_SIZE 0x100
#define K80_SOC_MAILBOX_SPINLOCK_OFFSET 0x20000
#define K80_SOC_MAILBOX_SPINLOCK_SIZE 0x400

/* mailbox global reg */
#define MAILBOX_MAX_OUTSTANDING 0x0
#define MAILBOX_CROSS_DIE_SYNC_ADDR_LOW 0x4
#define MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH 0x8
#define MAILBOX_CORSS_CHIP0_SYNC_ADDR_LOW 0xc
#define MAILBOX_CORSS_CHIP0_SYNC_ADDR_HIGH 0x10
#define MAILBOX_CORSS_CHIP1_SYNC_ADDR_LOW 0x14
#define MAILBOX_CORSS_CHIP1_SYNC_ADDR_HIGH 0x18
#define MAILBOX_CORSS_CHIP2_SYNC_ADDR_LOW 0x1c
#define MAILBOX_CORSS_CHIP2_SYNC_ADDR_HIGH 0x20
#define MAILBOX_CORSS_CHIP3_SYNC_ADDR_LOW 0x24
#define MAILBOX_CORSS_CHIP3_SYNC_ADDR_HIGH 0x28
#define MAILBOX_CORSS_CHIP4_SYNC_ADDR_LOW 0x2c
#define MAILBOX_CORSS_CHIP4_SYNC_ADDR_HIGH 0x30
#define MAILBOX_CORSS_CHIP5_SYNC_ADDR_LOW 0x34
#define MAILBOX_CORSS_CHIP5_SYNC_ADDR_HIGH 0x38
#define MAILBOX_CORSS_CHIP6_SYNC_ADDR_LOW 0x3c
#define MAILBOX_CORSS_CHIP6_SYNC_ADDR_HIGH 0x40
#define MAILBOX_CORSS_CHIP7_SYNC_ADDR_LOW 0x44
#define MAILBOX_CORSS_CHIP7_SYNC_ADDR_HIGH 0x48
#define MAILBOX_XPU_PENDING_OFFSET_ADDR 0x4c
#define MAILBOX_XPU_IOBASE_ADDR_LOW 0x50
#define MAILBOX_XPU_IOBASE_ADDR_HIGH 0x54
#define MAILBOX_CHIP_ID 0x58
#define MAILBOX_XPU_IO_SIZE 0x5c
#define MAILBOX_TIMER_OUT_STATUS0 0x60
#define MAILBOX_TIMER_OUT_STATUS1 0x64
#define MAILBOX_TIMER_OUT_STATUS2 0x68
#define MAILBOX_TIMER_OUT_STATUS3 0x6c
#define MAILBOX_TIMER_OUT_STATUS4 0x70
#define MAILBOX_TIMER_OUT_STATUS5 0x74
#define MAILBOX_TIMER_OUT_STATUS6 0x78
#define MAILBOX_TIMER_OUT_STATUS7 0x7c
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS0 0x80
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS1 0x84
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS2 0x88
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS3 0x8c
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS4 0x90
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS5 0x94
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS6 0x98
#define MAILBOX_TARGET_VALUE_CONFIG_EXCEPT_STATUS7 0x9c
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS0 0xa0
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS1 0xa4
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS2 0xa8
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS3 0xac
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS4 0xb0
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS5 0xb4
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS6 0xb8
#define MAILBOX_EVENT_CONFIG_EXCEPT_STATUS7 0xbc
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS0 0xc0
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS1 0xc4
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS2 0xc8
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS3 0xcc
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS4 0xd0
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS5 0xd4
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS6 0xd8
#define MAILBOX_CROSS_DIECHIP_EXCEPT_STATUS7 0xdc

/* event */
#define MAILBOX_EVENT_MESSAGE_event_message_bits 0x0
#define MAILBOX_EVENT_CONFIG 0x4
#define MAILBOX_EVENT_STATE 0x8
#define MAILBOX_EVENT_TARGET_VALUE 0xc
#define MAILBOX_EVENT_TARGET_BITMAP0 0x10
#define MAILBOX_EVENT_TARGET_BITMAP1 0x14
#define MAILBOX_EVENT_TARGET_BITMAP2 0x18
#define MAILBOX_EVENT_TARGET_BITMAP3 0x1c
#define MAILBOX_EVENT_TARGET_BITMAP4 0x20
#define MAILBOX_EVENT_TARGET_BITMAP5 0x24
#define MAILBOX_EVENT_TARGET_BITMAP6 0x28
#define MAILBOX_EVENT_TARGET_BITMAP7 0x2c
#define MAILBOX_EVENT_CURRENT_VALUE 0x34
#define MAILBOX_EVENT_PENDING_BITMAP0 0x38
#define MAILBOX_EVENT_PENDING_BITMAP1 0x3c
#define MAILBOX_EVENT_PENDING_BITMAP2 0x40
#define MAILBOX_EVENT_PENDING_BITMAP3 0x44
#define MAILBOX_EVENT_PENDING_BITMAP4 0x48
#define MAILBOX_EVENT_PENDING_BITMAP5 0x4c
#define MAILBOX_EVENT_PENDING_BITMAP6 0x50
#define MAILBOX_EVENT_PENDING_BITMAP7 0x54
#define MAILBOX_EVENT_RESET 0x5c
#define MAILBOX_CROSS_DIE_SYNC 0x60
#define MAILBOX_CROSS_CHIP_SYNC0 0x64
#define MAILBOX_CROSS_CHIP_SYNC1 0x68
#define MAILBOX_CROSS_CHIP_SYNC2 0x6c
#define MAILBOX_CROSS_CHIP_SYNC3 0x70
#define MAILBOX_CROSS_CHIP_SYNC4 0x74
#define MAILBOX_CROSS_CHIP_SYNC5 0x78
#define MAILBOX_CROSS_CHIP_SYNC6 0x7c
#define MAILBOX_CROSS_CHIP_SYNC7 0xa4
#define MAILBOX_CROSS_DIE_EVENT_IDX 0x80
#define MAILBOX_CROSS_CHIP_SYNC0_EVENT_IDX 0x84
#define MAILBOX_CROSS_CHIP_SYNC1_EVENT_IDX 0x88
#define MAILBOX_CROSS_CHIP_SYNC2_EVENT_IDX 0x8c
#define MAILBOX_CROSS_CHIP_SYNC3_EVENT_IDX 0x90
#define MAILBOX_CROSS_CHIP_SYNC4_EVENT_IDX 0x94
#define MAILBOX_CROSS_CHIP_SYNC5_EVENT_IDX 0x98
#define MAILBOX_CROSS_CHIP_SYNC6_EVENT_IDX 0x9c
#define MAILBOX_CROSS_CHIP_SYNC7_EVENT_IDX 0xa8
#define MAILBOX_EVENT_TIMEROUT 0xa0

/* spinlock */
#define MAILBOX_SPINLOCK0 0x20000
#define MAILBOX_SPINLOCK255 0x203fc

__shared_point__ unsigned long __attribute__((weak)) block_event_va_start;
__shared_point__ unsigned long __attribute__((weak)) block_event_va_size;
__shared_point__ unsigned long __attribute__((weak)) mailbox_va_start;
__shared_point__ unsigned long __attribute__((weak)) mailbox_va_size;

__attribute__((always_inline)) void __sync_block__(void)
{
    int thread_offset  = threadIdx.x << 3;
    const int set = 15; // event index
    unsigned long virt = K80_XPU_PER_EVENT_REG_SIZE * set;
    
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_EVENT_CONFIG), 0xF);
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_EVENT_TARGET_VALUE), 4);
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_EVENT_TARGET_BITMAP), 0xF);
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_EVENT_CURRENT_VALUE), 1);

    __riscv_duca_coprw(DUCA::Event, (virt + XPU_EVENT_EVENT_HART0_WIRE + thread_offset), 1);
    size_t status = __riscv_duca_coprr(DUCA::Event, XPU_EVENT_RESET);
    if (status == 0)
    {
        asm volatile("th.wfe");
        __riscv_duca_coprw(DUCA::Event, (virt + XPU_EVENT_HART0_PENDING_BIT + thread_offset), 1);
    }   
}


static inline __attribute__((always_inline)) void sync_c_d_y_x_rt(void)
{
    __sync_block__();    
}

#define DIE0_XPU_EVENT_BASE_ADDR 0x20100000000UL
#define DIE1_XPU_EVENT_BASE_ADDR 0x40100000000UL
#define DIE0_MAILBOX_BASE_ADDR 0x20000a80000UL
#define DIE1_MAILBOX_BASE_ADDR 0x40000a80000UL
#define MAILBOX_SIZE 0x21000

// struct event_message_bits
// {
//     unsigned int reset : 1;
//     unsigned int mailbox_sync_level : 2;
//     unsigned int cross_chip_bitmap : 8;
//     unsigned int thread_id : 8;
//     unsigned int cp_mode : 2;
//     unsigned int event_target_value : 9;
//     unsigned int current_value : 2;
// };

// static inline __attribute__((always_inline)) void __global_sync__(void)
// {
//     unsigned long base;
//     unsigned long virt;
//     unsigned long sync_addr;
//     // int block_id = device_block_id();
//     int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
//     int set = 255; // event index
//     int xpu_id_in_die;
//     unsigned long soc_mailbox_virt;
//     struct event_message_bits message;
//     unsigned int message_val;

//     xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

//     base = mailbox_va_start;
//     virt = base + blockIdx.z * MAILBOX_SIZE;

//     if (blockIdx.z == 0)
//     {
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
//         sync_addr = DIE1_MAILBOX_BASE_ADDR;
//         *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
//         *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

//         soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

//         *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

//         message.reset = 1;
//         message.mailbox_sync_level = 1;
//         message.cross_chip_bitmap = 0;
//         message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
//         message.cp_mode = 2;
//         message.event_target_value = gridDim.x * gridDim.y * blockDim.x;
//         message.current_value = 1;
//         memcpy(&message_val, &message, sizeof(message));

//         *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
//         base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
//         virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
//         *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

//         asm volatile("th.wfe");
//         *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
//     }
//     else if (blockIdx.z == 1)
//     {
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
//         *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
//         sync_addr = DIE0_MAILBOX_BASE_ADDR;
//         *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
//         *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

//         soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

//         *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
//         message.reset = 1;
//         message.mailbox_sync_level = 1;
//         message.cross_chip_bitmap = 0;
//         message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
//         message.cp_mode = 2;
//         message.event_target_value = gridDim.x * gridDim.y * blockDim.x;
//         message.current_value = 1;
//         memcpy(&message_val, &message, sizeof(message));
//         *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

//         base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
//         virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
//         *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

//         asm volatile("th.wfe");
//         *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
//     }
// }

void sync_init()
{   
    int thread_id = threadIdx.x;
    int row_id    = blockIdx.y;
    int col_id    = blockIdx.x;
    int die_id    = blockIdx.z;
    unsigned long soc_mailbox_virt;

    if ((row_id == 0) && (col_id == 0) && (thread_id == 0))
    {
        if (die_id == 0)
        {
            *(volatile unsigned int *)(mailbox_va_start + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW)  = DIE1_MAILBOX_BASE_ADDR & 0xFFFFFFFF;
            *(volatile unsigned int *)(mailbox_va_start + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (DIE1_MAILBOX_BASE_ADDR >> 32) & 0xFFFFFFFF;
            *(volatile unsigned int *)(mailbox_va_start + MAILBOX_XPU_PENDING_OFFSET_ADDR)  = K80_XPU_GLOBAL_REG_OFFSET;
            *(volatile unsigned int *)(mailbox_va_start + MAILBOX_XPU_IOBASE_ADDR_LOW)      = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
            *(volatile unsigned int *)(mailbox_va_start + MAILBOX_XPU_IOBASE_ADDR_HIGH)     = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
            *(volatile unsigned int *)(mailbox_va_start + MAILBOX_XPU_IO_SIZE)              = K80_XPU_EVENT_IO_SIZE;   
        }
        else
        {
            soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE;
            *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW)  = DIE0_MAILBOX_BASE_ADDR & 0xFFFFFFFF;
            *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (DIE0_MAILBOX_BASE_ADDR >> 32) & 0xFFFFFFFF;
            *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_XPU_PENDING_OFFSET_ADDR)  = K80_XPU_GLOBAL_REG_OFFSET;
            *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_XPU_IOBASE_ADDR_LOW)      = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
            *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_XPU_IOBASE_ADDR_HIGH)     = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
            *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_XPU_IO_SIZE)              = K80_XPU_EVENT_IO_SIZE;  
        }
    }
}

// FIXME: always inline
static inline __attribute__((optnone)) void __global_sync__(void)
{
    int thread_id  = threadIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    const int set = 0; // event index, workround, orign = 255
    int xpu_id_in_die;
    unsigned long virt;
    unsigned long soc_mailbox_virt;
    unsigned int message_val;
    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    unsigned int global_thread_id = xpu_id_in_die * thread_num + thread_id;
    unsigned int event_target_value = row_num * col_num * thread_num;
    message_val = 0x40100003 + (global_thread_id << 11) + (event_target_value << 21);


    soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE * die_id + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

    // 每次都会复位为0，理论上每次均需要配置，不可提前配置，此处用event_idx = 0 workround
    // if ((row_id == 0) && (col_id == 0) && (thread_id == 0)){
    //     *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
    // }
    *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;


    virt = K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_GLOBAL_EVENT_HART0_WIRE + (thread_id << 3)), 1);
    size_t status = __riscv_duca_coprr(DUCA::Event, XPU_EVENT_RESET);
    if (status == 0)
    {
        asm volatile("th.wfe");
        __riscv_duca_coprw(DUCA::Event, (virt + XPU_GLOBAL_HART0_PENDING_BIT + (thread_id << 3)), 1);
    }   
}

static inline __attribute__((always_inline)) void sync_c_rd_ry_rx_rt(void)
{
    __global_sync__();
}

static inline __attribute__((always_inline)) void sync_c_d_y_rx_t(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 223 + blockIdx.y * blockDim.x + threadIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.x;
        message.current_value = 1;
        // memcpy(&message_val, &message, sizeof(message));
        message_val = ((unsigned int *)&message)[0];

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.x;
        message.current_value = 1;
        // memcpy(&message_val, &message, sizeof(message));
        message_val = ((unsigned int *)&message)[0];
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_d_ry_x_t(void)
{
    int thread_id  = threadIdx.x;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;

    int set = 207 + col_id * thread_num + thread_id;
    int xpu_id_in_die;
    unsigned long virt;
    unsigned long soc_mailbox_virt;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    unsigned int global_thread_id = xpu_id_in_die * thread_num + thread_id;
    unsigned int event_target_value = row_num;
    message_val = 0x40100001 + (global_thread_id << 11) + (event_target_value << 21);

    soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE * die_id + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;
    *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;


    virt = K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_GLOBAL_EVENT_HART0_WIRE + (thread_id << 3)), 1);
    size_t status = __riscv_duca_coprr(DUCA::Event, XPU_EVENT_RESET);
    if (status == 0)
    {
        asm volatile("th.wfe");
        __riscv_duca_coprw(DUCA::Event, (virt + XPU_GLOBAL_HART0_PENDING_BIT + (thread_id << 3)), 1);
    }   
}

static inline __attribute__((always_inline)) void sync_c_rd_y_x_t()
{
    // pass
}

static inline __attribute__((always_inline)) void sync_c_d_y_rx_rt(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int col_num    = gridDim.x;

    int set = 199 + row_id;
    int xpu_id_in_die;
    unsigned long virt;
    unsigned long soc_mailbox_virt;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    unsigned int global_thread_id = xpu_id_in_die * thread_num + thread_id;
    unsigned int event_target_value = col_num * thread_num;
    message_val = 0x40100001 + (global_thread_id << 11) + (event_target_value << 21);

    soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE * die_id + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;
    *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

    
    virt = K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
    __riscv_duca_coprw(DUCA::Event, (virt + XPU_GLOBAL_EVENT_HART0_WIRE + (thread_id << 3)), 1);
    size_t status = __riscv_duca_coprr(DUCA::Event, XPU_EVENT_RESET);
    if (status == 0)
    {
        asm volatile("th.wfe");
        __riscv_duca_coprw(DUCA::Event, (virt + XPU_GLOBAL_HART0_PENDING_BIT + (thread_id << 3)), 1);
    } 
}

static inline __attribute__((always_inline)) void sync_c_d_ry_x_rt(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 195 + blockIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_rd_y_x_rt(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 163 + blockIdx.y * gridDim.x + blockIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_d_ry_rx_t(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 159 + threadIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * gridDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * gridDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_rd_y_rx_t(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 127 + blockIdx.y * blockDim.x + threadIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_rd_ry_x_t(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 111 + blockIdx.x * blockDim.x + threadIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_d_ry_rx_rt(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 110;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * gridDim.x * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 0;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * gridDim.x * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_rd_y_rx_rt(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 102 + blockIdx.y;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.x * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.x * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_rd_ry_x_rt(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 98 + blockIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * blockDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}

static inline __attribute__((always_inline)) void sync_c_rd_ry_rx_t(void)
{
    int thread_id  = threadIdx.x;
    int row_id     = blockIdx.y;
    int col_id     = blockIdx.x;
    int die_id     = blockIdx.z;

    int thread_num = blockDim.x;
    int row_num    = gridDim.y;
    int col_num    = gridDim.x;

    unsigned long base;
    unsigned long virt;
    unsigned long sync_addr;
    // int block_id = device_block_id();
    int block_id = die_id * row_num * col_num + row_id * col_num + col_id;
    int set = 94 + threadIdx.x;
    int xpu_id_in_die;
    unsigned long soc_mailbox_virt;
    struct event_message_bits message;
    unsigned int message_val;

    xpu_id_in_die = __riscv_duca_coprr(DUCA::XPU_IO_REG, PHYSICAL_XPU_ID);

    base = mailbox_va_start;
    virt = base + blockIdx.z * MAILBOX_SIZE;

    if (blockIdx.z == 0)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE0_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE0_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE1_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;

        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * gridDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;
        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
    else if (blockIdx.z == 1)
    {
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_LOW) = DIE1_XPU_EVENT_BASE_ADDR & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IOBASE_ADDR_HIGH) = (DIE1_XPU_EVENT_BASE_ADDR >> 32) & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_IO_SIZE) = K80_XPU_EVENT_IO_SIZE;
        *(volatile unsigned int *)(virt + MAILBOX_XPU_PENDING_OFFSET_ADDR) = K80_XPU_GLOBAL_REG_OFFSET;
        sync_addr = DIE0_MAILBOX_BASE_ADDR;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_LOW) = sync_addr & 0xFFFFFFFF;
        *(volatile unsigned int *)(virt + MAILBOX_CROSS_DIE_SYNC_ADDR_HIGH) = (sync_addr >> 32) & 0xFFFFFFFF;

        soc_mailbox_virt = mailbox_va_start + MAILBOX_SIZE + K80_SOC_MAILBOX_EVENT_OFFSET + K80_SOC_MAILBOX_EVENT_REG_SIZE * set;

        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_CROSS_DIE_EVENT_IDX) = set;
        message.reset = 1;
        message.mailbox_sync_level = 1;
        message.cross_chip_bitmap = 0;
        message.thread_id = xpu_id_in_die * blockDim.x + threadIdx.x;
        message.cp_mode = 2;
        message.event_target_value = gridDim.y * gridDim.x;
        message.current_value = 1;
        memcpy(&message_val, &message, sizeof(message));
        *(volatile unsigned int *)(soc_mailbox_virt + MAILBOX_EVENT_MESSAGE_event_message_bits) = message_val;

        base = block_event_va_start + (K80_XPU_TOTAL_EVENT_REG_SIZE * block_id);
        virt = base + K80_XPU_GLOBAL_REG_OFFSET + K80_XPU_GLOBAL_REG_SIZE * set;
        *((volatile unsigned long *)(virt + XPU_GLOBAL_EVENT_HART0_WIRE) + threadIdx.x) = set;

        asm volatile("th.wfe");
        *((volatile unsigned long *)(virt + XPU_GLOBAL_HART0_PENDING_BIT) + threadIdx.x) = 1;
    }
}
