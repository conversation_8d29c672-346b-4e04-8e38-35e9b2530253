/* sliceright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a slice of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#ifdef SYS_MODE
#include "isa.h"
#include "copy.h"
#include <nncase/ntt/apply.h>
#include <nncase/ntt/utility.h>

namespace nncase::ntt
{
template <Tensor TIn, typename TOut, typename TBegins, typename TEnds,
    FixedDimensions TAxes = decltype(make_index_shape<TBegins::rank()>()),
    FixedDimensions TSteps = decltype(make_ones_shape<TBegins::rank()>())>
void tdma_slice(const TIn &input, TOut &&output, const TBegins &begins,
    [[maybe_unused]] const TEnds &ends, const TAxes &axes = {},
    const TSteps &steps = {})
{
    static_assert(TBegins::rank() == TEnds::rank() && TBegins::rank() == TAxes::rank() && TBegins::rank() == TSteps::rank(),
        "begins, ends, axes and steps must have the same "
        "rank");

    auto [new_begins, new_steps] = slice_detail::translate_slice_params(
        input.shape(), begins, axes, steps);

    const auto steps_all_ones = steps.aggregate(true, [&](auto is_one, auto axis, auto i)
        { return is_one && steps[i] == 1_dim; });

    if (steps_all_ones)
    {
        auto sliced_input = input.view(new_begins, output.shape());
        ntt::tensor_copy_sync(sliced_input, output);
    }
    else
    {
        ntt::slice(input, output, begins, ends, axes, steps);
    }
}
} // namespace nncase::ntt
#endif