/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once

#include <cassert>
#include <cstdint>
#include <nncase/bfloat16.h>
#include <nncase/float8.h>
#include <nncase/half.h>
#include <nncase/ntt/ntt.h>
#include <riscv_vector.h>
#include <type_traits>
namespace nncase::ntt
{

namespace detail
{
    template <class T>
    struct is_paged_attention_t : std::false_type
    {
    };

    template <class Mesh, class Config>
    struct is_paged_attention_t<caching::paged_attention_kv_cache<Mesh, Config>> : std::true_type
    {
    };

}

enum class tmma_dtype_t
{
    fp8_1_5_2 = 0,
    fp8_1_4_3 = 1,
    fp16 = 2,
    bf16 = 3,
    fp32 = 4,
    int8 = 8,
    int32 = 12,
};

enum class tdma_mode
{
    tile = 0b00,
    im2col = 0b01,
    memset = 0b10
};

enum class mem_format
{
    ndhwc = 0b0,
    ncdwhcx = 0b1
};

enum class mem_type
{
    vhm = 0b0,
    dm = 0b1
};

enum class tdma_dtype_t
{
    b8 = 0b000,
    fp16 = 0b001,
    bf16 = 0b010,
    fp32 = 0b011,
    rd_fp16 = 0b101,
    rd_bf16 = 0b110,
    rd_fp32 = 0b111
};

template <uint32_t m, tmma_dtype_t dt, uint32_t reg_id, bool transpose = false>
inline void tload_a(const char *addr)
{
    static_assert(m == 32 || m == 64, "M only support 32 or 64");

    constexpr auto stride = m;
    constexpr auto m_flag = (m / 32) - 1;
    constexpr auto tp_flag = transpose ? 1 : 0;
    uint64_t cfg = (stride << 6) | ((uint32_t)dt << 2) | (m_flag << 1) | tp_flag;

#ifdef __clang__
    if constexpr (reg_id == 0b100)
        asm volatile("tloadA tra0, %0, %1, 0x0" : : "r"(addr), "r"(cfg) : "memory");
    else
        asm volatile("tloadA tra1, %0, %1, 0x0" : : "r"(addr), "r"(cfg) : "memory");
#else
    asm volatile("tloadA %0, %1, %2, 0x0\n\t" : : "i"(reg_id), "r"(addr), "r"(cfg) : "memory");
#endif
}

template <uint32_t k, tmma_dtype_t dt, uint32_t reg_id, bool transpose = false>
inline void tload_b(const char *addr)
{
    static_assert(k == 128 || k == 32 || k == 64,
        "K only support 32 or 64 or 128");

    constexpr auto stride = k;
    constexpr auto tp_flag = transpose ? 1 : 0;
    uint64_t cfg = (stride << 6) | ((uint32_t)dt << 2) | tp_flag;

#ifdef __clang__
    if constexpr (reg_id == 0b100)
        asm volatile("tloadB trb0, %0, %1, 0x0\n\t" : : "r"(addr), "r"(cfg) : "memory");
    else
        asm volatile("tloadB trb1, %0, %1, 0x0\n\t" : : "r"(addr), "r"(cfg) : "memory");
#else
    asm volatile("tloadB %0, %1, %2, 0x0\n\t" : : "i"(reg_id), "r"(addr), "r"(cfg) : "memory");
#endif
}

template <uint32_t m, tmma_dtype_t dt, uint32_t reg_id>
inline void tload_c(const char *addr)
{
    static_assert(m == 32 || m == 64, "M only support 32 or 64");
    static_assert(dt == tmma_dtype_t::fp32 || dt == tmma_dtype_t::int32,
        "C only support 32 bits");

    constexpr auto stride = m;
    constexpr auto m_flag = (m / 32) - 1;
    uint64_t cfg = (stride << 6) | ((uint32_t)dt << 2) | (m_flag << 1);

#ifdef __clang__
    if constexpr (reg_id == 0b100)
        asm volatile("tloadC trc0, %0, %1, 0x0\n\t" : : "r"(addr), "r"(cfg) : "memory");
    else
        asm volatile("tloadC trc1, %0, %1, 0x0\n\t" : : "r"(addr), "r"(cfg) : "memory");
#else
    asm volatile("tloadC %0, %1, %2, 0x0\n\t" : : "i"(reg_id), "r"(addr), "r"(cfg) : "memory");
#endif
}

template <uint32_t m, tmma_dtype_t dt, uint32_t reg_id>
inline void tstore(char *addr)
{
    static_assert(m == 32 || m == 64, "M only support 32 or 64");
    static_assert(dt == tmma_dtype_t::bf16 || dt == tmma_dtype_t::fp16 || dt == tmma_dtype_t::fp32 || dt == tmma_dtype_t::int32,
        "C only support 32 bits");

    constexpr auto stride = m;
    constexpr auto m_flag = (m / 32) - 1;
    uint64_t cfg = (stride << 6) | ((uint32_t)dt << 2) | (m_flag << 1);

#ifdef __clang__
    if constexpr (reg_id == 0b100)
        asm volatile("tstoreC trc0, %0, %1, 0x1\n\t" : : "r"(addr), "r"(cfg) : "memory");
    else
        asm volatile("tstoreC trc1, %0, %1, 0x1\n\t" : : "r"(addr), "r"(cfg) : "memory");
#else
    asm volatile("tstoreC %0, %1, %2, 0x1\n\t" : : "i"(reg_id), "r"(addr), "r"(cfg) : "memory");
#endif
}

inline void tstore_wait()
{
    uint64_t status;
    const uint64_t value = 0x18;
    // asm volatile("cop.coprr %0, 3, %1" : "=r"(status) : "r"(value));
}

template <uint32_t m, tmma_dtype_t dt>
inline void ttrans(char *addr)
{
    static_assert(m == 32 || m == 64, "M only support 32 or 64");
    // TODO:
}

template <uint32_t m, tmma_dtype_t dt_a, tmma_dtype_t dt_b, uint32_t reg_id_ab, uint32_t reg_id_cd, bool acc = false, bool start = false>
inline void tmma()
{
    static_assert(m == 32 || m == 64, "M only support 32 or 64");

    constexpr auto m_flag = (m / 32) - 1;
    uint64_t cfg = ((uint32_t)dt_b << 6) | ((uint32_t)dt_a << 2) | (m_flag << 1);

    if constexpr (acc && start)
    {
#ifdef __clang__
        if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra0, trb0, trc0, %0, 0x1, 0x1\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b101)
            asm volatile("tmma trc1, tra0, trb0, trc1, %0, 0x1, 0x1\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b101 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra1, trb1, trc0, %0, 0x1, 0x1\n\t" : : "r"(cfg) : "memory");
        else
            asm volatile("tmma trc1, tra1, trb1, trc1, %0, 0x1, 0x1\n\t" : : "r"(cfg) : "memory");
#else
        asm volatile("tmma %1, %0, %0, %1, 0x1, %2, 0x1\n\t" : : "i"(reg_id_ab), "i"(reg_id_cd), "r"(cfg) : "memory");
#endif
    }
    else if constexpr (acc && !start)
    {
#ifdef __clang__
        if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra0, trb0, trc0, %0, 0x1, 0x0\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b101)
            asm volatile("tmma trc1, tra0, trb0, trc1, %0, 0x1, 0x0\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b101 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra1, trb1, trc0, %0, 0x1, 0x0\n\t" : : "r"(cfg) : "memory");
        else
            asm volatile("tmma trc1, tra1, trb1, trc1, %0, 0x1, 0x0\n\t" : : "r"(cfg) : "memory");
#else
        asm volatile("tmma %1, %0, %0, %1, 0x1, %2, 0x0\n\t" : : "i"(reg_id_ab), "i"(reg_id_cd), "r"(cfg) : "memory");
#endif
    }
    else if constexpr (!acc && start)
    {
#ifdef __clang__
        if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra0, trb0, trc0, %0, 0x0, 0x1\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b101)
            asm volatile("tmma trc1, tra0, trb0, trc1, %0, 0x0, 0x1\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b101 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra1, trb1, trc0, %0, 0x0, 0x1\n\t" : : "r"(cfg) : "memory");
        else
            asm volatile("tmma trc1, tra1, trb1, trc1, %0, 0x0, 0x1\n\t" : : "r"(cfg) : "memory");
#else
        asm volatile("tmma %1, %0, %0, %1, 0x0, %2, 0x1\n\t" : : "i"(reg_id_ab), "i"(reg_id_cd), "r"(cfg) : "memory");
#endif
    }
    else
    {
#ifdef __clang__
        if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra0, trb0, trc0, %0, 0x0, 0x0\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b100 && reg_id_cd == 0b101)
            asm volatile("tmma trc1, tra0, trb0, trc1, %0, 0x0, 0x0\n\t" : : "r"(cfg) : "memory");
        else if constexpr (reg_id_ab == 0b101 && reg_id_cd == 0b100)
            asm volatile("tmma trc0, tra1, trb1, trc0, %0, 0x0, 0x0\n\t" : : "r"(cfg) : "memory");
        else
            asm volatile("tmma trc1, tra1, trb1, trc1, %0, 0x0, 0x0\n\t" : : "r"(cfg) : "memory");
#else
        asm volatile("tmma %1, %0, %0, %1, 0x0, %2, 0x0\n\t" : : "i"(reg_id_ab), "i"(reg_id_cd), "r"(cfg) : "memory");
#endif
    }
}

static inline void tdma_write(unsigned addr, uint64_t val)
{
    asm volatile("cop.coprw 1, %0, %1" : : "r"(addr), "r"(val));
}

static inline uint64_t tdma_read(uint64_t addr)
{
    uint64_t val;
    asm volatile("cop.coprr %0, 1, %1" : "=r"(val) : "r"(addr));
    return val;
}

template <class TElem>
constexpr inline size_t to_tdma_size_expand()
{
    if constexpr (detail::is_paged_attention_t<TElem>::value)
    {
        static_assert(sizeof(TElem) < 2048, "kvcache obj must less than 2048!");
        return 2048;
    }
    else
    {
        return sizeof(TElem);
    }
}

template <class T>
constexpr inline size_t to_tdma_layout_expand()
{
    using TE = nncase::ntt::element_or_scalar_t<T>;
    if constexpr (detail::is_paged_attention_t<TE>::value)
    {
        static_assert(sizeof(TE) < 2048, "kvcache obj must less than 2048!");
        return 2048;
    }
    else if constexpr (std::is_same_v<TE, int64_t>)
    {
        // note int64_t = 2*f32
        return 2;
    }
    else
    {
        return 1;
    }
}

template <class T>
static constexpr tdma_dtype_t to_tdma_type()
{
    using TE = nncase::ntt::element_or_scalar_t<T>;

    if constexpr (std::is_same_v<TE, int8_t> || std::is_same_v<TE, nncase::float_e5m2_t> || std::is_same_v<TE, nncase::float_e4m3_t> || detail::is_paged_attention_t<TE>::value || std::is_same_v<TE, bool>)
    {
        return tdma_dtype_t::b8;
    }
    else if constexpr (std::is_same_v<TE, nncase::half>)
    {
        return tdma_dtype_t::fp16;
    }
    else if constexpr (std::is_same_v<TE, nncase::bfloat16>)
    {
        return tdma_dtype_t::bf16;
    }
    else if constexpr (std::is_same_v<TE, int32_t> || std::is_same_v<TE, float> || std::is_same_v<TE, int64_t>)
    {
        return tdma_dtype_t::fp32;
    }
    else
    {
        static_assert(false, "type not impl!");
    }
}

template <tdma_mode mode, mem_format src_format, mem_format dst_format, tdma_dtype_t dt, class InShape, class InStride>
inline void tdma_vhm2dm(InShape shape, InStride stride, const char *src_addr, char *dst_addr)
{
    static_assert(src_format == dst_format || (src_format == mem_format::ndhwc && dst_format == mem_format::ncdwhcx), "Not supported vhm->dm format");
    mem_type src_type = mem_type::vhm;
    mem_type dst_type = mem_type::dm;

    uint64_t mem_format_type = ((uint32_t)mode << 7) | ((uint32_t)src_format << 6) | ((uint32_t)dst_format << 5) | ((uint32_t)src_type << 4) | ((uint32_t)dst_type << 3) | (uint32_t)dt;
    uint64_t src_layout = stride[stride.rank() - 1];
    stride.rank() > 1 ? (src_layout |= stride[stride.rank() - 2] << 16) : (src_layout |= 1L << 16);
    stride.rank() > 2 ? (src_layout |= stride[stride.rank() - 3] << 32) : (src_layout |= 1L << 32);
    stride.rank() > 3 ? (src_layout |= stride[stride.rank() - 4] << 48) : (src_layout |= 1L << 48);

    // dst_layout: no need
    // box_coordinates_off: no need
    uint64_t channel_control = 0b00;
    uint64_t sync_control = 0x0;
    uint64_t vhm_shape = (1ULL << 48) | (1ULL << 32) | (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    uint64_t vhm_shape_n = 0x1;
    // dm_shape: no need
    // src_addr: 48bits
    // dst_addr: 48bits
    uint64_t start_coordinate = 0x0;
    uint64_t start_coord_N = 0x0;
    uint64_t boxsize = (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    // im2col_offset: no need
    uint64_t pad_value = 0x0;
    uint64_t traversal_stride = (1ULL << 8) | (1ULL << 4) | 1;
    uint64_t config_finish = 0b1;

#ifdef __clang__
    tdma_write(0x100, mem_format_type);
    tdma_write(0x108, src_layout);
    tdma_write(0x120, channel_control);
    tdma_write(0x128, sync_control);
    tdma_write(0x180, vhm_shape);
    tdma_write(0x188, vhm_shape_n);
    tdma_write(0x198, (uint64_t)src_addr);
    tdma_write(0x1a0, (uint64_t)dst_addr);
    tdma_write(0x1a8, start_coordinate);
    tdma_write(0x1b0, start_coord_N);
    tdma_write(0x1b8, boxsize);
    tdma_write(0x1c8, pad_value);
    tdma_write(0x1d0, traversal_stride);
    tdma_write(0x1d8, config_finish);
#else
    asm volatile("tdma_write_cfg 0x100, %0\n\t" : : "r"(mem_format_type));
    asm volatile("tdma_write_cfg 0x108, %0\n\t" : : "r"(src_layout));
    asm volatile("tdma_write_cfg 0x120, %0\n\t" : : "r"(channel_control));
    asm volatile("tdma_write_cfg 0x128, %0\n\t" : : "r"(sync_control));
    asm volatile("tdma_write_cfg 0x180, %0\n\t" : : "r"(vhm_shape));
    asm volatile("tdma_write_cfg 0x188, %0\n\t" : : "r"(vhm_shape_n));
    asm volatile("tdma_write_cfg 0x198, %0\n\t" : : "r"(src_addr));
    asm volatile("tdma_write_cfg 0x1a0, %0\n\t" : : "r"(dst_addr));
    asm volatile("tdma_write_cfg 0x1a8, %0\n\t" : : "r"(start_coordinate));
    asm volatile("tdma_write_cfg 0x1b0, %0\n\t" : : "r"(start_coord_N));
    asm volatile("tdma_write_cfg 0x1b8, %0\n\t" : : "r"(boxsize));
    asm volatile("tdma_write_cfg 0x1c8, %0\n\t" : : "r"(pad_value));
    asm volatile("tdma_write_cfg 0x1d0, %0\n\t" : : "r"(traversal_stride));
    asm volatile("tdma_write_cfg 0x1d8, %0\n\t" : : "r"(config_finish));
#endif
}

template <tdma_mode mode, mem_format src_format, mem_format dst_format, tdma_dtype_t dt, class TInElem, class InShape, class InStride, class OutStride>
inline void tdma_vhm2vhm(InShape shape, InStride in_stride, OutStride out_stride, const char *src_addr, char *dst_addr)
{
    static_assert(src_format == dst_format, "Not supported vhm->vhm format");
    mem_type src_type = mem_type::vhm;
    mem_type dst_type = mem_type::vhm;

    uint64_t mem_format_type = ((uint32_t)mode << 7) | ((uint32_t)src_format << 6) | ((uint32_t)dst_format << 5) | ((uint32_t)src_type << 4) | ((uint32_t)dst_type << 3) | (uint32_t)dt;
    uint64_t src_layout = in_stride[in_stride.rank() - 1] * element_scalar_count_v<TInElem> * to_tdma_layout_expand<TInElem>();
    in_stride.rank() > 1 ? (src_layout |= in_stride[in_stride.rank() - 2] << 16) : (src_layout |= 1L << 16);
    in_stride.rank() > 2 ? (src_layout |= in_stride[in_stride.rank() - 3] << 32) : (src_layout |= 1L << 32);
    in_stride.rank() > 3 ? (src_layout |= in_stride[in_stride.rank() - 4] << 48) : (src_layout |= 1L << 48);

    uint64_t dst_layout = out_stride[out_stride.rank() - 1] * element_scalar_count_v<TInElem> * to_tdma_layout_expand<TInElem>();
    out_stride.rank() > 1 ? (dst_layout |= out_stride[out_stride.rank() - 2] << 16) : (dst_layout |= 1L << 16);
    out_stride.rank() > 2 ? (dst_layout |= out_stride[out_stride.rank() - 3] << 32) : (dst_layout |= 1L << 32);
    out_stride.rank() > 3 ? (dst_layout |= out_stride[out_stride.rank() - 4] << 48) : (dst_layout |= 1L << 48);

    // box_coordinates_off: no need
    uint64_t channel_control = 0b00;
    uint64_t sync_control = 0x0;

    uint64_t vhm_shape = shape[shape.rank() - 1] * element_scalar_count_v<TInElem> * to_tdma_layout_expand<TInElem>();
    shape.rank() > 1 ? (vhm_shape |= shape[shape.rank() - 2] << 16) : (vhm_shape |= 1L << 16);
    shape.rank() > 2 ? (vhm_shape |= shape[shape.rank() - 3] << 32) : (vhm_shape |= 1L << 32);
    shape.rank() > 3 ? (vhm_shape |= shape[shape.rank() - 4] << 48) : (vhm_shape |= 1L << 48);
    uint64_t vhm_shape_n = shape.rank() > 4 ? shape[shape.rank() - 5] : 1L;
    // dm_shape: no need
    // src_addr: 48bits
    // dst_addr: 48bits
    // start_coordinate: no need
    // start_coord_N: no need
    // boxsize: no need
    // im2col_offset: no need
    // pad_value: no need
    // traversal_stride: no need
    uint64_t config_finish = 0b1;
#ifdef __clang__
    tdma_write(0x100, mem_format_type);
    tdma_write(0x108, src_layout);
    tdma_write(0x110, dst_layout);
    tdma_write(0x120, channel_control);
    tdma_write(0x128, sync_control);
    tdma_write(0x180, vhm_shape);
    tdma_write(0x188, vhm_shape_n);
    tdma_write(0x198, (uint64_t)src_addr);
    tdma_write(0x1a0, (uint64_t)dst_addr);
    tdma_write(0x1d8, config_finish);
#else
    asm volatile("tdma_write_cfg 0x100, %0\n\t" : : "r"(mem_format_type));
    asm volatile("tdma_write_cfg 0x108, %0\n\t" : : "r"(src_layout));
    asm volatile("tdma_write_cfg 0x110, %0\n\t" : : "r"(dst_layout));
    asm volatile("tdma_write_cfg 0x120, %0\n\t" : : "r"(channel_control));
    asm volatile("tdma_write_cfg 0x128, %0\n\t" : : "r"(sync_control));
    asm volatile("tdma_write_cfg 0x160, %0\n\t" : : "r"(vhm_shape));
    asm volatile("tdma_write_cfg 0x168, %0\n\t" : : "r"(vhm_shape_n));
    asm volatile("tdma_write_cfg 0x178, %0\n\t" : : "r"(src_addr));
    asm volatile("tdma_write_cfg 0x180, %0\n\t" : : "r"(dst_addr));
    asm volatile("tdma_write_cfg 0x1b8, %0\n\t" : : "r"(config_finish));
#endif
}

template <tdma_mode mode, tdma_dtype_t dt, class TElem, class Shape>
inline void tdma_memset(Shape shape, char *addr, TElem value)
{
    uint64_t mem_format_type = ((uint32_t)mode << 7) | (uint32_t)dt;
    
    // box_coordinates_off: no need
    uint64_t channel_control = 0b00;
    uint64_t sync_control = 0x0;
    uint64_t dm_shape = shape[shape.rank() - 1] * element_scalar_count_v<TElem> * to_tdma_layout_expand<TElem>();
    shape.rank() > 1 ? (dm_shape |= shape[shape.rank() - 2] << 16) : (dm_shape |= 1L << 16);
    // FIXME: support other value
    uint64_t pad_value = 0x0;
    // src_addr: 48bits
    // dst_addr: 48bits
    // start_coordinate: no need
    // start_coord_N: no need
    // boxsize: no need
    // im2col_offset: no need
    // traversal_stride: no need
    uint64_t config_finish = 0b1;
    tdma_write(0x100, mem_format_type);
    tdma_write(0x120, channel_control);
    tdma_write(0x128, sync_control);
    tdma_write(0x190, dm_shape);
    tdma_write(0x1a0, (uint64_t)addr);
    tdma_write(0x1c8, pad_value);
    tdma_write(0x1d8, config_finish);
}

inline void tdma_wait()
{
    tdma_read(0x8);
}

template <tdma_mode mode, mem_format src_format, mem_format dst_format, tdma_dtype_t dt, class OutShape, class OutStride>
inline void tdma_dm2vhm(OutShape shape, OutStride stride, const char *src_addr, char *dst_addr)
{
    static_assert(src_format == dst_format || (src_format == mem_format::ncdwhcx && dst_format == mem_format::ndhwc), "Not supported dm->vhm format");
    mem_type src_type = mem_type::dm;
    mem_type dst_type = mem_type::vhm;

    uint64_t mem_format_type = ((uint32_t)mode << 7) | ((uint32_t)src_format << 6) | ((uint32_t)dst_format << 5) | ((uint32_t)src_type << 4) | ((uint32_t)dst_type << 3) | (uint32_t)dt;
    // src_layout: no need
    uint64_t dst_layout = stride[stride.rank() - 1];
    stride.rank() > 1 ? (dst_layout |= stride[stride.rank() - 2] << 16) : (dst_layout |= 1L << 16);
    stride.rank() > 2 ? (dst_layout |= stride[stride.rank() - 3] << 32) : (dst_layout |= 1L << 32);
    stride.rank() > 3 ? (dst_layout |= stride[stride.rank() - 4] << 48) : (dst_layout |= 1L << 48);
    // box_coordinates_off: no need
    uint64_t channel_control = 0b00;
    uint64_t sync_control = 0x0;
    uint64_t vhm_shape = (1ULL << 48) | (1ULL << 32) | (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    uint64_t vhm_shape_n = 0x1;
    // dm_shape: no need
    // src_addr: 48bits
    // dst_addr: 48bits
    uint64_t start_coordinate = 0x0;
    uint64_t start_coord_N = 0x0;
    uint64_t boxsize = (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    // im2col_offset: no need
    // pad_value: no need;
    uint64_t traversal_stride = (1ULL << 8) | (1ULL << 4) | 1;
    uint64_t config_finish = 0b1;

#ifdef __clang__
    tdma_write(0x100, mem_format_type);
    tdma_write(0x110, dst_layout);
    tdma_write(0x120, channel_control);
    tdma_write(0x128, sync_control);
    tdma_write(0x180, vhm_shape);
    tdma_write(0x188, vhm_shape_n);
    tdma_write(0x198, (uint64_t)src_addr);
    tdma_write(0x1a0, (uint64_t)dst_addr);
    tdma_write(0x1a8, start_coordinate);
    tdma_write(0x1b0, start_coord_N);
    tdma_write(0x1b8, boxsize);
    tdma_write(0x1d0, traversal_stride);
    tdma_write(0x1d8, config_finish);
#else
    asm volatile("tdma_write_cfg 0x100, %0\n\t" : : "r"(mem_format_type));
    asm volatile("tdma_write_cfg 0x110, %0\n\t" : : "r"(dst_layout));
    asm volatile("tdma_write_cfg 0x120, %0\n\t" : : "r"(channel_control));
    asm volatile("tdma_write_cfg 0x128, %0\n\t" : : "r"(sync_control));
    asm volatile("tdma_write_cfg 0x180, %0\n\t" : : "r"(vhm_shape));
    asm volatile("tdma_write_cfg 0x188, %0\n\t" : : "r"(vhm_shape_n));
    asm volatile("tdma_write_cfg 0x198, %0\n\t" : : "r"(src_addr));
    asm volatile("tdma_write_cfg 0x1a0, %0\n\t" : : "r"(dst_addr));
    asm volatile("tdma_write_cfg 0x1a8, %0\n\t" : : "r"(start_coordinate));
    asm volatile("tdma_write_cfg 0x1b0, %0\n\t" : : "r"(start_coord_N));
    asm volatile("tdma_write_cfg 0x1b8, %0\n\t" : : "r"(boxsize));
    asm volatile("tdma_write_cfg 0x1d0, %0\n\t" : : "r"(traversal_stride));
    asm volatile("tdma_write_cfg 0x1d8, %0\n\t" : : "r"(config_finish));
#endif
}

template <tdma_mode mode, mem_format src_format, mem_format dst_format, tdma_dtype_t dt, class OutShape, class OutStride>
inline void tdma_dm2vhm_weired(OutShape shape, OutStride stride, char *src_addr, char *dst_addr)
{
    static_assert(src_format == dst_format || (src_format == mem_format::ncdwhcx && dst_format == mem_format::ndhwc), "Not supported dm->vhm format");
    mem_type src_type = mem_type::dm;
    mem_type dst_type = mem_type::vhm;

    uint64_t mem_format_type = ((uint32_t)mode << 7) | ((uint32_t)src_format << 6) | ((uint32_t)dst_format << 5) | ((uint32_t)src_type << 4) | ((uint32_t)dst_type << 3) | (uint32_t)dt;
    // src_layout: no need
    uint64_t dst_layout = (1ULL << 48) | (1ULL << 32) | (stride[stride.rank() - 2] << 16) | stride[stride.rank() - 1];
    // box_coordinates_off: no need
    uint64_t channel_control = 0b00;
    uint64_t sync_control = 0x0;
    uint64_t vhm_shape = (1ULL << 48) | (1ULL << 32) | (stride[stride.rank() - 2] << 16) | stride[stride.rank() - 1];
    uint64_t vhm_shape_n = 0x1;
    // dm_shape: no need
    // src_addr: 48bits
    // dst_addr: 48bits
    uint64_t start_coordinate = 0x0;
    uint64_t start_coord_N = 0x0;
    uint64_t boxsize = (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    // im2col_offset: no need
    // pad_value: no need;
    uint64_t traversal_stride = (1ULL << 8) | (1ULL << 4) | 1;
    uint64_t config_finish = 0b1;

#ifdef __clang__
    tdma_write(0x100, mem_format_type);
    tdma_write(0x110, dst_layout);
    tdma_write(0x120, channel_control);
    tdma_write(0x128, sync_control);
    tdma_write(0x180, vhm_shape);
    tdma_write(0x188, vhm_shape_n);
    tdma_write(0x198, (uint64_t)src_addr);
    tdma_write(0x1a0, (uint64_t)dst_addr);
    tdma_write(0x1a8, start_coordinate);
    tdma_write(0x1b0, start_coord_N);
    tdma_write(0x1b8, boxsize);
    tdma_write(0x1d0, traversal_stride);
    tdma_write(0x1d8, config_finish);
#else
    asm volatile("tdma_write_cfg 0x100, %0\n\t" : : "r"(mem_format_type));
    asm volatile("tdma_write_cfg 0x110, %0\n\t" : : "r"(dst_layout));
    asm volatile("tdma_write_cfg 0x120, %0\n\t" : : "r"(channel_control));
    asm volatile("tdma_write_cfg 0x128, %0\n\t" : : "r"(sync_control));
    asm volatile("tdma_write_cfg 0x180, %0\n\t" : : "r"(vhm_shape));
    asm volatile("tdma_write_cfg 0x188, %0\n\t" : : "r"(vhm_shape_n));
    asm volatile("tdma_write_cfg 0x198, %0\n\t" : : "r"(src_addr));
    asm volatile("tdma_write_cfg 0x1a0, %0\n\t" : : "r"(dst_addr));
    asm volatile("tdma_write_cfg 0x1a8, %0\n\t" : : "r"(start_coordinate));
    asm volatile("tdma_write_cfg 0x1b0, %0\n\t" : : "r"(start_coord_N));
    asm volatile("tdma_write_cfg 0x1b8, %0\n\t" : : "r"(boxsize));
    asm volatile("tdma_write_cfg 0x1d0, %0\n\t" : : "r"(traversal_stride));
    asm volatile("tdma_write_cfg 0x1d8, %0\n\t" : : "r"(config_finish));
    uint64_t status;
    asm volatile("tdma_read_cfg %0, 0x1c0\n\t" : "=r"(status) :);
#endif
}

template <tdma_mode mode, mem_format src_format, mem_format dst_format,
    tdma_dtype_t dt, class InShape>
inline void tdma_dm2dm_async(InShape shape, char *src_addr,
    char *dst_addr)
{
    static_assert(src_format == dst_format || (src_format == mem_format::ndhwc && dst_format == mem_format::ncdwhcx),
        "Not supported vhm->dm format");
    mem_type src_type = mem_type::dm;
    mem_type dst_type = mem_type::dm;

    uint64_t mem_format_type = ((uint32_t)mode << 7) | ((uint32_t)src_format << 6) | ((uint32_t)dst_format << 5) | ((uint32_t)src_type << 4) | ((uint32_t)dst_type << 3) | (uint32_t)dt;
    // src_layout: no need
    // dst_layout: no need
    // box_coordinates_off: no need
    uint64_t channel_control = 0b00;
    uint64_t sync_control = 0x0;
    uint64_t vhm_shape = (1ULL << 48) | (1ULL << 32) | (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    uint64_t vhm_shape_n = 0x1;
    uint64_t dm_shape = (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    // src_addr: 48bits
    // dst_addr: 48bits
    uint64_t start_coordinate = 0x0;
    uint64_t start_coord_N = 0x0;
    uint64_t boxsize = (shape[shape.rank() - 2] << 16) | shape[shape.rank() - 1];
    // im2col_offset: no need
    uint64_t pad_value = 0x0;
    uint64_t traversal_stride = (1ULL << 8) | (1ULL << 4) | 1;
    uint64_t config_finish = 0b1;

#ifdef __clang__
    tdma_write(0x100, mem_format_type);
    tdma_write(0x120, channel_control);
    tdma_write(0x128, sync_control);
    tdma_write(0x180, vhm_shape);
    tdma_write(0x188, vhm_shape_n);
    tdma_write(0x190, dm_shape);
    tdma_write(0x198, (uint64_t)src_addr);
    tdma_write(0x1a0, (uint64_t)dst_addr);
    tdma_write(0x1a8, start_coordinate);
    tdma_write(0x1b0, start_coord_N);
    tdma_write(0x1b8, boxsize);
    tdma_write(0x1c8, pad_value);
    tdma_write(0x1d0, traversal_stride);
    tdma_write(0x1d8, config_finish);
#else
    asm volatile("tdma_write_cfg 0x100, %0\n\t" : : "r"(mem_format_type));
    asm volatile("tdma_write_cfg 0x108, %0\n\t" : : "r"(src_layout));
    asm volatile("tdma_write_cfg 0x120, %0\n\t" : : "r"(channel_control));
    asm volatile("tdma_write_cfg 0x128, %0\n\t" : : "r"(sync_control));
    asm volatile("tdma_write_cfg 0x180, %0\n\t" : : "r"(vhm_shape));
    asm volatile("tdma_write_cfg 0x188, %0\n\t" : : "r"(vhm_shape_n));
    asm volatile("tdma_write_cfg 0x190, %0\n\t" : : "r"(dm_shape));
    asm volatile("tdma_write_cfg 0x198, %0\n\t" : : "r"(src_addr));
    asm volatile("tdma_write_cfg 0x1a0, %0\n\t" : : "r"(dst_addr));
    asm volatile("tdma_write_cfg 0x1a8, %0\n\t" : : "r"(start_coordinate));
    asm volatile("tdma_write_cfg 0x1b0, %0\n\t" : : "r"(start_coord_N));
    asm volatile("tdma_write_cfg 0x1b8, %0\n\t" : : "r"(boxsize));
    asm volatile("tdma_write_cfg 0x1c8, %0\n\t" : : "r"(pad_value));
    asm volatile("tdma_write_cfg 0x1d0, %0\n\t" : : "r"(traversal_stride));
    asm volatile("tdma_write_cfg 0x1d8, %0\n\t" : : "r"(config_finish));
#endif
}

template <tmma_dtype_t type>
static uint32_t get_size_bytes()
{
    uint32_t size = 0;
    switch (type)
    {
    case tmma_dtype_t::int8:
    case tmma_dtype_t::fp8_1_5_2:
    case tmma_dtype_t::fp8_1_4_3:
        size = 1;
        break;
    case tmma_dtype_t::fp16:
    case tmma_dtype_t::bf16:
        size = 2;
        break;
    case tmma_dtype_t::int32:
    case tmma_dtype_t::fp32:
        size = 4;
        break;
    }
    return size;
}

template <class T>
static constexpr tmma_dtype_t to_tmma_type()
{
    if constexpr (std::is_same_v<T, int8_t>)
    {
        return tmma_dtype_t::int8;
    }
    else if constexpr (std::is_same_v<T, nncase::float_e5m2_t>)
    {
        return tmma_dtype_t::fp8_1_5_2;
    }
    else if constexpr (std::is_same_v<T, nncase::float_e4m3_t>)
    {
        return tmma_dtype_t::fp8_1_4_3;
    }
    else if constexpr (std::is_same_v<T, nncase::half>)
    {
        return tmma_dtype_t::fp16;
    }
    else if constexpr (std::is_same_v<T, nncase::bfloat16>)
    {
        return tmma_dtype_t::bf16;
    }
    else if constexpr (std::is_same_v<T, int32_t>)
    {
        return tmma_dtype_t::int32;
    }
    else if constexpr (std::is_same_v<T, float>)
    {
        return tmma_dtype_t::fp32;
    }
    else
    {
        static_assert(false, "type not impl!");
    }
}

template <class T>
static constexpr tdma_dtype_t to_tdma_reduce_type()
{
    using TE = nncase::ntt::element_or_scalar_t<T>;

    if constexpr (std::is_same_v<TE, nncase::half>)
    {
        return tdma_dtype_t::rd_fp16;
    }
    else if constexpr (std::is_same_v<TE, nncase::bfloat16>)
    {
        return tdma_dtype_t::rd_bf16;
    }
    else if constexpr (std::is_same_v<TE, int32_t> || std::is_same_v<TE, float>)
    {
        return tdma_dtype_t::rd_fp32;
    }
    else
    {
        static_assert(false, "type not impl!");
    }
}
}