/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#ifdef SYS_MODE
#include "isa.h"
#include <nncase/ntt/apply.h>
#include <nncase/ntt/utility.h>

namespace nncase::ntt
{
namespace detail
{
    template <class TLast, class TCur, class TDim>
    class recover_dim_inner_impl
    {
    public:
        constexpr auto operator()(const TLast &pstride, const TCur &stride,
            const TDim &dim) noexcept
        {
            if constexpr (FixedDimension<TLast>)
            {
                return ntt::where(pstride == dim_zero, dim, pstride / stride);
            }
            else
            {
                return pstride == 0 ? dim : pstride / stride;
            }
        }
    };

    template <class TCur, class TDim>
    class recover_dim_inner_impl<fixed_dim<0>, TCur, TDim>
    {
    public:
        constexpr auto operator()([[maybe_unused]] const fixed_dim<0> &pstride,
            [[maybe_unused]] const TCur &stride,
            const TDim &dim) noexcept
        {
            return dim;
        }
    };

    template <class TLast, class TCur, class TDim>
    class recover_dim_impl
    {
    public:
        constexpr auto operator()(const TLast &pstride, const TCur &stride,
            const TDim &dim) noexcept
        {
            if constexpr (FixedDimension<TCur>)
            {
                return ntt::where(stride == dim_zero, dim_one,
                    recover_dim_inner_impl<TLast, TCur, TDim> {}(
                        pstride, stride, dim));
            }
            else
            {
                return stride == 0 ? 1 : (pstride == 0 ? dim : pstride / stride);
            }
        }
    };

    template <Dimension TLast, Dimension TCur, Dimension TDim>
    constexpr auto recover_dim(const TLast &pstride, const TCur &stride,
        const TDim &dim) noexcept
    {
        return recover_dim_impl<TLast, TCur, TDim> {}(pstride, stride, dim);
    }

    template <Tensor TTensor>
    struct tensor_zero_impl<TTensor, true>
    {
        constexpr void operator()(TTensor &tensor) noexcept
        {
            constexpr auto rank = TTensor::shape_type::rank();
            using TElem = typename TTensor::value_type;
            const auto layout = recover_layout(tensor.shape(), tensor.strides());
            constexpr auto tdma_type = to_tdma_type<TElem>();

            if (((uintptr_t)tensor.elements().data()) % 128 == 0 && tensor.shape().back() * to_tdma_size_expand<TElem>() % 128 == 0 && ((layout.back() * element_scalar_count_v<TElem> * to_tdma_layout_expand<TElem>()) < 32768))
            {
                if constexpr (rank >= 2)
                {
                    auto domains = tensor.shape().template slice<0, rank - 2>();
                    auto strides = tensor.strides().template slice<0, rank - 2>();
                    auto shape = layout.template slice<rank - 2, rank>();

                    ntt::apply(domains, [&](auto index)
                        {
                            auto ptr = tensor.elements().data() + linear_offset(index, strides);
                            tdma_memset<tdma_mode::memset, tdma_type, TElem>(shape, reinterpret_cast<char *>(ptr), TElem{}); });
                }
                else
                {
                    tdma_memset<tdma_mode::memset, tdma_type, TElem>(layout, reinterpret_cast<char *>(tensor.elements().data()), TElem {});
                }
            }
            else
            {
                ntt::apply(tensor.shape(), [&](auto index)
                    { tensor(index) = {}; });
            }
        }
    };

    // template <Shape TShape, Strides TStrides>
    // constexpr auto recover_layout(const TShape &shape, const TStrides &strides)
    // {
    //     const auto recover_strides = strides.aggregate(
    //         empty_dims_alike_t<TStrides> {}, [&](auto result, auto dim, auto axis)
    //         {
    //             if constexpr (axis == 0) {
    //                 return result.append(dim);
    //             } else if constexpr (FixedDimension<decltype(dim)>) {
    //                 return result.append(
    //                     ntt::where(dim == dim_zero, result.back(), dim));
    //             } else {
    //                 return result.append(dim == 0 ? result.back() : dim);
    //             } });

    //     return recover_strides.aggregate(
    //         empty_dims_alike_t<TShape> {}, [&](auto result, auto stride, auto axis)
    //         {
    //             constexpr auto paxis = axis - 1_dim;
    //             const auto pstride = recover_strides[paxis];
    //             const auto dim = shape[axis];
    //             if constexpr (axis == 0) {
    //                 return result.append(dim);
    //             } else {
    //                 return result.append(detail::recover_dim(pstride, stride, dim));
    //             } });
    // }

    template <Shape TShape, Strides TStrides>
    constexpr auto recover_layout(const TShape &shape, const TStrides &strides)
    {
        if constexpr (TShape::rank() == 0 && TStrides::rank() == 0)
        {
            return make_shape(1);
        }
        else
        {
            // 1. strides[i] = strides[i] == 0 ? strides[i - 1] : strides[i]
            // 1.1 if last strides, return 1
            const auto strides1 = strides.aggregate(
                empty_dims_alike_t<TStrides> {},
                [&](auto result, auto stride, auto axis)
                {
                    if constexpr (axis == TStrides::rank() - 1)
                    {
                        return result.append(dim_one);
                    }
                    else if constexpr (axis == 0)
                    {
                        return make_strides(stride);
                    }
                    else
                    {
                        auto new_stride = ntt::where(stride == dim_zero, result.back(), stride);
                        return result.append(new_stride);
                    }
                });

            // 2. strides[i] = strides[i] == 0 ? strides[i + 1] * shape[i + 1] :
            // strides[i]
            const auto strides2 = strides1.reverse().aggregate(
                empty_dims_alike_t<TStrides> {},
                [&](auto result, auto stride, auto axis)
                {
                    if constexpr (axis == 0)
                    {
                        return make_strides(stride);
                    }
                    else
                    {
                        auto new_stride = ntt::where(
                            stride == dim_zero,
                            result.front() * shape[TShape::rank() - axis],
                            stride);
                        return result.prepend(new_stride);
                    }
                });

            return generate_shape<TShape::rank()>([&](auto axis)
                {
        if constexpr (axis == 0) {
            return shape.front();
        } else {
            return strides2[axis - 1_dim] / strides2[axis];
        } });
        }
    }

    template <Shape TShape>
    constexpr auto fixup_shape(const TShape &shape)
    {
        if constexpr (TShape::rank() == 0)
        {
            return make_shape(1);
        }
        else
        {
            return shape;
        }
    }

    template <bool load, typename TA, typename TB>
    struct copy_impl_matmul;
    template <bool load, Tensor TA, Tensor TB>
    struct copy_impl_matmul<load, TA, TB>
    {
        constexpr void operator()(const TA &input, TB &output)
        {
            auto input_shape = input.shape();

            using TInElem = typename TA::element_type;
            using TOutElem = typename std::decay_t<TB>::element_type;
            constexpr auto a_shape = typename TInElem::shape_type {};
            constexpr auto b_shape = typename TOutElem::shape_type {};
            constexpr auto a_strides = typename TInElem::strides_type {};
            constexpr auto b_strides = typename TOutElem::strides_type {};
            constexpr auto in_type = to_tdma_type<typename TInElem::element_type>();

            apply(input_shape, [&](auto index)
                {
                    if constexpr (load)
                    {
                        tdma_vhm2dm<tdma_mode::tile, mem_format::ndhwc, mem_format::ncdwhcx, in_type>(a_shape, a_shape, reinterpret_cast<const char *>(&input(index)), reinterpret_cast<char *>(&output(index)));
                    }
                    else
                    {
                        tdma_dm2vhm<tdma_mode::tile, mem_format::ncdwhcx, mem_format::ndhwc, in_type>(b_shape, b_shape, reinterpret_cast<const char *>(&input(index)), reinterpret_cast<char *>(&output(index)));
                    } });
        }
    };

    template <class TIn, class TOut>
    class copy_impl<TIn, TOut, true>
    {
    private:
        template <class TShape>
        constexpr void do_copy_impl(const TIn &input, TOut &output, const TShape &vhm_shape)
        {
            constexpr auto rank = TShape::rank();
            using TInElem = typename TIn::value_type;
            const auto src_layout = recover_layout(input.shape(), input.strides());
            const auto dst_layout = recover_layout(output.shape(), output.strides());
            constexpr auto tdma_type = to_tdma_type<TInElem>();

            if (vhm_shape.length() > 0 && ((uintptr_t)input.elements().data()) % 128 == 0 && ((uintptr_t)output.elements().data()) % 128 == 0 && vhm_shape.back() * to_tdma_size_expand<TInElem>() % 128 == 0 && ((src_layout.back() * element_scalar_count_v<TInElem> * to_tdma_layout_expand<TInElem>()) < 32768) && ((dst_layout.back() * element_scalar_count_v<TInElem> * to_tdma_layout_expand<TInElem>()) < 32768) && input.strides()[rank - 1] <= 1 && output.strides()[rank - 1] <= 1)
            {
                tdma_vhm2vhm<tdma_mode::tile, mem_format::ndhwc, mem_format::ndhwc, tdma_type, TInElem>(vhm_shape, src_layout, dst_layout, reinterpret_cast<const char *>(input.elements().data()), reinterpret_cast<char *>(output.elements().data()));
            }
            else
            {
                ntt::unary<ops::copy>(input, output);
            }
        }

    public:
        void operator()(const TIn &input, TOut &output)
        {
            do_copy_impl(input, output, fixup_shape(input.shape()));
        }
    };

    template<class NOUSE>
    class copy_wait_impl<NOUSE, true> {
    public:
        void operator()() { tdma_wait(); }
    };

    template <class T, Dimensions Index, Shape UShape>
    constexpr auto tdma_view(T &tensor, const Index &index, const UShape &shape) noexcept
    {
        using TElem = typename std::decay_t<T>::element_type;
        auto pos_index = positive_index(index, tensor.shape());
        auto offset = linear_offset(pos_index, tensor.strides());
        auto begin = tensor.elements().data() + offset;
        return make_tensor_view_from_address<TElem>(
            begin, shape, tensor.strides());
    }

    // template <bool load, bool interleave = true, class TA, class TB>
    // void tensor_copy_tdma(const TA &input, TB &&output) noexcept
    // {
    //     if constexpr (interleave)
    //     {
    //         copy_detail::copy_impl_matmul<load, TA, std::decay_t<TB>> impl;
    //         impl(input, output);
    //     }
    //     else
    //     {
    //         copy_detail::copy_impl_vector<load, TA, std::decay_t<TB>> impl;
    //         impl(input, output);
    //     }
    // }
}
} // namespace nncase::ntt
#endif