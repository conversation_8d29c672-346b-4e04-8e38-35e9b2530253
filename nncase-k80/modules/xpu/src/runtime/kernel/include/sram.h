#pragma once
#include <nncase/runtime/xpu/hardware_def.h>
#include <cstdint>
#include <cstdio>
#ifdef SYS_MODE
#include <nano_kernel_lib.h>
extern __shared_point__ unsigned long sram_va_size;
#endif

#ifdef SYS_MODE
__device__ [[maybe_unused]] static uint8_t *sram[NANOS];
static const unsigned long sram_size_per_block = 2 * sram_va_size;
static const int sram_size_per_thread = sram_va_size / THREADS;
#else
[[maybe_unused]] static uint8_t *sram[NANOS];
static const unsigned long sram_size_per_block = 1 * 1024 * 1024U;
static const int sram_size_per_thread = sram_size_per_block / THREADS;
#endif

