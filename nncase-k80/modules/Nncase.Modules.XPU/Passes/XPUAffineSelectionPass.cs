﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using Google.OrTools.ConstraintSolver;
using Microsoft.Extensions.DependencyInjection;
using NetFabric.Hyperlinq;
using Nncase.Diagnostics;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.Passes;
using Nncase.Passes.Analysis;
using Nncase.Passes.Mutators;
using Nncase.Passes.Transforms;
using Nncase.Targets;
using Nncase.TIR;

namespace Nncase.Passes;

public sealed partial class XPUAffineSelectionPass : NTTAffineSelectionPass
{
    public XPUAffineSelectionPass(CompileOptions compileOptions, string moduleKind = XPUTarget.Kind)
        : base(compileOptions, moduleKind)
    {
    }

    protected override Expr SelectCall(Call call, Expr output)
    {
        return base.SelectCall(call, output);
    }
}
