﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.CodeGen.NTT;
using Nncase.IR;
using Nncase.IR.NN;
using Nncase.TIR;

namespace Nncase.CodeGen.XPU;

/// <summary>
/// convert the type/op to c name.
/// </summary>
internal static class CSourceExtensions
{
    private static readonly Dictionary<PrimType, string> _primTypeToISA = new()
    {
        { DataTypes.Float32, "isa::fp32" },
    };

    public static string ToISA(this PrimType primType) =>
        _primTypeToISA[primType];

    public static string ToISA(this DataType dataType) => dataType switch
    {
        PrimType ptype => ptype.ToISA(),
        PointerType ptrType => ptrType.ElemType.ToISA() + " *",
        _ => throw new NotSupportedException(dataType.ToString()),
    };

    public static string ToC(this MemoryLocation location) => location switch
    {
        MemoryLocation.Output or MemoryLocation.Input or MemoryLocation.Rdata => "loc_t::device",
        MemoryLocation.Data => "loc_t::local",
        _ => throw new NotSupportedException(location.ToString()),
    };

    public static string ToCType(this IRArray<IR.NN.PagedKVCacheDimKind> arr) => $"fixed_shape_t<{string.Join(',', arr.Select(e => "(dim_t)" + e.ToC()))}>";

    public static string ToCType(this IRArray<IR.NN.AttentionCacheKind> arr) => $"fixed_shape_t<{string.Join(',', arr.Select(e => "(dim_t)" + e.ToC()))}>";

    public static string ToCType(this IRArray<IR.NN.AttentionDimKind> arr) => $"fixed_shape_t<{string.Join(',', arr.Select(e => "(dim_t)" + e.ToC()))}>";

    public static string ToCType(this IRArray<int> arr) => $"fixed_shape_t<{string.Join(',', arr)}>";

    public static string ToCType(this IEnumerable<SBP> arr)
    {
        var toc = (SBP sbp) => sbp switch
        {
            SBPSplit s => $"shard_policy::split<{string.Join(", ", s.Axes)}>",
            SBPBroadCast b => $"shard_policy::broadcast",
            _ => throw new ArgumentOutOfRangeException(nameof(arr)),
        };
        return $"{string.Join(',', arr.Select(toc))}";
    }

    public static string ToCType(this IPagedAttentionConfig config)
    {
        return $"caching::paged_attention_config<{config.NumLayers}, {config.NumKVHeads}, {config.HeadDim}, {config.KVPrimType.ToC()}, {config.BlockSize}, {config.CacheLayout.ToCType()}, {config.BlockLayout.ToCType()}, {config.VectorizedAxes.ToCType()}, {config.Lanes.ToCType()}, {config.ShardingAxes.ToCType()}, {config.AxisPolicies.OfType<SBP>().ToCType()}>";
    }

    public static string[] ToSlicing(this IEnumerable<string> dims, string[] begins, IRArray<SBP> ndsbp, Placement placement)
    {
        var hstrides = TensorUtilities.GetDefaultStrides(placement.Hierarchy.ToArray());
        var splitHierarchy = Enumerable.Range(0, begins.Length).Select(_ => new List<int>()).ToArray();
        var splits = Enumerable.Range(0, begins.Length).Select(_ => new List<int>()).ToArray();
        foreach (var (sbp, i) in ndsbp.Select((s, i) => (s, i)))
        {
            if (sbp is SBPSplit split)
            {
                splits[i] = split.Axes.ToList();
                splitHierarchy[i] = placement.Hierarchy.Select((h, i) => split.Axes.Contains(i) ? h : 1).ToList();
            }
        }

        foreach (var splist in splits)
        {
            splist.Sort((a, b) => -a.CompareTo(b));
        }

        for (int i = 0; i < begins.Length; i++)
        {
            var sp = splits[i];
            if (sp.Count > 0)
            {
                var dimi = dims.ElementAt(i);
                if (dimi.IndexOf('?', System.StringComparison.CurrentCulture) is int s && dimi.IndexOf(':', System.StringComparison.CurrentCulture) is int e && s != -1 && e != -1)
                {
                    dimi = dimi[(s + 1)..e].Trim();
                }

                begins[i] += " + " + sp.Skip(1).Aggregate($"{placement.Name[sp[0]]}id()", (acc, p) => $"({acc} + {TensorUtilities.GetProduct(splitHierarchy[i].ToArray().AsSpan()[(p + 1)..])} * {placement.Name[p]}id())") + $" * {dimi}";
            }
        }

        return [$"ntt::make_ranked_shape({string.Join(',', begins)})", $"ntt::fixed_shape<{string.Join(",", dims.Select(d => d.ToString()))}>{{}}"];
    }

    public static string[] ToSlicing(this IEnumerable<string> dims, IRArray<SBP> ndsbp, Placement placement) => ToSlicing(dims, Enumerable.Repeat("0", dims.Count()).ToArray(), ndsbp, placement);

    public static string ToC(this BinaryOp binaryOp) => binaryOp switch
    {
        BinaryOp.Add => "+",
        BinaryOp.Sub => "-",
        BinaryOp.Mul => "*",
        BinaryOp.Div => "/",
        _ => throw new NotSupportedException(binaryOp.ToString()),
    };
}
