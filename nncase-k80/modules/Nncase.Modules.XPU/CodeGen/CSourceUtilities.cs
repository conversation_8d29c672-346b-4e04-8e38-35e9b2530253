﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System.Globalization;
using Nncase.CodeGen.NTT;
using Nncase.Diagnostics;
using Nncase.IR.Math;
using Nncase.IR.Shapes;

namespace Nncase.CodeGen.XPU;

public enum RuntimeMode
{
    Local = 0,
    UserMode = 1,
    SystemMode = 2,
}

public static class CSourceUtilities
{
    public static string ConvertBinary(Binary binary, CSymbol[] arguments)
    {
        var lhs = arguments[Binary.Lhs.Index].Name;
        var rhs = arguments[Binary.Rhs.Index].Name;
        var type = arguments[0].Type;
        type = type.EndsWith("_t") ? type[..^2] : type;
        string str;
        switch (binary.BinaryOp)
        {
            case BinaryOp.Add or BinaryOp.Sub or BinaryOp.Mul or BinaryOp.Div:
                str = $"({lhs} {binary.BinaryOp.ToC()} {rhs})";
                break;
            case BinaryOp.Min or BinaryOp.Max:
                str = $"nncase_mt->{type}_binary_{binary.BinaryOp.ToString().ToLower(CultureInfo.CurrentCulture)}({lhs}, {rhs})";
                break;
            default:
                throw new NotSupportedException();
        }

        return str;
    }

    public static bool TryGetDivRem(string dim, out int div, out int rem)
    {
        div = 0;
        rem = 0;
        if (dim.IndexOf('?', System.StringComparison.CurrentCulture) is int s && dim.IndexOf(':', System.StringComparison.CurrentCulture) is int e && s != -1 && e != -1)
        {
            div = int.Parse(dim[(s + 1)..e].Trim());
            rem = int.Parse(dim[(e + 1)..^1].Trim());
            return true;
        }

        return false;
    }

    public static string ConvertUnary(Unary op, CSymbol[] arguments)
    {
        var input = arguments[Unary.Input.Index].Name;
        string str;
        switch (op.UnaryOp)
        {
            case UnaryOp.Abs:
                str = $"std::abs({input})";
                break;
            default:
                str = $"nncase_mt->{arguments[0].Type}_{nameof(Unary).ToLower(CultureInfo.CurrentCulture)}_{op.UnaryOp.ToString().ToLower(CultureInfo.CurrentCulture)}({input})";
                break;
        }

        return str;
    }

    public static string ConvertCompare(Compare op, CSymbol[] arguments)
    {
        var lhs = arguments[Compare.Lhs.Index].Name;
        var rhs = arguments[Compare.Rhs.Index].Name;
        string str = $"({lhs} {op.CompareOp.ToC()} {rhs})";
        return str;
    }

    public static string ConvertSelect(Select s, CSymbol[] arguments)
    {
        var p = arguments[Select.Predicate.Index].Name;
        var lhs = arguments[Select.TrueValue.Index].Name;
        var rhs = arguments[Select.FalseValue.Index].Name;
        string str = $"({p} ? {lhs} : {rhs})";
        return str;
    }

    public static string ConvertClamp(Clamp op, CSymbol[] arguments)
    {
        var input = arguments[Clamp.Input.Index].Name;
        var min = arguments[Clamp.Min.Index].Name;
        var max = arguments[Clamp.Max.Index].Name;
        string str = $"std::clamp({input}, {min}, {max})";
        return str;
    }

    public static string ConvertAsTensor(AsTensor op, CSymbol[] arguments)
    {
        var input = arguments[0].Name;
        return $"ntt::as_tensor({input})";
    }

    public static RuntimeMode GetRuntimeMode()
    {
        var sysMod = Environment.GetEnvironmentVariable("SYS_MODE") switch
        {
            var e when string.IsNullOrEmpty(e) => -1,
            var e when int.TryParse(e, out var s) => s,
            _ => throw new NotSupportedException("SYS_MODE Must Be A Number!"),
        };

        return sysMod switch
        {
            var s when s == -1 => RuntimeMode.Local,
            var s when s == 0 => RuntimeMode.UserMode,
            _ => RuntimeMode.SystemMode,
        };
    }

    public static int DumpInst()
    {
        return Environment.GetEnvironmentVariable("DUMP_INST") switch
        {
            var e when string.IsNullOrEmpty(e) => 0,
            var e when int.TryParse(e, out var s) => s,
            _ => 0,
        };
    }
}
