﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nncase.Diagnostics;

namespace Nncase.CodeGen.XPU;

internal sealed class LinkableModule : ILinkableModule
{
    private const int _textAlignment = 8;

    private readonly Stream _desc;

    private readonly Stream _rdata;

    private readonly IReadOnlyList<Stream> _threadLocalRdatas;

    private readonly IReadOnlyList<Stream> _blockLocalRdatas;

    private readonly IReadOnlyList<ILinkableFunction> _functions;

    private readonly CompileOptions _options;

    public LinkableModule(Stream desc, Stream rdata, IReadOnlyList<Stream> threadLocalRdatas, IReadOnlyList<Stream> blockLocalRdatas, IReadOnlyList<ILinkableFunction> functions, CompileOptions options)
    {
        _desc = desc;
        _rdata = rdata;
        _threadLocalRdatas = threadLocalRdatas;
        _blockLocalRdatas = blockLocalRdatas;
        _functions = functions;
        _options = options;
        PublicFunctions = _functions.OfType<LinkableKernelFunction>().ToArray();
    }

    public IReadOnlyList<ILinkableFunction> PublicFunctions { get; }

    public ILinkedModule Link(ILinkContext linkContext)
    {
        var kernelFuntions = _functions.Where(f => f is LinkableKernelFunction).Cast<LinkableKernelFunction>().ToArray();
        var deviceFuntions = _functions.Where(f => f is LinkableDeviceFunction).Cast<LinkableDeviceFunction>().ToArray();
        var lambdaFunctions = _functions.Where(f => f is LinkableLambdaFunction).Cast<LinkableLambdaFunction>().ToArray();

        var codegenDir = Path.Join(DumpScope.Current.Directory, "module");
        if (!Directory.Exists(codegenDir))
        {
            Directory.CreateDirectory(codegenDir);
        }

        using (var fs = File.Open(Path.Join(codegenDir, "topo_aware_runtime.h"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(kernelFuntions[0].FunctionCSource.TopoAwareRuntime);
            }
        }

#if false
        using (var fs = File.Open(Path.Join(codegenDir, "cluster_def.h"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(CSourceBuiltn.ClusterDef());
            }
        }
#endif

        using (var fs = File.Open(Path.Join(codegenDir, "thread_main.cpp"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(kernelFuntions[0].FunctionCSource.Main[0]); // include
                foreach (var item in kernelFuntions.Select(f => f.FunctionCSource.Main[1]))
                {
                    writer.WriteLine(item);
                }
            }
        }

        using (var fs = File.Open(Path.Join(codegenDir, "main.cpp"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode)
                {
                    writer.WriteLine(kernelFuntions[0].FunctionCSource.Main[2]); // just dummy main.
                }
                else
                {
                    writer.WriteLine(kernelFuntions[0].FunctionCSource.Main[0]);
                    foreach (var item in kernelFuntions.Select(f => f.FunctionCSource.Main[1]))
                    {
                        writer.WriteLine(item);
                    }

                    writer.WriteLine(kernelFuntions[0].FunctionCSource.Main[2]);

                    foreach (var item in kernelFuntions.Select(f => f.FunctionCSource.Main[3]))
                    {
                        writer.WriteLine(item);
                    }

                    writer.WriteLine(kernelFuntions[0].FunctionCSource.Main[4]);
                }
            }
        }

#if false
        using (var fs = File.Open(Path.Join(codegenDir, "shared.h"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(kernelFuntions[0].FunctionCSource.Shared);
            }
        }
#endif

        var kernel = kernelFuntions[0].FunctionCSource.Kernel[0] + "\n#include \"lambda_functions.h\"\n" +
        string.Join("\n", kernelFuntions.Select(f => f.FunctionCSource.DeviceFuncHeader).Aggregate((s1, s2) => s1.Concat(s2).ToArray()).Distinct().ToArray()) + "\n" +
        string.Join("\n", kernelFuntions.Select(f => f.FunctionCSource.Kernel[1]).ToArray());
        using (var fs = File.Open(Path.Join(codegenDir, "kernel.h"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(kernel);
            }
        }

        using (var fs = File.Open(Path.Join(codegenDir, "CMakeLists.txt"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                writer.Write(CSourceBuiltn.CMakeDef("module"));
            }
        }

        using (var fs = File.Open(Path.Join(codegenDir, "device_functions.h"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                foreach (var func in deviceFuntions)
                {
                    writer.Write(func.Header);
                }
            }
        }

        using (var fs = File.Open(Path.Join(codegenDir, "lambda_functions.h"), FileMode.Create))
        {
            using (var writer = new StreamWriter(fs))
            {
                foreach (var func in lambdaFunctions)
                {
                    writer.Write(func.Header);
                }
            }
        }

        var manager = new SectionManager();
        var textWriter = manager.GetWriter(WellknownSectionNames.Text);
        var linkedFunctions = new List<LinkedFunction>();
        int offset = 0;
        ulong rdataAlign = 8;
        var elfPath = CompileCSource(codegenDir);
        var func_text = File.ReadAllBytes(elfPath);
        textWriter.Write(func_text);
        foreach (var func in kernelFuntions)
        {
            linkedFunctions.Add(new LinkedFunction(func.Id, func.SourceFunction, (uint)offset, (uint)func_text.Length, func.Sections));
        }

        return new LinkedModule(linkedFunctions, _desc, manager.GetContent(WellknownSectionNames.Text)!, _rdata, _threadLocalRdatas, _blockLocalRdatas, rdataAlign);
    }

    private string CompileCSource(string sourcePath)
    {
        var compiler = new CSourceCompiler();
        return compiler.Compile(sourcePath, Path.Join(sourcePath, "build", Path.GetFileName(sourcePath)));
    }
}
