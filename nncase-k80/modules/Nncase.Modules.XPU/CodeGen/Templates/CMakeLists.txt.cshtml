@using System.Linq
@using NetFabric.Hyperlinq
@using Nncase
@using Nncase.CodeGen.NTT
@using Nncase.IR
@using Nncase.CodeGen.XPU;
@using static Nncase.CodeGen.NTT.CSourceExtensions;
@using static Nncase.CodeGen.XPU.CSourceExtensions;
@model Nncase.CodeGen.XPU.CMakeModel
@{
}
cmake_minimum_required(VERSION 3.13)
project(xpu)
# remove for newlib
string(REGEX REPLACE "-latomic" "" CMAKE_CXX_STANDARD_LIBRARIES "${CMAKE_CXX_STANDARD_LIBRARIES}")
@if(Model.Mode == RuntimeMode.SystemMode) {
@:if (DUCA_XPUSDK_PATH)
@:  add_definitions(-DSYS_MODE)
@:  include_directories($ENV{DUCA_XPUSDK_PATH}/include/ ${CMAKE_CURRENT_SOURCE_DIR} @(Model.RuntimePath)/include/nncase/runtime/xpu/kernel/)
@:endif()
@:if (DUMP_INST)
@:  add_definitions(-DDUMP_INST)
@:endif()
}

@if (Model.Mode == RuntimeMode.SystemMode)
{
@:add_compile_options(-O3 -fno-vectorize -fno-slp-vectorize)
} else {
@:add_compile_options(-O3)
}

include(@(Model.RuntimePath)/src/xpu_runtime.cmake)

@if (Model.Mode == RuntimeMode.SystemMode) {
@:add_compile_options(
@:  --target=riscv64-unknown-elf
@:  --gcc-toolchain=$ENV{DUCA_TOOLCHAIN_PATH}
@:  --sysroot=$ENV{DUCA_TOOLCHAIN_PATH}/riscv64-unknown-elf
@:  -Wall
@:  -fomit-frame-pointer
@:  # -fno-strict-aliasing
@:  -fno-builtin-malloc
@:  -fno-builtin-printf
@:  -fno-builtin-exit
@:  -mtls-dialect=desc
@:  -ffunction-sections
@:  -menable-experimental-extensions
@:  -fdata-sections
@:  -DCFG_MAKEFILE
@:  -fno-pic
@:  -fno-PIC
@:  -fexceptions
@:  # -march=rv64gcv_zvl1024b_zvfh_xtheadvsfa_xducakernel
@:  # -march=rv64gcv_zvl1024b_zvfh_xtheadvsfa_xducakernel_xducacopif_xtheadsync_xtheadlpw
@:  # -mcpu=c908x
@:  -fdeclspec
@:  -mrvv-vector-bits=zvl
@:  -Wno-narrowing
@:)
@:
@:set(CMAKE_LINKER ld.lld)
@:set(LINKER "$ENV{DUCA_XPUSDK_PATH}/link.lds")
@:# Set the linker flags
@:set(CMAKE_LIB_LINKER_FLAGS "-fuse-ld=lld --gcc-toolchain=$ENV{DUCA_TOOLCHAIN_PATH}
@:--sysroot=$ENV{DUCA_TOOLCHAIN_PATH}/riscv64-unknown-elf --target=riscv64-unknown-elf -static -Wl,--gc-sections -Wl,--library=c -Wl,--library=gloss -Wl,--library=g -Wl,--no-relax -Wl,--wrap=printf -Wl,--wrap=exit -Wl,--wrap=malloc -T ${LINKER}")
@:set(CMAKE_EXE_LINKER_FLAGS "-fuse-ld=lld --gcc-toolchain=$ENV{DUCA_TOOLCHAIN_PATH} --sysroot=$ENV{DUCA_TOOLCHAIN_PATH}/riscv64-unknown-elf --target=riscv64-unknown-elf -static -Wl,--gc-sections -Wl,--library=c -Wl,--library=gloss -Wl,--library=g -Wl,--no-relax -Wl,--wrap=printf -Wl,--wrap=exit -Wl,--wrap=malloc -T ${LINKER}")
} else {
@:add_compile_options(-fPIC -fexceptions)
}

set(CMAKE_CXX_STANDARD 23)

if (DISABLE_RVV)
  add_definitions(-DDISABLE_RVV)
endif()

add_link_options(
-no-pie
#-fPIE
-fno-stack-protector
@if(Model.Mode == RuntimeMode.SystemMode) {
@:-static
} else
{
@:-Wl,-e,_Z6_startPcPN6nncase7runtime3xpu19hardware_context_mtEPNS2_15runtime_util_mtEPNS2_19nncase_method_tableERSt6vectorINS0_3ntt7runtime17thread_inout_descESaISC_EESF_PhPmSG_SH_SG_SG_
}
)


@if (Model.Mode == RuntimeMode.SystemMode)
{
@:add_library(nncase_xpu_module STATIC "thread_main.cpp")
@:target_compile_options(nncase_xpu_module PRIVATE -mcmodel=medany -U__riscv_zvfbfmin)
@:target_include_directories(
@:nncase_xpu_module PUBLIC @(Model.RuntimePath)/include/nncase/runtime/xpu/kernel/ @(Model.RuntimePath)/include/
@:)
@:target_link_libraries(nncase_xpu_module PUBLIC nncase_ntt_module)
@:
@:add_executable(@Model.Name "main.cpp")
@:target_compile_options(@Model.Name PRIVATE -mcmodel=medany -U__riscv_zvfbfmin)
@:if (DUCA_XPUSDK_PATH)
@:  target_link_libraries(@Model.Name PUBLIC $ENV{DUCA_XPUSDK_PATH}/lib/libxpusdk.a)
@:endif()
@:target_link_libraries(@Model.Name PUBLIC -Wl,--whole-archive nncase_xpu_module -Wl,--no-whole-archive)
}
else
{
@:add_library(@Model.Name SHARED "main.cpp")
@:target_include_directories(@Model.Name PUBLIC @(Model.RuntimePath)/include/nncase/runtime/xpu/kernel/ @(Model.RuntimePath)/include/)
@:target_compile_options(@Model.Name PRIVATE -fvisibility=default)
@:target_link_libraries(@Model.Name PUBLIC nncase_ntt_module)
@:set_target_properties(@Model.Name PROPERTIES POSITION_INDEPENDENT_CODE ON)
@:set_target_properties(@Model.Name PROPERTIES PREFIX "" SUFFIX "")
}
