@using System.Linq
@using NetFabric.Hyperlinq
@using Nncase
@using Nncase.CodeGen.NTT
@using Nncase.IR
@using Nncase.CodeGen.XPU;
@using static Nncase.CodeGen.NTT.CSourceExtensions;
@using static Nncase.CodeGen.XPU.CSourceExtensions;
@model Nncase.CodeGen.NTT.KernelMainModel
@{
  var inputCount = Model.PrimFunction.Parameters.Length;
}

@if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode) {
@:__kernel__ void @(Model.PrimFunction.Name)_wrapper(const nncase::ntt::runtime::thread_inout_desc *input_descs, nncase::ntt::runtime::thread_inout_desc *const output_descs, uint8_t *rdata, void *uniform_thread_local_rdata_ptr, void *uniform_block_local_rdata_ptr, uint8_t *output_data, void *uniform_thread_local_data_ptr, void *uniform_block_local_data_ptr) {
@if (CSourceUtilities.DumpInst() != 0)
{
@:    uint64_t c1, c2;
@:    uint64_t i1, i2;
@:    i1 = ReadInstructions();
@:    c1 = ReadCycles();
@:    __asm__ volatile("sync");
}
@:    sync_init();
@:    int block_id = device_block_id();
@:    auto thread_local_rdata = (uint8_t *)get_local_uniform_ptr(uniform_thread_local_rdata_ptr, block_id) + threadIdx.x * @Model.ThreadLocalRdataPoolSize;
@:    auto block_local_rdata = (uint8_t *)get_local_uniform_ptr(uniform_block_local_rdata_ptr, block_id);
} 
else 
{
@:void @(Model.PrimFunction.Name)_wrapper(unsigned long current_id, const nncase::ntt::runtime::thread_inout_desc *input_descs, nncase::ntt::runtime::thread_inout_desc *const output_descs, uint8_t *rdata, uint8_t *thread_local_rdata, uint8_t *block_local_rdata, uint8_t *output_data) {
}
    using namespace nncase::ntt::distributed::detail;
    /* compute contexts */
    thread_context _thread_ctx;
    constexpr size_t _data_pool_size = @Model.DataSize;
      unsigned long placement = 0;
    @if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode)
    {
      @:unsigned long did = blockIdx.z;
      @:unsigned long yid = blockIdx.y;
      @:unsigned long xid = blockIdx.x;
      @:unsigned long tid = threadIdx.x;
      @:uint8_t *thread_local_data = (uint8_t *)get_local_uniform_ptr(uniform_thread_local_data_ptr, block_id) + tid * _data_pool_size;
      @:uint8_t *block_local_data = (uint8_t *)get_local_uniform_ptr(uniform_block_local_data_ptr, block_id);
    }
    else
    {
      @:unsigned long did = current_id / (@Model.Options.Hierarchies[0][2] * @Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4]);
      @:unsigned long yid = (current_id % (@Model.Options.Hierarchies[0][2] * @Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4])) / (@Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4]);
      @:unsigned long xid = ((current_id % (@Model.Options.Hierarchies[0][2] * @Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4])) % (@Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4])) / @Model.Options.Hierarchies[0][4];
      @:unsigned long tid = ((current_id % (@Model.Options.Hierarchies[0][2] * @Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4])) % (@Model.Options.Hierarchies[0][3] * @Model.Options.Hierarchies[0][4])) % @Model.Options.Hierarchies[0][4];
      @:uint8_t *thread_local_data = vhm[did * PLACEMENTS[placement][2] * PLACEMENTS[placement][3] + yid * PLACEMENTS[placement][3] + xid] + tid * _data_pool_size;
      @:uint8_t *block_local_data = vhm[did * PLACEMENTS[placement][2] * PLACEMENTS[placement][3] + yid * PLACEMENTS[placement][3] + xid] + _data_pool_size *  @Model.Options.Hierarchies[0][4];
    }
      xpu_thread_context = { .tid = tid, .bid = (yid * @Model.Options.Hierarchies[0][3]) + xid, .did = did, .cid = 0, };
      _thread_ctx = thread_context(0, did, yid, xid, tid, placement);
    @if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode)
    {
      @:global_thread_local_data_pool_start = (uintptr_t)get_local_uniform_ptr(uniform_thread_local_data_ptr, block_id);
      @:global_thread_local_data_pool_size = _data_pool_size;
      @:global_thread_local_rdata_pool_start = (uintptr_t)get_local_uniform_ptr(uniform_thread_local_rdata_ptr, block_id);
      @:global_thread_local_rdata_pool_size = @Model.ThreadLocalRdataPoolSize;
    }
    else
    {
      @:const auto program_ids = ntt::make_shape(0, did, xpu_thread_context.bid, tid);
      @:global_local_data_ptr(program_ids)(0_dim) = (uintptr_t)(thread_local_data);
      @:global_local_data_ptr(program_ids)(1_dim) = global_local_data_ptr(program_ids)(0_dim) + _data_pool_size;
      @:global_thread_local_rdata_ptr(program_ids)(0_dim) = (uintptr_t)thread_local_rdata;
      @:global_thread_local_rdata_ptr(program_ids)(1_dim) = global_thread_local_rdata_ptr(program_ids)(0_dim) + @Model.ThreadLocalRdataPoolSize;
      @:global_block_local_rdata_ptr(program_ids)(0_dim) = (uintptr_t)block_local_rdata;
      @:global_block_local_rdata_ptr(program_ids)(1_dim) = global_block_local_rdata_ptr(program_ids)(0_dim) + @Model.BlockLocalRdataPoolSize;
    }

    /* prepare inputs */
    @{
    var names = new List<string>();
    var dynamicVars = IRHelpers.GetDynamicDimVars();

    foreach (var (input, i) in Model.PrimFunction.Parameters.ToArray().Select((input, i) => (input, i))) 
    {
      var name = IRHelpers.GetIdentityName(input.Name);
      names.Add(name);

      if (input is DimVar)
      {
    @:int64_t @name = *(int64_t*)input_descs[@i].data;
      }
      else 
      {
          var shape = input.CheckedShape;
          var rank = shape.Rank;
          var sizeStr = $"input_descs[{i}].size / {input.CheckedDataType.SizeInBytes}";
          var elemType = input.CheckedDataType.ToC();
          var dimStr = $"make_shape({string.Join(", ", Enumerable.Range(0, rank).Select(d => shape[d].IsFixed ? $"{shape[d].FixedValue}_dim" : $"input_descs[{i}].shape[{d}]"))})";
          var strideStr = $"make_strides({string.Join(", ", Enumerable.Range(0, rank).Select(d => $"input_descs[{i}].strides[{d}]"))})";

          if (input.CheckedDataType is ReferenceType referenceType)
          {
              if (referenceType.ElemType is Nncase.IR.NN.PagedAttentionKVCacheType kvcacheType)
              {
                  var size = TensorUtilities.GetProduct(shape.ToValueArray()); // todo: support dynamic shape
    @:std::span<ntt::runtime::thread_paged_attention_kv_cache_desc> p@(name)_descs((ntt::runtime::thread_paged_attention_kv_cache_desc*)input_descs[@i].data, input_descs[@i].size / sizeof(ntt::runtime::thread_paged_attention_kv_cache_desc));
    @:using kv_storage_type_t = typename paged_attention_kv_cache_t::kv_storage_type_t;
    @:alignas(paged_attention_kv_cache_t) std::byte p@(name)[sizeof(paged_attention_kv_cache_t) * @size];
    @:distributed::topology_synchronize(); // note: from uma to numa need wait for collect other thread's vhm address.
    @:for (size_t i = 0; i < @size; ++i) {
      @:auto desc = p@(name)_descs[i];
      @:auto kv_addrs = make_tensor_view_from_address((kv_storage_type_t **)desc.kv_cache_addrs.data(), paged_attention_kv_cache_t::kv_addrs_shape);
      @:auto ptr = (paged_attention_kv_cache_t*)p@(name) + i;
      @:new (ptr) paged_attention_kv_cache_t(desc.num_seqs, desc.num_tokens, make_tensor_view_from_address(desc.context_lens, make_shape(desc.context_lens_size)), make_tensor_view_from_address(desc.seq_lens, make_shape(desc.seq_lens_size)), make_tensor_view_from_address(desc.block_table, make_shape(desc.block_table_shape[0], desc.block_table_shape[1], paged_attention_kv_cache_t::id_length)), make_tensor_view_from_address(desc.slot_mapping, make_shape(desc.slot_mapping_shape[0], paged_attention_kv_cache_t::id_length)), kv_addrs);
    @:}
    @:distributed::topology_synchronize(); // note: from numa to uma need wait for tensor copy.
    @:auto @(name) = make_tensor_view_from_address((paged_attention_kv_cache_t *)p@(name), @Html.Raw(dimStr), @Html.Raw(strideStr));
              }
          }
          else
          {
  @:auto @(name) = make_tensor_view_from_address((const @Html.Raw(elemType) *)input_descs[@i].data, @Html.Raw(dimStr), @Html.Raw(strideStr));
          }
      }
    }

    @if (CSourceUtilities.DumpInst() != 0)
    {
    @:    __asm__ volatile("sync");
    @:    c2 = ReadCycles();
    @:    i2 = ReadInstructions();
    @:    printf("entry_cycle %lu\n", (c2 - c1));
    @:    printf("entry_insts %lu\n", (i2 - i1));
    }

    
    @:@(Model.PrimFunction.Name)(_thread_ctx, thread_local_data, block_local_data, rdata, thread_local_rdata, block_local_rdata, @(string.Join(", ", names)), output_data, output_descs);
    }
}

