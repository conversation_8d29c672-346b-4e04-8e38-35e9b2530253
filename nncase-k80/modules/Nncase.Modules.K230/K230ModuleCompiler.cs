﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using Nncase.CodeGen;
using Nncase.CodeGen.K230;
using Nncase.IR;
using Nncase.Passes;

namespace Nncase.Targets;

public class K230ModuleCompiler : IModuleCompiler
{
    public string ModuleKind => K230Target.Kind;

    public MaskVectorStyle MaskVectorStyle => MaskVectorStyle.Slim;

    public int Lane => 16;

    public IModuleBuilder CreateModuleBuilder(CompileOptions options) => new CodeGen.K230.ModuleBuilder(options);

    public bool IsSupportedCall(Call call, CompileOptions options)
    {
        return call.Target switch
        {
            Op op => PassUtility.IsCpuSupported(op, call, call.Arguments, ModuleKind),
            _ => false,
        };
    }
}
