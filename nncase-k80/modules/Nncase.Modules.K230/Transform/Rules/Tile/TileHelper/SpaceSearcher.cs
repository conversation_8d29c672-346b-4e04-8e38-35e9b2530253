// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Nncase.IR;
using Nncase.TIR;
using static Nncase.Passes.Rules.K230.TileUtilities;

namespace Nncase.Passes.Rules.K230;

public static class GNNEEnv
{
    public static bool UseCcr { get; } = true;

    // x0 is fixed
    public static int GprNum { get; } = 31;

    public static int SsrNum { get; } = 8;

    public static int PuHeight { get; } = 24;

    public static int PuWidth { get; } = 32;

    public static int WAlignNum { get; } = PuHeight;

    public static int ActNumPerChan { get; } = 7;

    public static int TcuActNum { get; } = 1;

    public static int MfuPuHeight { get; } = 16;

    public static int PuKernelSpad { get; } = 8;

    public static int GlbBankWidth { get; } = 32;

    public static int GlbBankHeight { get; } = 4096;

    public static int GlbWidth { get; } = 1;

    public static int GlbHeight { get; } = 32;

    public static int GlbBankSize { get; } = GlbBankWidth * GlbBankHeight;

    public static int GlbDepth { get; } = GlbHeight * GlbBankHeight;

    public static int GlbSize { get; } = GlbBankSize * GlbWidth * GlbHeight;

    /// <summary>
    /// Gets a value indicating whether false.
    /// </summary>
    public static bool BatchInference { get; }

    public static int NPingPongSplit { get; } = 2;

    public static int IfmapBankWidth { get; } = GlbWidth;

    public static int WBankWidth { get; } = GlbWidth;

    public static int OfmapBankWidth { get; } = GlbWidth;

    public static int PsumBankWidth { get; } = GlbWidth;

    public static int ActBankWidth { get; } = GlbWidth;

    public static int IfQargBankWidth { get; } = GlbWidth;

    public static int ResInQargBankWidth { get; } = GlbWidth;

    public static int WQargBankWidth { get; } = GlbWidth;

    public static int StQargBankWidth { get; } = GlbWidth;

    public static int IfL1SizePerChan { get; } = 1024;

    public static int IfL1Size { get; } = PuHeight * IfL1SizePerChan;

    public static int PsumL1ElePerChan { get; } = 1024;

    public static int PsumL1Size { get; } = 4 * PuWidth * PsumL1ElePerChan;

    public static int MultiLayerTilingH { get; }

    public static int MultiLayerTilingW { get; }

    public static int Ai2dSramLen { get; } = 256;

    public static int Ai2dSramSize { get; } = Ai2dSramLen * Ai2dSramLen;
}

public class BoxOnGlb
{
    // public readonly tensor_on_glb owner;
    private readonly ItemName _ownerName;

    private readonly int[] _box;

    // public BoxOnGlb(int[] box, tensor_on_glb owner, item_name owner_name)
    public BoxOnGlb(int[] box, ItemName ownerName)
    {
        _box = box;
        _box = box;

        // this.owner = owner;
        _ownerName = ownerName;
    }

    public ItemName OwnerName => _ownerName;

    public int[] Box => _box;

    public override string ToString()
    {
        return $"[w: {Box[0]}, h: {Box[1]}]";
    }
}

/// <inheritdoc />
public class TensorOnGlb : IBufferView<TensorOnGlb>
{
    private readonly SelectedRange[] _selectedRanges;

    private readonly int[] _stride;

    private readonly int[] _dimensions;

    public TensorOnGlb(ReadOnlySpan<int> fullShape, DataType dataType, int startAddr, MmuItem mmu = null!)
    {
        DType = dataType;
        _selectedRanges = fullShape.ToArray().Select(i => new SelectedRange(0, i, Padding.Zero())).ToArray();
        _dimensions = SelectedRanges.ToArray().Select(it => it.End - it.Start).ToArray();
        _stride = TensorUtilities.GetDefaultStridesGeneric(fullShape, false).Select(i => i * DType.SizeInBytes).ToArray();
        IsSubView = false;
        Parent = this;
        RootParent = this;

        Addr = startAddr;
        Mmu = mmu ?? new();

        AllocatedBytes = GlbNByte;
    }

    /// <summary>
    /// Initializes a new instance of the <see cref="TensorOnGlb"/> class.
    /// from the select ranges.
    /// </summary>
    /// <param name="shape_ranges">shape_ranges.</param>
    /// <param name="parent">parent.</param>
    public TensorOnGlb(ReadOnlySpan<SelectedRange> shape_ranges, TensorOnGlb parent)
    {
        DType = parent.DType;
        _selectedRanges = shape_ranges.ToArray();
        _dimensions = _selectedRanges.Select(it => it.End - it.Start).ToArray();
        _stride = parent.Stride.ToArray();
        IsSubView = true;
        Parent = parent;
        RootParent = parent.RootParent;

        Addr = parent.Addr;
        Mmu = parent.Mmu;

        AllocatedBytes = GlbNByte;
    }

    public ReadOnlySpan<SelectedRange> SelectedRanges => _selectedRanges;

    public ReadOnlySpan<int> Stride => _stride;

    public ReadOnlySpan<int> Dimensions => _dimensions;

    public int GetGlbNElement => (int)Math.Ceiling(1.0 * (Addr + (Dimensions[0] * Stride[0])) / DType.SizeInBytes);

    public int GlbNByte => Addr + (Dimensions[0] * Stride[0]);

    public int Addr { get; private set; }

    public MmuItem Mmu { get; set; }

    public int AllocatedBytes { get; set; }

    public int AddrOffset => SelectedRanges.ToArray().Zip(Stride.ToArray()).Aggregate(0, (acc, t) => acc + (t.First.Start * t.Second));

    public int CurAddr => Addr + AddrOffset;

    /// <summary>
    /// Gets get the StrideGlb.
    /// </summary>
    /// <returns>.</returns>
    // public IR.Expr StrideGlb()
    // {
    //     return TIR.K230.I.PackStrideGlb(RootParent.Stride[0], RootParent.Stride[1], RootParent.Stride[2]);
    // }
    public TensorOnGlb Parent { get; init; }

    public TensorOnGlb RootParent { get; init; }

    public DataType DType { get; init; }

    public bool IsSubView { get; init; }

    public TensorOnGlb this[SegmentND segments]
    {
        get
        {
            Assert(segments.Count == SelectedRanges.Length);
            return new(segments.Zip(SelectedRanges.ToArray()).Select(t => t.Second.Slice(t.First)).ToArray(), this);
        }
    }

    public TensorOnGlb this[params Segment1D[] segments]
    {
        get => this[new SegmentND(segments)];
    }

    public int GetAddr(int dim0, int dim1, int dim2, int dim3, int pp = 0, int bufNum = 2)
    {
        Assert(dim0 < Dimensions[0] || (dim0 == 0 && dim0 == Dimensions[0]));
        Assert(dim1 < Dimensions[1] || (dim1 == 0 && dim1 == Dimensions[1]));
        Assert(dim2 < Dimensions[2] || (dim2 == 0 && dim2 == Dimensions[2]));
        Assert(dim3 < Dimensions[3] || (dim3 == 0 && dim3 == Dimensions[3]));

        int ppOffset = 0;
        if (pp > 0)
        {
            ppOffset = Mmu.Width * GNNEEnv.GlbBankWidth * Mmu.Depth / bufNum;
            while (ppOffset % 4 != 0)
            {
                ppOffset -= 1;
            }

            ppOffset *= pp;
        }

        return ppOffset + (dim0 * Stride[0]) + (dim1 * Stride[1]) + (dim2 * Stride[2]) + (dim3 * Stride[3]);
    }

    public void SetGlbStride(int noconflictH = 0, int noconflictC = 0, int noconflictN = 0, int alignedFactor = 1)
    {
        var dim = Dimensions;
        if (noconflictH == 1)
        {
            int tmpStrideH = GetAlignedNum(_stride[2], GNNEEnv.GlbBankWidth);
            while ((tmpStrideH / GNNEEnv.GlbBankWidth) % 2 != 1)
            {
                tmpStrideH += GNNEEnv.GlbBankWidth;
            }

            _stride[2] = tmpStrideH;
            _stride[2] = GetAlignedNum(_stride[2], alignedFactor);
        }
        else
        {
            _stride[2] = dim[3] * _stride[3];
        }

        if (noconflictC == 1)
        {
            int tmpStrideC = GetAlignedNum(_stride[1], GNNEEnv.GlbBankWidth);
            while ((tmpStrideC / GNNEEnv.GlbBankWidth) % 2 != 1)
            {
                tmpStrideC += GNNEEnv.GlbBankWidth;
            }

            _stride[1] = tmpStrideC;
        }
        else
        {
            _stride[1] = dim[2] * _stride[2];
        }

        if (noconflictN == 1)
        {
            int tmpStrideN = GetAlignedNum(_stride[0], GNNEEnv.GlbBankWidth);
            while ((tmpStrideN / GNNEEnv.GlbBankWidth) % 2 != 1)
            {
                tmpStrideN += GNNEEnv.GlbBankWidth;
            }

            _stride[0] = tmpStrideN;
        }
        else
        {
            _stride[0] = dim[1] * _stride[1];
        }
    }

    /// <inheritdoc/>
    public override string ToString()
    {
        return $"{DType.GetDisplayName()}[{string.Join(",", _dimensions)}]";
    }
}

public class CcrHandler
{
    private readonly List<Ccr> _ccrs;
    private readonly List<int> _nonUsedItem;
    private readonly Dictionary<int, string> _itemToSpace;
    private readonly Dictionary<string, int> _spaceToItem;
    private int _nCcr = 64;

    public CcrHandler()
    {
        _ccrs = new();
        _nonUsedItem = new();
        _spaceToItem = new();
        _itemToSpace = new();
        for (int i = 0; i < _nCcr; i++)
        {
            _ccrs.Add(new(i));
            _nonUsedItem.Add(i);
        }
    }

    public string GetName(ItemName itemName, int id = -1) => $"{itemName}" + (id >= 0 ? $"_{id}" : string.Empty);

    public int GetCcrItem(string space)
    {
        if (_spaceToItem.TryGetValue(space, out var ccrItem))
        {
            return ccrItem;
        }

        ccrItem = GetNonUsedItem();
        _spaceToItem[space] = ccrItem;
        _itemToSpace[ccrItem] = space;
        return ccrItem;
    }

    public void FreeCcrItem(int item)
    {
        string space = _itemToSpace[item];
        _itemToSpace.Remove(item);
        _spaceToItem.Remove(space);
        _nonUsedItem.Add(item);
    }

    public void SetItem(int item, int value)
    {
        _ccrs[item].Value = value;
    }

    public void SetItems(List<CcrSet> ccrsToSet)
    {
        foreach (var cs in ccrsToSet)
        {
            _ccrs[cs.Ccr].Value = cs.Value;
        }
    }

    public void AcquireItem(int item)
    {
        _ccrs[item].Acquire();
    }

    public int GetValue(int item)
    {
        return _ccrs[item].Value;
    }

    public void ClearItem(int item)
    {
        _ccrs[item].Clear();
    }

    public void ClearItems(List<CcrClr> ccrsToClr)
    {
        foreach (var cc in ccrsToClr)
        {
            _ccrs[cc.Ccr].Clear();
        }
    }

    public bool CcrSanityCheck()
    {
        bool ret = true;
        for (int i = 0; i < _nCcr; i++)
        {
            if (_ccrs[i].Value != 0)
            {
                ret = false;
                break;
            }
        }

        return ret;
    }

    private int GetNonUsedItem()
    {
        int ccrItem = _nonUsedItem[0];
        _nonUsedItem.Remove(_nonUsedItem[0]);
        return ccrItem;
    }
}

public class GprHandler
{
    private readonly List<KeyValuePair<GprKey, int>> _gprIdx = new();
    private readonly Dictionary<GprKey, KeyValuePair<GprKey, int>> _gprIdxMap = new();
    private readonly Dictionary<GprKey, int> _gprValMap = new();
    private readonly int _nGpr;
    private int _idxToUse;
    private int _nextIdxToAllocate;

    public GprHandler(int nGpr = 31)
    {
        _nGpr = nGpr;
        _idxToUse = 0;
        _nextIdxToAllocate = 1;
        _gprIdx.Clear();
        _gprIdxMap.Clear();
        _gprValMap.Clear();
    }

    public Gpr GetGprItem(GprKey key)
    {
        if (_gprIdxMap.ContainsKey(key))
        {
            return new Gpr(-1, -1, false);
        }

        var keyValue = _gprIdxMap[key];
        _gprIdx.Remove(_gprIdxMap[key]);
        _gprIdx.Insert(0, keyValue);
        _gprIdxMap[key] = _gprIdx[0];
        return new Gpr(keyValue.Value, _gprValMap[key], false);
    }

    public Gpr SetGprItem(int value, bool basement = false)
    {
        var key = new GprKey(value, basement);
        if (value == 0 && !basement)
        {
            return new Gpr(0, 0, false);
        }

        bool changed;
        if (!_gprIdxMap.ContainsKey(key) || basement)
        {
            if (_gprIdx.Count == _nGpr)
            {
                var item = _gprIdx[_nGpr - 1];
                _idxToUse = item.Value;
                _gprIdxMap.Remove(item.Key);
                _gprIdx.Remove(item);
            }
            else
            {
                _idxToUse = _nextIdxToAllocate;
                _nextIdxToAllocate = (_nextIdxToAllocate + 1) % _nGpr;
                if (_nextIdxToAllocate == 0)
                {
                    _nextIdxToAllocate = _nGpr;
                }
            }

            changed = true;
        }
        else
        {
            _idxToUse = _gprIdxMap[key].Value;
            _gprIdx.Remove(_gprIdxMap[key]);
            changed = value != _gprValMap[key];
        }

        _gprIdx.Insert(0, new(key, _idxToUse));
        _gprIdxMap[key] = _gprIdx[0];
        _gprValMap[key] = value;
        if (basement)
        {
            changed = true;
        }

        return new Gpr(_idxToUse, value, changed);
    }

    public Gpr SetGprItem1(Expr value)
    {
        var key = new GprKey(-1, true);
        if (_gprIdx.Count == _nGpr)
        {
            var item = _gprIdx[_nGpr - 1];
            _idxToUse = item.Value;
            _gprIdxMap.Remove(item.Key);
            _gprIdx.Remove(item);
        }
        else
        {
            _idxToUse = _nextIdxToAllocate;
            _nextIdxToAllocate = (_nextIdxToAllocate + 1) % _nGpr;
            if (_nextIdxToAllocate == 0)
            {
                _nextIdxToAllocate = _nGpr;
            }
        }

        bool changed = true;

        _gprIdx.Insert(0, new(key, _idxToUse));
        _gprIdxMap[key] = _gprIdx[0];
        _gprValMap[key] = -1;

        return new Gpr(_idxToUse, value, changed);
    }
}

public class SsrHandler
{
    private readonly int _nSsr;
    private readonly List<KeyValuePair<SsrKey, int>> _ssrIdx = new();
    private readonly Dictionary<SsrKey, KeyValuePair<SsrKey, int>> _ssrIdxMap = new();
    private readonly Dictionary<SsrKey, long> _ssrValMap = new();
    private int _idxToUse;
    private int _nextIdxToAllocate;

    public SsrHandler(int nSsr = 8)
    {
        _nextIdxToAllocate = 0;
        _nSsr = nSsr;
        _idxToUse = 0;
        _ssrIdx.Clear();
        _ssrIdxMap.Clear();
        _ssrValMap.Clear();
    }

    public Ssr GetSsrItem(SsrKey key)
    {
        if (_ssrIdxMap.ContainsKey(key))
        {
            return new(-1, -1, false);
        }

        var keyValue = _ssrIdxMap[key];
        _ssrIdx.Remove(_ssrIdxMap[key]);
        _ssrIdx.Insert(0, keyValue);
        _ssrIdxMap[key] = _ssrIdx[0];
        return new(keyValue.Value, _ssrValMap[key], false);
    }

    public Ssr SetSsrItem(long value, bool forceNew = false)
    {
        var key = new SsrKey(value);
        bool changed;
        if (!_ssrIdxMap.ContainsKey(key) || forceNew)
        {
            if (_ssrIdx.Count == _nSsr)
            {
                var item = _ssrIdx[_nSsr - 1];
                _idxToUse = item.Value;
                _ssrIdxMap.Remove(item.Key);
                _ssrIdx.Remove(item);
            }
            else
            {
                _idxToUse = _nextIdxToAllocate;
                _nextIdxToAllocate = (_nextIdxToAllocate + 1) % _nSsr;
            }

            changed = true;
        }
        else
        {
            _idxToUse = _ssrIdxMap[key].Value;
            _ssrIdx.Remove(_ssrIdxMap[key]);
            changed = value != _ssrValMap[key];
        }

        _ssrIdx.Insert(0, new(key, _idxToUse));
        _ssrIdxMap[key] = _ssrIdx[0];
        _ssrValMap[key] = value;

        return new(_idxToUse, value, changed);
    }
}

public class WeightGroupHandler
{
    private readonly DataType _weightDatatypeDdr;
    private readonly DataType _weightDatatypeGlb;
    private readonly List<SegmentND> _weightGroupSlice = new();
    private readonly List<Segment1D> _qargGroupSlice = new();
    private readonly List<SegmentND> _dwGroupSlice = new();
    private readonly List<Segment1D> _dwQargGroupSlice = new();
    private readonly Dictionary<SegmentND, int> _weightGroupOffset = new();
    private readonly Dictionary<SegmentND, int> _weightGroupAlignedOffset = new(0);
    private readonly Dictionary<SegmentND, int> _weightGroupAlignedGlbOffset = new(0);
    private readonly Dictionary<Segment1D, int> _qargAlignedOffset = new();
    private readonly Dictionary<SegmentND, int> _dwGroupOffset = new();
    private readonly Dictionary<SegmentND, int> _dwGroupAlignedOffset = new();
    private readonly Dictionary<Segment1D, int> _dwQargAlignedOffset = new();

    private int _currentOffset;

    private int _currentAlignedOffset;

    // private int current_aligned_glb_offset_;
    private int _currentQargOffset;
    private int _currentQargAlignedOffset;
    private int _currentDwOffset;
    private int _currentDwAlignedOffset;
    private int _currentDwQargAlignedOffset;

    public WeightGroupHandler(DataType weightDatatypeDdr, DataType weightDatatypeGlb)
    {
        _weightDatatypeDdr = weightDatatypeDdr;
        _weightDatatypeGlb = weightDatatypeGlb;
        _currentOffset = 0;
        _currentAlignedOffset = 0;

        // current_aligned_glb_offset_ = 0;
        _currentQargOffset = 0;
        _currentQargAlignedOffset = 0;
        _currentDwOffset = 0;
        _currentDwAlignedOffset = 0;
        _currentDwQargAlignedOffset = 0;
    }

    public void Current_aligned_offset_init()
    {
        _currentAlignedOffset = 0;
    }

    public void UpdateWeightGroup(SegmentND tensor4d)
    {
        if (!_weightGroupSlice.Contains(tensor4d))
        {
            _weightGroupSlice.Add(tensor4d);
            _weightGroupOffset.Add(tensor4d, _currentOffset);
            _weightGroupAlignedOffset.Add(tensor4d, _currentAlignedOffset);

            if (!_qargGroupSlice.Contains(tensor4d[0]))
            {
                _qargGroupSlice.Add(tensor4d[0]);
                _qargAlignedOffset.Add(tensor4d[0], _currentQargAlignedOffset);
                _currentQargOffset += tensor4d[0].Length;
                _currentQargAlignedOffset += (int)Math.Ceiling(1.0f * tensor4d[0].Length / GNNEEnv.PuWidth) * GNNEEnv.PuWidth;
            }

            _currentOffset += GetShapeSize(tensor4d);
            _currentAlignedOffset += GetAlignedShapeSize(tensor4d);
        }
    }

    public void UpdateDwWeightGroup(SegmentND tensor4d)
    {
        if (!_dwGroupSlice.Contains(tensor4d))
        {
            _dwGroupSlice.Add(tensor4d);
            _dwGroupOffset.Add(tensor4d, _currentDwOffset);
            _dwGroupAlignedOffset.Add(tensor4d, _currentDwAlignedOffset);

            if (!_dwQargGroupSlice.Contains(tensor4d[1]))
            {
                _dwQargGroupSlice.Add(tensor4d[1]);
                _dwQargAlignedOffset.Add(tensor4d[1], _currentDwQargAlignedOffset);
                _currentDwQargAlignedOffset += (int)Math.Ceiling(1.0f * tensor4d[1].Length / GNNEEnv.PuWidth) * GNNEEnv.PuWidth;
            }

            _currentDwOffset += GetShapeSize(tensor4d, GNNEEnv.PuWidth);
            _currentDwAlignedOffset += GetAlignedShapeSizeDw(tensor4d);
        }
    }

    public void UpdateWeightGroupOnGlb(SegmentND tensor4d, int offset)
    {
        if (!_weightGroupAlignedGlbOffset.ContainsKey(tensor4d))
        {
            throw new KeyNotFoundException();
        }

        if (!_weightGroupAlignedGlbOffset.ContainsKey(tensor4d))
        {
            _weightGroupAlignedGlbOffset.Add(tensor4d, offset);
        }
    }

    public int WeightGroupOffset(SegmentND tensor4d)
    {
        if (!_weightGroupOffset.ContainsKey(tensor4d))
        {
            throw new KeyNotFoundException();
        }

        return _weightGroupOffset[tensor4d];
    }

    public int WeightGroupAlignedOffset(SegmentND tensor4d)
    {
        if (!_weightGroupAlignedOffset.ContainsKey(tensor4d))
        {
            throw new KeyNotFoundException();
        }

        return _weightGroupAlignedOffset[tensor4d];
    }

    public int WeightGroupAlignedGlbOffset(SegmentND tensor4d)
    {
        if (!_weightGroupAlignedGlbOffset.ContainsKey(tensor4d))
        {
            throw new KeyNotFoundException();
        }

        return _weightGroupAlignedGlbOffset[tensor4d];
    }

    public int DwGroupOffset(SegmentND tensor4d)
    {
        if (!_dwGroupOffset.ContainsKey(tensor4d))
        {
            throw new KeyNotFoundException();
        }

        return _dwGroupOffset[tensor4d];
    }

    public int DwGroupAlignedOffset(SegmentND tensor4d)
    {
        if (!_dwGroupAlignedOffset.ContainsKey(tensor4d))
        {
            throw new KeyNotFoundException();
        }

        return _dwGroupAlignedOffset[tensor4d];
    }

    public int QargAlignedOffset(Segment1D mSeg)
    {
        if (!_qargAlignedOffset.ContainsKey(mSeg))
        {
            throw new KeyNotFoundException();
        }

        return _qargAlignedOffset[mSeg];
    }

    public int DwQargAlignedOffset(Segment1D mSeg)
    {
        if (!_dwQargAlignedOffset.ContainsKey(mSeg))
        {
            throw new KeyNotFoundException();
        }

        return _dwQargAlignedOffset[mSeg];
    }

    public List<SegmentND> WeightGroupSlice()
    {
        return _weightGroupSlice;
    }

    public List<Segment1D> QargGroupSlice()
    {
        return _qargGroupSlice;
    }

    public List<SegmentND> DwGroupSlice()
    {
        return _dwGroupSlice;
    }

    public List<Segment1D> DwQargGroupSlice()
    {
        return _dwQargGroupSlice;
    }

    public int CurrentOffset()
    {
        return _currentOffset;
    }

    public int CurrentAlignedOffset()
    {
        return _currentAlignedOffset;
    }

    public int CurrentQargAlignedOffset()
    {
        return _currentQargAlignedOffset;
    }

    public int CurrentQargOffset()
    {
        return _currentQargOffset;
    }

    public int GetShapeSize(SegmentND tensor4d, int align = 1)
    {
        return tensor4d[0].Length * (int)Math.Ceiling(1.0f * tensor4d[1].Length / align) * align * tensor4d[2].Length * tensor4d[3].Length;
    }

    public int GetAlignedShapeSize(SegmentND tensor4d)
    {
        return GetShapeSize(tensor4d, GNNEEnv.WAlignNum);
    }

    public int GetAlignedShapeSizeDw(SegmentND tensor4d)
    {
        return GetShapeSize(tensor4d, GNNEEnv.PuWidth);
    }

    public int WeightBytesPerElementDdr()
    {
        return GetBytesPerElement(_weightDatatypeDdr);
    }

    public int WeightBytesPerElementGlb()
    {
        return GetBytesPerElement(_weightDatatypeGlb);
    }
}

public class TiledGlb
{
    private Dictionary<ItemName, TensorOnGlb> _glbMap = null!;

    private Dictionary<ItemName, MmuItem> _items = null!;
    private int[] _lastOutShape = null!; // reduce window
    private int _nPingPongSplit;

    public TiledGlb(Dictionary<ItemName, TensorOnGlb> glbMap, Dictionary<ItemName, MmuItem> items, int[] lastOutShape, int nPingPongSplit)
    {
        GlbMap = glbMap;
        Items = items;
        LastOutShape = lastOutShape;
        NPingPongSplit = nPingPongSplit;
    }

    public TiledGlb()
    {
        GlbMap = new();
        Items = new();
        LastOutShape = Array.Empty<int>();
        NPingPongSplit = 1;
    }

    public Dictionary<ItemName, TensorOnGlb> GlbMap
    {
        get => _glbMap;
        set => _glbMap = value ?? throw new ArgumentNullException(nameof(value));
    }

    public Dictionary<ItemName, MmuItem> Items
    {
        get => _items;
        set => _items = value ?? throw new ArgumentNullException(nameof(value));
    }

    public int[] LastOutShape
    {
        get => _lastOutShape;
        set => _lastOutShape = value ?? throw new ArgumentNullException(nameof(value));
    }

    public int NPingPongSplit
    {
        get => _nPingPongSplit;
        set => _nPingPongSplit = value;
    }
}

internal static class SpaceSearcher
{
    // public space_searcher()
    // {
    // }
    public static int GetBasementSize()
    {
        return 128 * 4;
    }

    public static int GetTensorSize(int d0, int d1, int d2, int d3, int bytesPerElement)
    {
        return d0 * d1 * d2 * d3 * bytesPerElement;
    }

    public static int GetWeightSize(int r, int s, int c, int m, int bytesPerElement)
    {
        return r * s * (int)Math.Ceiling(1.0f * c / GNNEEnv.WAlignNum) * GNNEEnv.WAlignNum * m * bytesPerElement;
    }

    public static int GetActSize(int c, int nActParam, int bytesPerElement)
    {
        return c * nActParam * bytesPerElement;
    }

    public static int GetQargSize(int c, int bytesPerElement)
    {
        return c * bytesPerElement;
    }

    public static int GetWQargSize(int m, int bytesPerElement)
    {
        return (int)Math.Ceiling(1.0f * m / GNNEEnv.PuWidth) * GNNEEnv.PuWidth * bytesPerElement;
    }

    public static int GetInputHeight(int e, int inH, int r, int outH, int uh, int dh, in Padding p)
    {
        var segments = SegmentBy(0, e, outH).ToList();
        int h = 0;
        foreach (var seg in segments)
        {
            int tmp = GetInputRowSegment(seg.Start, e, inH, r, uh, dh, p).Length;
            if (tmp > h)
            {
                h = tmp;
            }
        }

        return h;
    }

    public static int GetInputWidth(int f, int inW, int s, int outW, int uw, int dw, in Padding p)
    {
        return GetInputHeight(f, inW, s, outW, uw, dw, p);
    }

    public static int GetDeconvInputHeight(int e, int inH, int r, int outH, int uh, int dh, Padding p)
    {
        var segments = SegmentBy(0, e, outH).ToList();
        int h = 0;
        foreach (var seg in segments)
        {
            int tmp = GetDeconvInputRowSegment(seg.Start, seg.Length, p.Before, inH, uh, r, dh).Length;
            if (tmp > h)
            {
                h = tmp;
            }
        }

        return h;
    }

    public static int GetDeconvInputWidth(int f, int inW, int s, int outW, int uw, int dw, Padding p)
    {
        return GetDeconvInputHeight(f, inW, s, outW, uw, dw, p);
    }

    public static AllocateResult TryAllocate(BoxPacker bp)
    {
        var boxes = bp.Boxes.AsReadOnly();

        // 1. reorder boxes
        int[] boxDepth = boxes.Select(b => b.Box[1]).ToArray();
        int[] boxOrderByDepth = SortIndexes(boxDepth);
        var sortedBoxes = boxOrderByDepth.Select(i => boxes[i]).ToArray();

        int[] boxWidth = boxes.Select(b => b.Box[0]).ToArray();
        int[] boxOrderByWidth = SortIndexes(boxWidth);
        int[] boxOrderFinal = boxOrderByWidth.Select(i => boxOrderByDepth[i]).ToArray();

        // 1.1 put basement at first place
        var boxOrderFinalList = boxOrderFinal.ToList();
        foreach (int idx in boxOrderFinal)
        {
            if (bp.Boxes[idx].OwnerName != ItemName.Basement)
            {
                continue;
            }

            boxOrderFinalList.Remove(idx);
            boxOrderFinalList.Insert(0, idx);
            break;
        }

        boxOrderFinal = boxOrderFinalList.ToArray();

        // 2. try allocate
        Dictionary<ItemName, MmuItem> items = new();
        bool ok = true;
        for (int i = 0; i < boxes.Count; i++)
        {
            var item = bp.Allocate_item(boxes[boxOrderFinal[i]].Box[0], boxes[boxOrderFinal[i]].Box[1]);
            if (item is { Depth: 0, Width: 0 })
            {
                ok = false;
                break;
            }

            items[boxes[boxOrderFinal[i]].OwnerName] = item;

            // boxes[box_order_final[i]].owner.mmu = item;
        }

        AllocateResult result = new();
        result.IsOk = ok;
        result.Items = items;
        result.Boxes = bp.Boxes;
        return result;
    }

    private static int[] SortIndexes<T>(T[] v)
        where T : unmanaged, IEquatable<T>
    {
        var idx = Enumerable.Range(0, v.Length).ToList();
        idx.Sort((i1, i2) => Comparer<T>.Default.Compare(v[i2], v[i1]));
        return idx.ToArray();
    }
}

internal class BoxPacker
{
    private readonly ILogger<BoxPacker> _logger = CompileSessionScope.GetCurrentThrowIfNull().GetRequiredService<ILogger<BoxPacker>>();
    private readonly int _width;
    private readonly int _height;
    private readonly int _nMaxMmuNum;
    private readonly int[,] _space;
    private readonly List<MmuItem> _items;
    private List<BoxOnGlb> _boxes = null!;

    public BoxPacker(int maxMmuNum)
        : this(GNNEEnv.GlbWidth, GNNEEnv.GlbDepth, maxMmuNum)
    {
    }

    public BoxPacker(int width, int height, int nMaxMmuNum)
    {
        _width = width;
        _height = height;
        _nMaxMmuNum = nMaxMmuNum;
        Boxes = new();
        _space = new int[height, width];
        _items = new();
        for (int i = 0; i < height; ++i)
        {
            for (int j = 0; j < width; ++j)
            {
                _space[i, j] = -1;
            }
        }
    }

    public List<BoxOnGlb> Boxes
    {
        get => _boxes;
        set => _boxes = value;
    }

    public void Add(BoxOnGlb box) => Boxes.Add(box);

    // public void Add(tensor_on_glb tensor) => Add(tensor.ToBox());
    public MmuItem Allocate_item(int width, int height)
    {
        Assert(width != 0);
        if (height == 0)
        {
            height = 1;
        }

        if (height > GNNEEnv.GlbDepth || width > GNNEEnv.GlbWidth)
        {
            return new(0, 0, 0, 0, 0);
        }

        int[] startIndex = Find_space_(width, height);
        int itemId = GetId();
        if (itemId >= 0 && startIndex[0] >= 0 && startIndex[1] >= 0)
        {
            MmuItem item = new(itemId, startIndex[0], width, startIndex[1], height);
            FillSpace(item);
            _items.Add(item);
            return item;
        }

        return new(0, 0, 0, 0, 0);
    }

    public void DisplayFinalAllocation()
    {
        _logger.LogTrace("--------------------------");
        _logger.LogTrace("GLB allocation:");
        for (int item = 0; item < _nMaxMmuNum; item++)
        {
            int startX = -1, startY = -1;
            int endX = _width, endY = _height;
            for (int i = 0; i < _height; ++i)
            {
                for (int j = 0; j < _width; ++j)
                {
                    if (_space[i, j] == item)
                    {
                        startX = j;
                        startY = i;
                        break;
                    }
                }

                if (startX >= 0)
                {
                    break;
                }
            }

            if (startX >= 0)
            {
                for (int i = startY + 1; i < _height; ++i)
                {
                    if (_space[i, startX] != item)
                    {
                        endY = i;
                        break;
                    }
                }

                for (int j = startX + 1; j < _width; ++j)
                {
                    if (_space[startY, j] != item)
                    {
                        endX = j;
                        break;
                    }
                }

                _logger.LogTrace($"{_space[startY, startX]} : ({startX}, {endX}, {endX - startX}) ({startY}, {endY}, {endY - startY} )");
            }
        }

        _logger.LogTrace("--------------------------");
    }

    private int[] Find_space_(int width, int height)
    {
        for (int i = 0; i < _height - height + 1; ++i)
        {
            for (int j = 0; j < _width - width + 1; ++j)
            {
                if (_space[i, j] != -1)
                {
                    continue;
                }

                bool found = true;
                for (int m = 0; m < height; m++)
                {
                    for (int n = 0; n < width; n++)
                    {
                        if (_space[i + m, j + n] != -1)
                        {
                            found = false;
                            break;
                        }
                    }
                }

                if (found)
                {
                    return new[] { j, i };
                }
            }
        }

        return new[] { -1, -1 };
    }

    private int GetId()
    {
        if (_items.Count == 0)
        {
            return 0;
        }

        for (int i = 0; i < _nMaxMmuNum; i++)
        {
            bool found = _items.All(t => i != t.Id);

            if (found)
            {
                return i;
            }
        }

        return -1;
    }

    private void FillSpace(MmuItem item)
    {
        for (int i = item.StartDepth; i < item.StartDepth + item.Depth; i++)
        {
            for (int j = item.StartBank; j < item.StartBank + item.Width; j++)
            {
                _space[i, j] = item.Id;
            }
        }
    }
}

internal class AllocateResult
{
    public AllocateResult(bool isOk, Dictionary<ItemName, MmuItem> items, List<BoxOnGlb> boxes, int[] lastOutShape, Dictionary<ItemName, int> bufferSize, List<NodeInfo> nodeInfo, Dictionary<ItemName, TensorOnGlb> glbMap)
    {
        IsOk = isOk;
        Items = items;
        Boxes = boxes;
        LastOutShape = lastOutShape;
        BufferSize = bufferSize;
        NodeInfo = nodeInfo;
        GlbMap = glbMap;
    }

    public AllocateResult()
    {
    }

    public bool IsOk { get; set; }

    public Dictionary<ItemName, MmuItem> Items { get; set; } = null!;

    public List<BoxOnGlb> Boxes { get; set; } = null!;

    public int[] LastOutShape { get; set; } = null!;

    public Dictionary<ItemName, int> BufferSize { get; set; } = null!;

    public List<NodeInfo> NodeInfo { get; set; } = null!;

    public Dictionary<ItemName, TensorOnGlb> GlbMap { get; set; } = null!;
}

internal class Ccr
{
    private int _item;

    private int _value;

    // int _max_value = 15;
    public Ccr(int item)
    {
        _item = item;
        _value = 0;
    }

    public int Item => _item;

    public int Value
    {
        get => _value;
        set
        {
            Assert(_value == 0);
            if (_value != 0)
            {
                throw new ArgumentOutOfRangeException($"[Error] in ccr set, ccr id:{_item}");
            }

            _value = value;
        }
    }

    public void Clear()
    {
        if (_value <= 0)
        {
            throw new ArgumentOutOfRangeException($"[Error] in ccr clr, ccr id:{_item}");
        }

        _value -= 1;
    }

    public void Acquire()
    {
        Assert(_value > 0);
    }
}
