// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

#define K230_INST_OPT
using Nncase.CodeGen.K230;
using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Tensors;
using Nncase.TIR;
using Nncase.TIR.Instructions;
using static Nncase.Passes.Rules.K230.TileUtilities;
using Buffer = Nncase.IR.F.Buffer;

namespace Nncase.Passes.Rules.K230;

public class GnneActionUpdater
{
    private readonly TiledGlb _glb;
    private readonly CcrHandler _ccrHandler;
    private readonly GprHandler _gprHandler;
    private readonly SsrHandler _ssRegHandler;
    private readonly ConfActions _latestConfActions = new();

    public GnneActionUpdater(List<GnneAction> actions, TiledGlb glb, <PERSON>cr<PERSON><PERSON><PERSON> ccrHandler, <PERSON>pr<PERSON><PERSON><PERSON> gpr, SsrHandler ssr)
    {
        Actions = actions;
        _glb = glb;
        _ccrHandler = ccrHandler;
        _gprHandler = gpr;
        _ssRegHandler = ssr;
    }

    public List<GnneAction> Actions { get; }

    public long PackStride(int n, int c, int h)
    {
        return (n * (1L << 32)) + (c * (1L << 16)) + h;
    }

    public long PackShape(int n, int c, int h, int w)
    {
        return (n * (1L << 48)) + (c * (1L << 32)) + (h * (1L << 16)) + w;
    }

    public ConfActions LatestConfActions()
    {
        return _latestConfActions;
    }

    public void UpdateEnd()
    {
        Actions.Add(new GnneActionEnd());
    }

    public void UpdateFence()
    {
        Actions.Add(new GnneActionFence());
    }

    public void UpdateIntr(int intrNum)
    {
        var rs = _gprHandler.SetGprItem(intrNum);
        Actions.Add(new GnneActionIntr(rs));
    }

    public void UpdateCcr(List<CcrSet> ccrsToSet, List<CcrClr> ccrsToClr)
    {
        // decl
        ccrsToSet ??= new List<CcrSet>();
        ccrsToClr ??= new List<CcrClr>();
        int num = ccrsToSet.Count + ccrsToClr.Count;
        if (num > 0)
        {
            var rnum = _gprHandler.SetGprItem(num);
            Actions.Add(new GnneActionCcrDecl(rnum));

            // ccr clr
            foreach (var cc in ccrsToClr)
            {
                Actions.Add(new GnneActionCcrClr(cc.Ccr));
            }

            // ccr set
            foreach (var cs in ccrsToSet)
            {
                Assert(cs.Value > 0);
                Actions.Add(new GnneActionCcrSet(cs.Ccr, cs.Value));
            }

            // ccr checker
            _ccrHandler.SetItems(ccrsToSet);
            _ccrHandler.ClearItems(ccrsToClr);
        }
    }

    public void UpdateMmuConf()
    {
        int Overlap(int x1L, int x1R, int x2L, int x2R)
        {
            int left = x1L > x2L ? x1L : x2L;
            int right = x1R < x2R ? x1R : x2R;
            return right - left;
        }

        foreach (var item in _glb.Items)
        {
            var rstart = _gprHandler.SetGprItem(item.Value.StartDepth);
            var rdepth = _gprHandler.SetGprItem(item.Value.Depth);
            foreach (var itemOther in from itemOther in _glb.Items where itemOther.Key != item.Key let lap = Overlap(item.Value.StartDepth, item.Value.StartDepth + item.Value.Depth, itemOther.Value.StartDepth, itemOther.Value.StartDepth + itemOther.Value.Depth) where lap > 0 select itemOther)
            {
                Console.WriteLine("[tiling_check]: mmu overlap happen!");
                Console.WriteLine($"mmu_a: {item.Key}, mmu_b: {itemOther.Key}");
                Console.WriteLine($"start_a: {item.Value.StartDepth}, depth_a: {item.Value.Depth}; start_b: {itemOther.Value.StartDepth}, depth_b: {itemOther.Value.Depth}");
                throw new NotSupportedException("mmu config conflicts!");
            }

            Actions.Add(new Gnne_action_mmu_conf(item.Value, rstart, rdepth));
        }
    }

    public void UpdateLoadWQarg(Call lwQarg, WeightGroupHandler weightGroup, TIR.Buffer ddrCon, int offsetD = 0, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, ItemName name = ItemName.WQarg, int offsetS = 0)
    {
        var wQargType = lwQarg[GNNELoadW.Input].CheckedDataType;
        int bytesPerElement = GetBytesPerElement(wQargType);

        int loadCnt = 0;
        foreach (var qg in weightGroup.QargGroupSlice())
        {
            loadCnt++;
            int addrS = (qg.Start * bytesPerElement) + offsetS;
            int length = qg.Length;
            int addrD = (weightGroup.QargAlignedOffset(qg) * bytesPerElement) + offsetD;

            Gpr rlenCompressed = new(-1, length, false);
            Gpr rlenDecompressed = new(-1, length, false);
            GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, wQargType, wQargType);
#if K230_INST_OPT
            if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
            {
                rlenCompressed = _gprHandler.SetGprItem(length);
                rlenDecompressed = _gprHandler.SetGprItem(length);
                l2LoadWConf = new(rlenCompressed, rlenDecompressed, wQargType, wQargType);
                _latestConfActions.L2LoadWConf = l2LoadWConf;
                Actions.Add(l2LoadWConf);
            }

            var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)addrS);
            var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrD));

            // TODO: buffer中增加basemnet的定义
            Expr basement = Buffer.BufferIndexOf(ddrCon);
            var rbasement = _gprHandler.SetGprItem1(basement);
            int validCNum = Math.Min(GNNEEnv.WAlignNum - 1, length - 1);
            var rvalidCNum = _gprHandler.SetGprItem(validCNum);
            if (loadCnt == weightGroup.QargGroupSlice().Count && GNNEEnv.UseCcr)
            {
                UpdateCcr(ccrsToSet, ccrsToClr);
            }

            Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalidCNum));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateLoadWQargMatmul(Call call, WeightGroupHandler weightGroup, TIR.Buffer ddrCon, int n, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, ItemName name = ItemName.WQarg)
    {
        var wQargType = call[GNNELoadW.Input].CheckedDataType;
        int bytesPerElement = GetBytesPerElement(wQargType);

        int loadCnt = 0;
        foreach (var qg in weightGroup.QargGroupSlice())
        {
            loadCnt++;
            int addrS = (qg.Start * bytesPerElement) + (n * weightGroup.CurrentQargOffset());
            int length = qg.Length;
            int addrD = (weightGroup.QargAlignedOffset(qg) * bytesPerElement) + (n * weightGroup.CurrentQargAlignedOffset());

            Gpr rlenCompressed = new(-1, length, false);
            Gpr rlenDecompressed = new(-1, length, false);
            GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, wQargType, wQargType);
#if K230_INST_OPT
            if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
            {
                rlenCompressed = _gprHandler.SetGprItem(length);
                rlenDecompressed = _gprHandler.SetGprItem(length);
                l2LoadWConf = new(rlenCompressed, rlenDecompressed, wQargType, wQargType);
                _latestConfActions.L2LoadWConf = l2LoadWConf;
                Actions.Add(l2LoadWConf);
            }

            var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)addrS);
            var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrD));
            Expr basemnet = Buffer.BufferIndexOf(ddrCon);
            var rbasement = _gprHandler.SetGprItem1(basemnet);
            int validCNum = Math.Min(GNNEEnv.WAlignNum - 1, length - 1);
            var rvalidCNum = _gprHandler.SetGprItem(validCNum);
            if (loadCnt == weightGroup.QargGroupSlice().Count && GNNEEnv.UseCcr)
            {
                UpdateCcr(ccrsToSet, ccrsToClr);
            }

            Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalidCNum));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateLoadDwQarg(Call call, WeightGroupHandler weightGroup, TIR.Buffer ddrCon, int offsetD = 0, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!)
    {
        var wQargType = call[GNNEPdp0DW.WeightsBias].CheckedDataType;
        int bytesPerElement = GetBytesPerElement(wQargType);

        int loadCnt = 0;
        foreach (var qg in weightGroup.DwQargGroupSlice())
        {
            loadCnt++;
            int addrS = qg.Start * bytesPerElement;
            int length = qg.Length;
            int addrD = (weightGroup.DwQargAlignedOffset(qg) * bytesPerElement) + offsetD;

            Gpr rlenCompressed = new(-1, length, false);
            Gpr rlenDecompressed = new(-1, length, false);
            GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, wQargType, wQargType);
#if K230_INST_OPT
            if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
            {
                rlenCompressed = _gprHandler.SetGprItem(length);
                rlenDecompressed = _gprHandler.SetGprItem(length);
                l2LoadWConf = new(rlenCompressed, rlenDecompressed, wQargType, wQargType);
                _latestConfActions.L2LoadWConf = l2LoadWConf;
                Actions.Add(l2LoadWConf);
            }

            var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)addrS);
            var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.DwQarg].Mmu.Id, addrD));
            Expr basemnet = Buffer.BufferIndexOf(ddrCon);
            var rbasement = _gprHandler.SetGprItem1(basemnet);
            int validCNum = Math.Min(GNNEEnv.WAlignNum - 1, length - 1);
            var rvalidCNum = _gprHandler.SetGprItem(validCNum);
            if (loadCnt == weightGroup.DwQargGroupSlice().Count && GNNEEnv.UseCcr)
            {
                UpdateCcr(ccrsToSet, ccrsToClr);
            }

            Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalidCNum));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdatePreloadW(SegmentND weight, Call call, WeightGroupHandler weightGroup, int wPp, TIR.Buffer ddrCon, int offsetD = 0, ItemName weightItem = ItemName.WeightPreload, bool h2c = false, int pos = 0)
    {
        var wType = call[GNNELoadW.Input].CheckedDataType;
        int bytesPerElement = GetBytesPerElement(wType);

        foreach (var wg in weightGroup.WeightGroupSlice())
        {
            if (wg[0].Start >= weight[0].Start && wg[1].Start >= weight[1].Start
                                               && wg[2].Start >= weight[2].Start && wg[3].Start >= weight[3].Start
                                               && wg[0].End <= weight[0].End && wg[1].End <= weight[1].End
                                               && wg[2].End <= weight[2].End && wg[3].End <= weight[3].End)
            {
                int addrS = (weightGroup.WeightGroupOffset(wg) * bytesPerElement) + (pos * weightGroup.GetShapeSize(wg));
                int length = wg.Shape_size;
                int addrD = (weightGroup.WeightGroupAlignedOffset(wg) * bytesPerElement) + (pos * weightGroup.GetAlignedShapeSize(wg));
                addrD += (wPp * _glb.GlbMap[weightItem].AllocatedBytes) + offsetD;

                Gpr rlenCompressed = new(-1, length, false);
                Gpr rlenDecompressed = new(-1, length, false);
                GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType);
#if K230_INST_OPT
                if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
                {
                    rlenCompressed = _gprHandler.SetGprItem(length);
                    rlenDecompressed = _gprHandler.SetGprItem(length);
                    l2LoadWConf = new(rlenCompressed, rlenDecompressed, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType);
                    _latestConfActions.L2LoadWConf = l2LoadWConf;
                    Actions.Add(l2LoadWConf);
                }

                var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)addrS);
                var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[weightItem].Mmu.Id, addrD));
                Expr basemnet = Buffer.BufferIndexOf(ddrCon);
                var rbasement = _gprHandler.SetGprItem1(basemnet);
                int cNum = wg[1].Length - 1;
                if (h2c)
                {
                    cNum = (wg[1].Length * wg[2].Length) - 1;
                }

                var rvalidCNum = _gprHandler.SetGprItem(cNum);
                Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalidCNum));
            }
        }

        Actions.Add(new GnneActionFence());
    }

    public void UpdateLoadW(SegmentND wg, Call call, WeightGroupHandler weightGroup, int wPp, TIR.Buffer ddrCon, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetD = 0, bool h2c = false, int offsetS = 0, ItemName name = ItemName.Weight, int pos = 0)
    {
        var wType = call[GNNELoadW.Input].CheckedDataType;
        int bytesPerElement = GetBytesPerElement(wType);

        int addrS = (weightGroup.WeightGroupOffset(wg) * bytesPerElement) + offsetS + (pos * weightGroup.GetShapeSize(wg));
        int length = wg.Shape_size;
        int addrD = (weightGroup.WeightGroupAlignedOffset(wg) * bytesPerElement) + (pos * weightGroup.GetAlignedShapeSize(wg));
        addrD += (wPp * _glb.GlbMap[name].AllocatedBytes) + offsetD;

        Gpr rlenCompressed = new(-1, length, false);
        Gpr rlenDecompressed = new(-1, length, false);
        GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType);
#if K230_INST_OPT
        if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
        {
            rlenCompressed = _gprHandler.SetGprItem(length);
            rlenDecompressed = _gprHandler.SetGprItem(length);
            l2LoadWConf = new(rlenCompressed, rlenDecompressed, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType, wType == DataTypes.Int16 ? DataTypes.UInt8 : wType);
            _latestConfActions.L2LoadWConf = l2LoadWConf;
            Actions.Add(l2LoadWConf);
        }

        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        int cNum = wg[1].Length - 1;
        if (h2c)
        {
            cNum = (wg[1].Length * wg[2].Length) - 1;
        }

        var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)addrS);
        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrD));
        Expr basemnet = Buffer.BufferIndexOf(ddrCon);
        var rbasement = _gprHandler.SetGprItem1(basemnet);
        var rvalid_c_num = _gprHandler.SetGprItem(cNum);
        Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalid_c_num));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateLoadDw(Call dw, WeightGroupHandler weightGroup, TIR.Buffer ddrCon, int offsetD = 0, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!)
    {
        var wType = ((Call)dw[GNNEPdp0DW.Weights])[GNNELoadW.Input].CheckedDataType;
        int bytesPerElement = GetBytesPerElement(wType);
        var filterH = dw[GNNEPdp0DW.Weights].CheckedShape[2];
        var filterW = dw[GNNEPdp0DW.Weights].CheckedShape[3];

        foreach (var wg in weightGroup.WeightGroupSlice())
        {
            int cs = wg[0].Start;
            int ce = wg[0].End;
            var dwWg = new SegmentND(..1, cs..ce, ..(int)filterH.FixedValue, ..(int)filterW.FixedValue);
            weightGroup.UpdateDwWeightGroup(dwWg);
        }

        int loadTotalNum = weightGroup.DwGroupSlice().Count;

        foreach (var dwWg in weightGroup.DwGroupSlice())
        {
            int addrS = weightGroup.DwGroupOffset(dwWg) * bytesPerElement;
            int addrD = (weightGroup.DwGroupAlignedOffset(dwWg) * bytesPerElement) + offsetD;

            loadTotalNum--;
            int cs = dwWg[1].Start;
            int ce = GetAlignedNum(dwWg[1].End, GNNEEnv.PuWidth);
            var l2LdDwSlice = new SegmentND(..1, cs..ce, ..(int)filterH.FixedValue, ..(int)filterW.FixedValue);
            int length = l2LdDwSlice.Shape_size;

            var rlenCompressed = new Gpr(-1, length, false);
            var rlenDecompressed = new Gpr(-1, length, false);
            GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, wType, wType);
#if K230_INST_OPT
            if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
            {
                rlenCompressed = _gprHandler.SetGprItem(length);
                rlenDecompressed = _gprHandler.SetGprItem(length);
                l2LoadWConf = new(rlenCompressed, rlenDecompressed, wType, wType);
                _latestConfActions.L2LoadWConf = l2LoadWConf;
                Actions.Add(l2LoadWConf);
            }

            var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)addrS);
            var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.DwWeight].Mmu.Id, addrD));
            Expr basemnet = Buffer.BufferIndexOf(ddrCon);
            var rbasement = _gprHandler.SetGprItem1(basemnet);
            int validCNum = Math.Min(l2LdDwSlice[1].Length - 1, GNNEEnv.PuHeight - 1);
            var rvalidCNum = _gprHandler.SetGprItem(validCNum);

            if (GNNEEnv.UseCcr && loadTotalNum == 0)
            {
                UpdateCcr(ccrsToSet, ccrsToClr);
            }

            Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalidCNum));
        }
    }

    public void UpdateLoadAct(Call ld, TIR.Buffer ddrCon, ItemName name = ItemName.Act, int offsetD = 0, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!)
    {
        var actType = ld[GNNELoadW.Input].CheckedDataType;
        var actShape = ld[GNNELoadW.Input].CheckedShape;
        const int AddrS = 0;
        int length = (int)actShape.ProdWithDynamicAsMaxValue();
        int addrD = offsetD;

        Gpr rlenCompressed = new(-1, length, false);
        Gpr rlenDecompressed = new(-1, length, false);
        GnneActionL2LoadWConf l2LoadWConf = new(rlenCompressed, rlenDecompressed, actType, actType);
#if K230_INST_OPT
        if (_latestConfActions.L2LoadWConf is null || _latestConfActions.L2LoadWConf! != l2LoadWConf)
#endif
        {
            rlenCompressed = _gprHandler.SetGprItem(length);
            rlenDecompressed = _gprHandler.SetGprItem(length);
            l2LoadWConf = new(rlenCompressed, rlenDecompressed, actType, actType);
            _latestConfActions.L2LoadWConf = l2LoadWConf;
            Actions.Add(l2LoadWConf);
        }

        var raddrS = _gprHandler.SetGprItem1(Buffer.AddressOf(ddrCon) + (ulong)AddrS);
        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrD));
        Expr basemnet = Buffer.BufferIndexOf(ddrCon);
        var rbasement = _gprHandler.SetGprItem1(basemnet);
        var rvalidCNum = _gprHandler.SetGprItem(GNNEEnv.WAlignNum - 1);

        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        Actions.Add(new GnneActionL2LoadW(rbasement, raddrD, raddrS, rvalidCNum));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateLoadIf(SegmentND ifmap, Call call, int iPp, Expr ddrIf, int offsetD = 0, List<int> stridesD = null!, ItemName name = ItemName.Ifmap, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, bool h2c = false, int bias = 0, List<int> stridesS = null!, GNNEShape shape = null!, long[] layout = null!)
    {
        var inputShape = call[GNNELoad.Input].CheckedShape;
        var ddrType = call[GNNELoad.Input].CheckedDataType;
        var glbType = call.CheckedDataType;

        if (stridesS is null)
        {
            stridesS = new[] { (int)inputShape[1].FixedValue!, (int)inputShape[2].FixedValue!, (int)inputShape[3].FixedValue! }.ToList();
        }

        if (stridesD is null)
        {
            stridesD = new[] { ifmap[1].Length, ifmap[2].Length, ifmap[3].Length }.ToList();
        }

        if (shape is null)
        {
            shape = new(ifmap[0].Length, ifmap[1].Length, ifmap[2].Length, ifmap[3].Length);
        }

        Gpr? raddrD;
        Gpr? shapeRn;
        Gpr? shapeRc;
        Gpr? shapeRh;
        Gpr? shapeRw;
        Ssr shapeRss;
        Ssr strideRssD;
        if (h2c)
        {
            if (ifmap.PadH.Before > 0)
            {
                int addrDest = (iPp * _glb.GlbMap[name].AllocatedBytes) + offsetD + _glb.GlbMap[name].GetAddr(0, 0, 0, 0);
                raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrDest));

                var rv = _gprHandler.SetGprItem(bias);

                var strideRnD = _gprHandler.SetGprItem(stridesD[0]);
                var strideRcD = _gprHandler.SetGprItem(stridesD[1]);
                var strideRhD = _gprHandler.SetGprItem(stridesD[2]);
                strideRssD = _ssRegHandler.SetSsrItem(PackStride(stridesD[0], stridesD[1], stridesD[2]));
                Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, strideRssD));

                shapeRn = _gprHandler.SetGprItem(ifmap[0].Length);
                shapeRc = _gprHandler.SetGprItem(ifmap[1].Length);
                shapeRh = _gprHandler.SetGprItem(ifmap.PadH.Before);
                shapeRw = _gprHandler.SetGprItem(ifmap[3].Length);
                shapeRss = _ssRegHandler.SetSsrItem(PackShape(ifmap[0].Length, ifmap[1].Length, ifmap.PadH.Before, ifmap[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, shapeRss));

                Actions.Add(new GnneActionMfuMemset(raddrD, rv, strideRssD, shapeRss, glbType));
            }

            if (ifmap.PadH.After > 0)
            {
                int addrDest = (iPp * _glb.GlbMap[name].AllocatedBytes) + offsetD + _glb.GlbMap[name].GetAddr(0, 0, ifmap[2].Length + ifmap.PadH.Before, 0);
                raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrDest));

                var rv = _gprHandler.SetGprItem(bias);

                var strideRnD = _gprHandler.SetGprItem(stridesD[0]);
                var strideRcD = _gprHandler.SetGprItem(stridesD[1]);
                var strideRhD = _gprHandler.SetGprItem(stridesD[2]);
                strideRssD = _ssRegHandler.SetSsrItem(PackStride(stridesD[0], stridesD[1], stridesD[2]));
                Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, strideRssD));

                shapeRn = _gprHandler.SetGprItem(ifmap[0].Length);
                shapeRc = _gprHandler.SetGprItem(ifmap[1].Length);
                shapeRh = _gprHandler.SetGprItem(ifmap.PadH.After);
                shapeRw = _gprHandler.SetGprItem(ifmap[3].Length);
                shapeRss = _ssRegHandler.SetSsrItem(PackShape(ifmap[0].Length, ifmap[1].Length, ifmap.PadH.After, ifmap[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, shapeRss));

                Actions.Add(new GnneActionMfuMemset(raddrD, rv, strideRssD, shapeRss, glbType));
            }
        }

        bool canBeReshaped = true;
        switch (call[GNNELoad.Input])
        {
            case Call { Target: Slice }:
                canBeReshaped = false;
                break;
            case Call { Target: Reshape }:
                {
                    // if (((Call)call[GNNELoad.Input])[Reshape.Input] is Var || ((Call)call[GNNELoad.Input])[Reshape.Input] is Call { Target: Slice })
                    if (((Call)call[GNNELoad.Input])[Reshape.Input] is Call { Target: Slice })
                    {
                        canBeReshaped = false;
                    }

                    break;
                }
        }

        if (canBeReshaped)
        {
            ReshapeSpecialScenario(ref stridesS, ref shape, ref stridesD);
        }

        Ssr strideRssS = new(-1, PackStride(stridesS[0], stridesS[1], stridesS[2]), false);
        strideRssD = new(-1, PackStride(stridesD[0], stridesD[1], stridesD[2]), false);
        Gnne_action_l2_load_conf l2LoadConf = new(strideRssD, strideRssS, glbType, ddrType);
#if K230_INST_OPT
        // if (_latestConfActions.l2_load_conf is null || _latestConfActions.l2_load_conf != l2_load_conf)
#endif
        {
            if (canBeReshaped)
            {
                var strideRnS = _gprHandler.SetGprItem(stridesS[0]);
                var strideRcS = _gprHandler.SetGprItem(stridesS[1]);
                var strideRhS = _gprHandler.SetGprItem(stridesS[2]);
                strideRssS = _ssRegHandler.SetSsrItem(PackStride(stridesS[0], stridesS[1], stridesS[2]));
                Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, strideRssS, (GNNELoad)call.Target));
            }
            else
            {
                var strideRnS = _gprHandler.SetGprItem1(stridesS[0]);
                var strideRcS = _gprHandler.SetGprItem1(stridesS[1]);
                var strideRhS = _gprHandler.SetGprItem1(stridesS[2]);
                strideRssS = _ssRegHandler.SetSsrItem(PackStride(stridesS[0], stridesS[1], stridesS[2]), forceNew: true);
                Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, strideRssS, (GNNELoad)call.Target));
            }

            var strideRnD = _gprHandler.SetGprItem(stridesD[0]);
            var strideRcD = _gprHandler.SetGprItem(stridesD[1]);
            var strideRhD = _gprHandler.SetGprItem(stridesD[2]);
            strideRssD = _ssRegHandler.SetSsrItem(PackStride(stridesD[0], stridesD[1], stridesD[2]), true);
            Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, strideRssD));

            l2LoadConf = new(strideRssD, strideRssS, glbType, ddrType);
            _latestConfActions.L2LoadConf = l2LoadConf;
            Actions.Add(l2LoadConf);
        }

        shapeRn = _gprHandler.SetGprItem(shape[0]);
        shapeRc = _gprHandler.SetGprItem(shape[1]);
        shapeRh = _gprHandler.SetGprItem(shape[2]);
        shapeRw = _gprHandler.SetGprItem(shape[3]);
        shapeRss = _ssRegHandler.SetSsrItem(PackShape(shape[0], shape[1], shape[2], shape[3]));
        Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, shapeRss));

        int addrD = (iPp * _glb.GlbMap[name].AllocatedBytes) + offsetD;
        if (h2c)
        {
            addrD = (iPp * _glb.GlbMap[name].AllocatedBytes) + offsetD + (ifmap[2].Padding.Before * _glb.GlbMap[name].Stride[2]);
        }

        int mmuItem = _glb.GlbMap[name].Mmu.Id;

        if (name == ItemName.LstmOfH || name == ItemName.LstmOfC)
        {
            addrD = offsetD;
            mmuItem = _glb.GlbMap[name].Mmu.Id;
        }

        var raddrS = _gprHandler.SetGprItem1(0);
        raddrD = _gprHandler.SetGprItem(ToGlbAddr(mmuItem, addrD));

        // TODO: buffer中增加basemnet的定义
        // Expr basement = ddrIf.MemLocation == MemoryLocation.Rdata ? 2 : ddrIf.MemLocation == MemoryLocation.Data ? 3 : 0;
        Expr basement = Buffer.BufferIndexOf(ddrIf);
        var rbasement = _gprHandler.SetGprItem1(basement);
        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        Actions.Add(new GnneActionL2Load(rbasement, raddrD, raddrS, shapeRss, call, ifmap, ddrIf, layout));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateG2LIf(SegmentND slice, SegmentND ifmap, Call call, int iPp, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetS = 0, DataType type = null!, ItemName name = ItemName.Ifmap, bool restored = false, int strideNReshape = 0, int strideCReshape = 0, int strideHReshape = 0, bool h2c = false, int r = 0, int uh = 1, bool slice_from = true)
    {
        var glbType = call is null ? type is null ? DataTypes.UInt8 : type : call[GNNELoad.Input].CheckedDataType;
        const L1_TYPE L1Type = L1_TYPE.@if_;

        if (restored)
        {
            int strideN = strideNReshape;
            int strideC = strideCReshape;
            int strideH = strideHReshape;
            Ssr strideRssS = new(-1, PackStride(strideN, strideC, strideH), false);
            GnneActionDmLoadL1Conf dmLoadL1Conf = new(0, 0, 0, strideRssS, glbType, L1Type);
#if K230_INST_OPT
            if (_latestConfActions.DmLoadL1Conf is null || _latestConfActions.DmLoadL1Conf! != dmLoadL1Conf)
#endif
            {
                var strideRnS = _gprHandler.SetGprItem(strideN);
                var strideRcS = _gprHandler.SetGprItem(strideC);
                var strideRhS = _gprHandler.SetGprItem(strideH);
                strideRssS = _ssRegHandler.SetSsrItem(PackStride(strideN, strideC, strideH));
                Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, strideRssS));
                dmLoadL1Conf = new(0, 0, 0, strideRssS, glbType, L1Type);
                _latestConfActions.DmLoadL1Conf = dmLoadL1Conf;
                Actions.Add(dmLoadL1Conf);
            }
        }
        else
        {
            Ssr strideRssS = new(-1, PackStride(_glb.GlbMap[name].Dimensions[1], _glb.GlbMap[name].Dimensions[2], _glb.GlbMap[name].Dimensions[3]), false);
            GnneActionDmLoadL1Conf dmLoadL1Conf = new(0, 0, 0, strideRssS, glbType, L1Type);
#if K230_INST_OPT
            if (_latestConfActions.DmLoadL1Conf is null || _latestConfActions.DmLoadL1Conf! != dmLoadL1Conf)
#endif
            {
                var strideRnS = _gprHandler.SetGprItem(_glb.GlbMap[name].Dimensions[1]);
                var strideRcS = _gprHandler.SetGprItem(_glb.GlbMap[name].Dimensions[2]);
                var strideRhS = _gprHandler.SetGprItem(_glb.GlbMap[name].Dimensions[3]);
                strideRssS = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[name].Dimensions[1], _glb.GlbMap[name].Dimensions[2], _glb.GlbMap[name].Dimensions[3]));
                Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, strideRssS));
                dmLoadL1Conf = new(0, 0, 0, strideRssS, glbType, L1Type);
                _latestConfActions.DmLoadL1Conf = dmLoadL1Conf;
                Actions.Add(dmLoadL1Conf);
            }
        }

        int hl = slice[2].Length;
        if (h2c)
        {
            hl = (int)(Math.Floor((hl - r + slice.PadH.Before + slice.PadH.After) * 1.0 / uh) + 1);
        }

        var shapeRn = _gprHandler.SetGprItem(slice[0].Length);
        var shapeRc = _gprHandler.SetGprItem(slice[1].Length);
        var shapeRh = _gprHandler.SetGprItem(hl);
        var shapeRw = _gprHandler.SetGprItem(slice[3].Length);
        var shapeRss = _ssRegHandler.SetSsrItem(PackShape(slice[0].Length, slice[1].Length, hl, slice[3].Length));
        Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, shapeRss));

        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        int addrS;
        if (restored)
        {
            int offset0 = (slice[0].Start - ifmap[0].Start) % ifmap[0].Length;
            addrS = offsetS + (iPp * _glb.GlbMap[name].AllocatedBytes) + (offset0 * _glb.GlbMap[name].Stride[0]) + (slice[2].Start * strideHReshape);
        }
        else
        {
            int offset0 = (slice[0].Start - ifmap[0].Start) % ifmap[0].Length;
            int offset1 = (slice[1].Start - ifmap[1].Start) % ifmap[1].Length;
            int offset2 = (slice[2].Start - ifmap[2].Start) % ifmap[2].Length;
            if (h2c)
            {
                offset2 = slice[2].Start - slice.PadH.Before - (ifmap[2].Start - ifmap.PadH.Before);
            }

            int offset3 = (slice[3].Start - ifmap[3].Start) % ifmap[3].Length;
            addrS = offsetS + (iPp * _glb.GlbMap[name].AllocatedBytes);
            if (slice_from)
            {
                addrS += _glb.GlbMap[name].GetAddr(offset0, offset1, offset2, offset3);
            }
        }

        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrS));
        int htocWindow = h2c ? (uh << 16) + r : 0;
        var rhtocWindow = _gprHandler.SetGprItem(htocWindow);
        Actions.Add(new GnneActionDmLoadL1(0, 0, raddrS, shapeRss, rhtocWindow, L1Type, slice));
    }

    public void UpdateG2RW(SegmentND slice, WeightGroupHandler weightGroup, int ocPerGroup, Call call, int wPp, int pos, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetWS = 0, int offsetQargS = 0, ItemName wName = ItemName.Weight, ItemName wQargName = ItemName.WQarg, bool h2c = false)
    {
        var glbType = call[GNNELoadW.Input].CheckedDataType;
        var quantType = DataTypes.Int8;
        if (glbType == DataTypes.UInt8 || (glbType == DataTypes.Int16 && pos == 0))
        {
            quantType = DataTypes.UInt8;
        }

        int kernelH = slice[2].Length;
        int kernelW = slice[3].Length;
        int strideOc = Math.Max(slice[1].Length, GNNEEnv.WAlignNum) * kernelH * kernelW;
        if (h2c)
        {
            kernelH = 1;
            strideOc = Math.Max(slice[1].Length, GNNEEnv.WAlignNum) * kernelW;
        }

        Assert(kernelH <= 31 && kernelW <= 31);
        Gpr rstrideOc = new(-1, strideOc, false);
        GnneActionDmLoadWConf dmLoadWConf = new(0, 0, 1, kernelH, kernelW, rstrideOc);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConf is null || _latestConfActions.DmLoadWConf! != dmLoadWConf)
#endif
        {
            rstrideOc = _gprHandler.SetGprItem(strideOc);
            dmLoadWConf = new(0, 0, 1, kernelH, kernelW, rstrideOc);
            _latestConfActions.DmLoadWConf = dmLoadWConf;
            Actions.Add(dmLoadWConf);
        }

        int groups = Math.Max(slice[0].Length / ocPerGroup, 1);
        int goc = Math.Min(slice[0].Length, ocPerGroup);
        Gpr rgroups = new(-1, groups, false);
        Gpr rgoc = new(-1, goc, false);
        GnneActionDmLoadWConf2 dmLoadWConf2 = new(0, 0, 5, rgroups, rgoc);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConf2 is null || _latestConfActions.DmLoadWConf2! != dmLoadWConf2!)
#endif
        {
            rgroups = _gprHandler.SetGprItem(groups);
            rgoc = _gprHandler.SetGprItem(goc);
            dmLoadWConf2 = new(0, 0, 5, rgroups, rgoc);
            _latestConfActions.DmLoadWConf2 = dmLoadWConf2;
            Actions.Add(dmLoadWConf2);
        }

        GnneActionPuWConf puWConf = new(0, 0, 5, kernelH, kernelW);
#if K230_INST_OPT
        if (_latestConfActions.PuWConf is null || _latestConfActions.PuWConf! != puWConf)
#endif
        {
            _latestConfActions.PuWConf = puWConf;
            Actions.Add(puWConf);
        }

        GnneActionDmLoadWConfDeq dmLoadWConfDeq = new(0, 0, 2, quantType);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConfDeq is null || _latestConfActions.DmLoadWConfDeq! != dmLoadWConfDeq)
#endif
        {
            _latestConfActions.DmLoadWConfDeq = dmLoadWConfDeq;
            Actions.Add(dmLoadWConfDeq);
        }

        int addrS = (wPp * _glb.GlbMap[wName].AllocatedBytes) + offsetWS;
        addrS += pos * weightGroup.GetAlignedShapeSize(slice);
        addrS += weightGroup.WeightGroupAlignedOffset(slice) * GetBytesPerElement(glbType);
        int addrBw = weightGroup.QargAlignedOffset(slice[0]) + offsetQargS;

        int oc = slice[0].Length;
        int ic = slice[1].Length;
        if (h2c)
        {
            ic = slice[1].Length * slice[2].Length;
        }

        var strideRnS = _gprHandler.SetGprItem(0);
        var strideRcS = _gprHandler.SetGprItem(ic);
        var strideRhS = _gprHandler.SetGprItem(oc);
        var rIochannels = _ssRegHandler.SetSsrItem(PackStride(0, ic, oc));
        Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rIochannels));

        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[wName].Mmu.Id, addrS));
        int wQargMmuId = quantType == DataTypes.Int8 ? 0 : _glb.GlbMap[wQargName].Mmu.Id; // if quant_type = dt_int8, mmu_id is useless.
        var raddrBw = _gprHandler.SetGprItem(ToGlbAddr(wQargMmuId, addrBw));
        Actions.Add(new GnneActionDmLoadW(0, 0, raddrS, raddrBw, rIochannels, DM_LOAD_W_DEST.pu, slice));
    }

    public void UpdateG2RWMatmul(SegmentND slice, SegmentND tensor, WeightGroupHandler weightGroup, int ocPerGroup, Call call, int wPp, int pos, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int n = 0, ItemName wName = ItemName.Weight, ItemName wQargName = ItemName.WQarg)
    {
        var glbType = call[GNNELoad.Input].CheckedDataType;
        var quantType = DataTypes.Int8;
        if (glbType == DataTypes.UInt8 || (glbType == DataTypes.Int16 && pos == 0))
        {
            quantType = DataTypes.UInt8;
        }

        int kernelH = slice[2].Length;
        int kernelW = slice[3].Length;
        int strideOc = GetAlignedNum(tensor[1].Length, GNNEEnv.WAlignNum) * kernelH * kernelW;
        Assert(kernelH == 1 && kernelW == 1);
        Gpr rstrideOc = new(-1, strideOc, false);
        GnneActionDmLoadWConf dmLoadWConf = new(0, 0, 1, kernelH, kernelW, rstrideOc);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConf is null || _latestConfActions.DmLoadWConf! != dmLoadWConf)
#endif
        {
            rstrideOc = _gprHandler.SetGprItem(strideOc);
            dmLoadWConf = new(0, 0, 1, kernelH, kernelW, rstrideOc);
            _latestConfActions.DmLoadWConf = dmLoadWConf;
            Actions.Add(dmLoadWConf);
        }

        int groups = Math.Max(slice[0].Length / ocPerGroup, 1);
        int goc = Math.Min(slice[0].Length, ocPerGroup);
        Gpr rgroups = new(-1, groups, false);
        Gpr rgoc = new(-1, goc, false);
        GnneActionDmLoadWConf2 dmLoadWConf2 = new(0, 0, 5, rgroups, rgoc);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConf2 is null || _latestConfActions.DmLoadWConf2! != dmLoadWConf2)
#endif
        {
            rgroups = _gprHandler.SetGprItem(groups);
            rgoc = _gprHandler.SetGprItem(goc);
            dmLoadWConf2 = new(0, 0, 5, rgroups, rgoc);
            _latestConfActions.DmLoadWConf2 = dmLoadWConf2;
            Actions.Add(dmLoadWConf2);
        }

        GnneActionPuWConf puWConf = new(0, 0, 5, kernelH, kernelW);
#if K230_INST_OPT
        if (_latestConfActions.PuWConf is null || _latestConfActions.PuWConf! != puWConf)
#endif
        {
            _latestConfActions.PuWConf = puWConf;
            Actions.Add(puWConf);
        }

        GnneActionDmLoadWConfDeq dmLoadWConfDeq = new(0, 0, 2, quantType);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConfDeq is null || _latestConfActions.DmLoadWConfDeq! != dmLoadWConfDeq)
#endif
        {
            _latestConfActions.DmLoadWConfDeq = dmLoadWConfDeq;
            Actions.Add(dmLoadWConfDeq);
        }

        int addrS = (wPp * _glb.GlbMap[wName].AllocatedBytes) + (n * tensor[0].Length * strideOc * GetBytesPerElement(quantType));
        addrS += pos * _glb.GlbMap[wName].AllocatedBytes / 2;
        addrS += (((slice[0].Start - tensor[0].Start) * strideOc) + (slice[1].Start - tensor[1].Start)) * GetBytesPerElement(quantType);
        int addrBw = weightGroup.QargAlignedOffset(slice[0]) + (n * weightGroup.CurrentQargAlignedOffset());

        int oc = slice[0].Length;
        int ic = slice[1].Length;

        var strideRnS = _gprHandler.SetGprItem(0);
        var strideRcS = _gprHandler.SetGprItem(ic);
        var strideRhS = _gprHandler.SetGprItem(oc);
        var rIochannels = _ssRegHandler.SetSsrItem(PackStride(0, ic, oc));
        Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rIochannels));

        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[wName].Mmu.Id, addrS));
        int wQargMmuId = quantType == DataTypes.Int8 ? 0 : _glb.GlbMap[wQargName].Mmu.Id; // if quant_type = dt_int8, mmu_id is useless.
        var raddrBw = _gprHandler.SetGprItem(ToGlbAddr(wQargMmuId, addrBw));
        Actions.Add(new GnneActionDmLoadW(0, 0, raddrS, raddrBw, rIochannels, DM_LOAD_W_DEST.pu, slice));
    }

    public void UpdateG2RDw(Call call, SegmentND slice, WeightGroupHandler weightGroup, int ocPerGroup, int offsetWS = 0, int offsetQargS = 0, List<CcrClr> dwCcrsToClr = null!)
    {
        var glbType = call[GNNEPdp0DW.Input].CheckedDataType;
        var quantType = glbType;
        int kernelH = slice[2].Length;
        int kernelW = slice[3].Length;
        int strideOc = GNNEEnv.PuWidth * kernelH * kernelW;

        Gpr rstrideOc = new(-1, strideOc, false);
        GnneActionDmLoadWConf dmLoadWConf = new(0, 0, 1, kernelH, kernelW, rstrideOc);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConf is null || _latestConfActions.DmLoadWConf! != dmLoadWConf)
#endif
        {
            rstrideOc = _gprHandler.SetGprItem(strideOc);
            dmLoadWConf = new(0, 0, 1, kernelH, kernelW, rstrideOc);
            _latestConfActions.DmLoadWConf = dmLoadWConf;
            Actions.Add(dmLoadWConf);
        }

        int groups = Math.Max(slice[0].Length / ocPerGroup, 1);
        int goc = Math.Min(slice[0].Length, ocPerGroup);
        Gpr rgroups = new(-1, groups, false);
        Gpr rgoc = new(-1, goc, false);
        GnneActionDmLoadWConf2 dmLoadWConf2 = new(0, 0, 5, rgroups, rgoc);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConf2 is null || _latestConfActions.DmLoadWConf2! != dmLoadWConf2)
#endif
        {
            rgroups = _gprHandler.SetGprItem(groups);
            rgoc = _gprHandler.SetGprItem(goc);
            dmLoadWConf2 = new(0, 0, 5, rgroups, rgoc);
            _latestConfActions.DmLoadWConf2 = dmLoadWConf2;
            Actions.Add(dmLoadWConf2);
        }

        GnneActionPuPdp0WConf pdp0WConf = new(0, 0, 6, kernelH, kernelW);
#if K230_INST_OPT
        if (_latestConfActions.Pdp0WConf is null || _latestConfActions.Pdp0WConf! != pdp0WConf)
#endif
        {
            _latestConfActions.Pdp0WConf = pdp0WConf;
            Actions.Add(pdp0WConf);
        }

        GnneActionDmLoadWConfDeq dmLoadWConfDeq = new(0, 0, 2, quantType);
#if K230_INST_OPT
        if (_latestConfActions.DmLoadWConfDeq is null || _latestConfActions.DmLoadWConfDeq! != dmLoadWConfDeq)
#endif
        {
            _latestConfActions.DmLoadWConfDeq = dmLoadWConfDeq;
            Actions.Add(dmLoadWConfDeq);
        }

        int addrS = (weightGroup.DwGroupAlignedOffset(slice) * GetBytesPerElement(quantType)) + offsetWS;
        int addrBw = weightGroup.DwQargAlignedOffset(slice[1]) + offsetQargS;

        int oc = 1;
        int ic = slice[0].Length * slice[1].Length;

        var strideRnS = _gprHandler.SetGprItem(0);
        var strideRcS = _gprHandler.SetGprItem(ic);
        var strideRhS = _gprHandler.SetGprItem(oc);
        var rIochannels = _ssRegHandler.SetSsrItem(PackStride(0, ic, oc));
        Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rIochannels));

        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.DwWeight].Mmu.Id, addrS));
        int dwQargMmuId = quantType == DataTypes.Int8 ? 0 : _glb.GlbMap[ItemName.DwQarg].Mmu.Id; // if quant_type = dt_int8, mmu_id is useless.
        var raddrBw = _gprHandler.SetGprItem(ToGlbAddr(dwQargMmuId, addrBw));
        if (GNNEEnv.UseCcr)
        {
            List<CcrSet> dwCcrsToSet = new();
            UpdateCcr(dwCcrsToSet, dwCcrsToClr);
        }

        Actions.Add(new GnneActionDmLoadW(0, 0, raddrS, raddrBw, rIochannels, DM_LOAD_W_DEST.pdp0, slice));
    }

    public void UpdateL2RIf(SegmentND slice, SegmentND ifmap, int strideH, int strideW, int icPerGroup, Call call, int padValue, int pos, int bias, DataType type = null!, bool h2c = false, int r = 0)
    {
        var glbType = call is null ? type is null ? DataTypes.UInt8 : type : call[GNNELoad.Input].CheckedDataType;
        var quantType = DataTypes.Int8;
        if (glbType == DataTypes.UInt8 || (glbType == DataTypes.Int16 && pos == 0))
        {
            quantType = DataTypes.UInt8;
        }

        int strideL1N = 24;
        int strideL1C = ifmap[2].Length;
        int strideL1H = ifmap[3].Length;

        int sh = strideH;
        if (h2c)
        {
            sh = 1;
            strideL1C = (int)(Math.Floor((slice[2].Length - r + slice.PadH.Before + slice.PadH.After) * 1.0 / strideH) + 1);
        }

        Ssr rstrideS = new(-1, PackStride(strideL1N, strideL1C, strideL1H), false);
        GnneActionPuFetchifConf1 fetchifConf1 = new(0, 0, 0, strideW, sh, rstrideS);
#if K230_INST_OPT
        if (_latestConfActions.FetchifConf1 is null || _latestConfActions.FetchifConf1! != fetchifConf1)
#endif
        {
            var strideRnS = _gprHandler.SetGprItem(strideL1N);
            var strideRcS = _gprHandler.SetGprItem(strideL1C);
            var strideRhS = _gprHandler.SetGprItem(strideL1H);
            rstrideS = _ssRegHandler.SetSsrItem(PackStride(strideL1N, strideL1C, strideL1H));
            Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstrideS));
            fetchifConf1 = new(0, 0, 0, strideW, sh, rstrideS);
            _latestConfActions.FetchifConf1 = fetchifConf1;
            Actions.Add(fetchifConf1);
        }

        int groups = Math.Max(ifmap[1].Length / icPerGroup, 1);
        int gic = Math.Min(slice[1].Length, icPerGroup);
        if (h2c)
        {
            gic = gic * r;
        }

        Gpr rgic = new(-1, gic, false);
        GnneActionPuFetchifConf2 fetchifConf2 = new(0, 0, 1, rgic, new(-1, -1, false));
#if K230_INST_OPT
        if (_latestConfActions.FetchifConf2 is null || _latestConfActions.FetchifConf2! != fetchifConf2)
#endif
        {
            rgic = _gprHandler.SetGprItem(gic);
            fetchifConf2 = new(0, 0, 1, rgic, new(-1, -1, false));
            _latestConfActions.FetchifConf2 = fetchifConf2;
            Actions.Add(fetchifConf2);
        }

        int sliceC = slice[1].Length;
        int sliceH = slice[2].Length + slice.PadH.Sum();
        int sliceW = slice[3].Length + slice.PadW.Sum();
        if (h2c)
        {
            sliceH = strideL1C;
            sliceC = sliceC * r;
        }

        int offsetH = slice[2].Start - ifmap[2].Start;
        int offsetW = slice[3].Start - ifmap[3].Start;
        int addrS = (ifmap[3].Length * offsetH) + offsetW;
        if (pos == 1)
        {
            addrS += GNNEEnv.IfL1Size / GNNEEnv.PuHeight / 2;
        }

        Ssr rshape = new(-1, PackShape(slice[0].Length, sliceC, sliceH, sliceW), false);
        Gpr raddrS = new(-1, addrS, false);
        Gpr rgroups = new(-1, groups, false);
        GnneActionPuFetchifConf3 fetchifConf3 = new(0, 0, 2, raddrS, rgroups, rshape);
#if K230_INST_OPT
        if (_latestConfActions.FetchifConf3 is null || _latestConfActions.FetchifConf3! != fetchifConf3)
#endif
        {
            var shapeRn = _gprHandler.SetGprItem(slice[0].Length);
            var shapeRc = _gprHandler.SetGprItem(sliceC);
            var shapeRh = _gprHandler.SetGprItem(sliceH);
            var shapeRw = _gprHandler.SetGprItem(sliceW);
            rshape = _ssRegHandler.SetSsrItem(PackShape(slice[0].Length, sliceC, sliceH, sliceW));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

            raddrS = _gprHandler.SetGprItem(addrS);
            rgroups = _gprHandler.SetGprItem(groups);
            fetchifConf3 = new(0, 0, 2, raddrS, rgroups, rshape);
            _latestConfActions.FetchifConf3 = fetchifConf3;
            Actions.Add(fetchifConf3);
        }

        Ssr sspad = new(-1, -1, false);
        if (slice[2].Length == 0 || slice[3].Length == 0)
        {
            Gpr rpad_value = new(-1, padValue, false);
            sspad = new(-1, PackShape(sliceH, 0, sliceW, 0), false);
            GnneActionPuFetchifConf4 fetchifConf4 = new(0, 0, 3, rpad_value, sspad);
#if K230_INST_OPT
            if (_latestConfActions.FetchifConf4 is null || _latestConfActions.FetchifConf4! != fetchifConf4)
#endif
            {
                rpad_value = _gprHandler.SetGprItem(padValue);
                var shapeRn = _gprHandler.SetGprItem(sliceH);
                var shapeRc = _gprHandler.SetGprItem(0);
                var shapeRh = _gprHandler.SetGprItem(sliceW);
                var shapeRw = _gprHandler.SetGprItem(0);
                sspad = _ssRegHandler.SetSsrItem(PackShape(sliceH, 0, sliceW, 0));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, sspad));
                fetchifConf4 = new(0, 0, 3, rpad_value, sspad);
                _latestConfActions.FetchifConf4 = fetchifConf4;
                Actions.Add(fetchifConf4);
            }
        }
        else
        {
            if (h2c)
            {
                Gpr rpad_value = new(-1, padValue, false);
                sspad = new(-1, PackShape(0, 0, slice.PadW.Before, slice.PadW.After), false);
                GnneActionPuFetchifConf4 fetchifConf4 = new(0, 0, 3, rpad_value, sspad);
#if K230_INST_OPT
                if (_latestConfActions.FetchifConf4 is null || _latestConfActions.FetchifConf4! != fetchifConf4)
#endif
                {
                    rpad_value = _gprHandler.SetGprItem(padValue);
                    var shapeRn = _gprHandler.SetGprItem(0);
                    var shapeRc = _gprHandler.SetGprItem(0);
                    var shapeRh = _gprHandler.SetGprItem(slice.PadW.Before);
                    var shapeRw = _gprHandler.SetGprItem(slice.PadW.After);
                    sspad = _ssRegHandler.SetSsrItem(PackShape(0, 0, slice.PadW.Before, slice.PadW.After));
                    Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, sspad));
                    fetchifConf4 = new(0, 0, 3, rpad_value, sspad);
                    _latestConfActions.FetchifConf4 = fetchifConf4;
                    Actions.Add(fetchifConf4);
                }
            }
            else
            {
                Gpr rpadValue = new(-1, padValue, false);
                sspad = new(-1, PackShape(slice.PadH.Before, slice.PadH.After, slice.PadW.Before, slice.PadW.After), false);
                GnneActionPuFetchifConf4 fetchifConf4 = new(0, 0, 3, rpadValue, sspad);
#if K230_INST_OPT
                if (_latestConfActions.FetchifConf4 is null || _latestConfActions.FetchifConf4! != fetchifConf4)
#endif
                {
                    rpadValue = _gprHandler.SetGprItem(padValue);
                    var shapeRn = _gprHandler.SetGprItem(slice.PadH.Before);
                    var shapeRc = _gprHandler.SetGprItem(slice.PadH.After);
                    var shapeRh = _gprHandler.SetGprItem(slice.PadW.Before);
                    var shapeRw = _gprHandler.SetGprItem(slice.PadW.After);
                    sspad = _ssRegHandler.SetSsrItem(PackShape(slice.PadH.Before, slice.PadH.After, slice.PadW.Before, slice.PadW.After));
                    Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, sspad));
                    fetchifConf4 = new(0, 0, 3, rpadValue, sspad);
                    _latestConfActions.FetchifConf4 = fetchifConf4;
                    Actions.Add(fetchifConf4);
                }
            }
        }

        int ic = 0b11111;
        Gpr ric = new(-1, ic, false);
        Gpr rbx = new(-1, bias, false);
        GnneActionPuFetchifConfDeq fetchifConfDeq = new(0, 0, 4, ric, rbx, quantType);
#if K230_INST_OPT
        if (_latestConfActions.FetchifConfDeq is null || _latestConfActions.FetchifConfDeq! != fetchifConfDeq)
#endif
        {
            ric = _gprHandler.SetGprItem(ic);
            rbx = _gprHandler.SetGprItem(bias);
            fetchifConfDeq = new(0, 0, 4, ric, rbx, quantType);
            _latestConfActions.FetchifConfDeq = fetchifConfDeq;
            Actions.Add(fetchifConfDeq);
        }
    }

    public void UpdateR2LPsum(int shift, SegmentND r2LPsum, SegmentND l2GOf, SegmentND storeOf, int ofPp, int l1Pp, ACT0_OUTPUT_DEST destTarget, bool releaseIf, int pos, TcuComputeMode tcuMode, bool loopStart, bool loopEnd, int strideH, int strideW, int ocPerGroup, DataType ifType, DataType wType, DataType ofType, DataType actType, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetAct = 0, int offsetL2GOf = 0, List<CcrClr> actCcrToClr = null!, ItemName act0Name = ItemName.Act, bool l2g_of_slice_from = true)
    {
        if (loopEnd)
        {
            UpdateDmLoadAct0(l2GOf, tcuMode, true, ACT0_CHANNEL.pu, actType, act0Name, offsetAct, actCcrToClr);
            if (destTarget == ACT0_OUTPUT_DEST.dm)
            {
                UpdateDmStoreOf(l2GOf, storeOf, ofPp, ofType, ACT0_CHANNEL.pu, ccrsToSet, ccrsToClr, offsetL2GOf, l2g_of_slice_from);
            }

            List<CcrSet> act0CcrSet = new();
            if (destTarget == ACT0_OUTPUT_DEST.psum)
            {
                act0CcrSet.Add(new CcrSet(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Psum, l1Pp)), 1));
            }

            UpdateAct0Compute(l2GOf, shift, l1Pp, ifType, wType, ofType, destTarget, ACT0_CHANNEL.pu, true, act0CcrSet);
        }

        int goc = Math.Min(r2LPsum[1].Length, ocPerGroup);
        Gpr strideRhS = new(-1, -1, false);
        Ssr rstrideD = new(-1, -1, false);
        if (tcuMode == TcuComputeMode.TransposeConv2d)
        {
            Gpr rgoc = new(-1, goc, false);
            rstrideD = new(-1, PackStride(l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length * strideH), false);
            GnneActionPuOfConf1 puOfConf1 = new(0, 0, 6, rgoc, new(-1, -1, false), rstrideD);
#if K230_INST_OPT
            if (_latestConfActions.PuOfConf1 is null || _latestConfActions.PuOfConf1! != puOfConf1)
#endif
            {
                var strideRnS = _gprHandler.SetGprItem(l2GOf[1].Length);
                var strideRcS = _gprHandler.SetGprItem(l2GOf[2].Length);
                strideRhS = _gprHandler.SetGprItem(l2GOf[3].Length * strideH);
                rstrideD = _ssRegHandler.SetSsrItem(PackStride(l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length * strideH));
                Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstrideD));
                rgoc = _gprHandler.SetGprItem(goc);
                puOfConf1 = new(0, 0, 6, rgoc, new(-1, -1, false), rstrideD);
                _latestConfActions.PuOfConf1 = puOfConf1;
                Actions.Add(puOfConf1);
            }
        }
        else
        {
            Gpr rgoc = new(-1, goc, false);
            rstrideD = new(-1, PackStride(r2LPsum[1].Length, r2LPsum[2].Length, r2LPsum[3].Length), false);
            GnneActionPuOfConf1 puOfConf1 = new(0, 0, 6, rgoc, new(-1, -1, false), rstrideD);
#if K230_INST_OPT
            if (_latestConfActions.PuOfConf1 is null || _latestConfActions.PuOfConf1! != puOfConf1)
#endif
            {
                var strideRnS = _gprHandler.SetGprItem(r2LPsum[1].Length);
                var strideRcS = _gprHandler.SetGprItem(r2LPsum[2].Length);
                strideRhS = _gprHandler.SetGprItem(r2LPsum[3].Length);
                rstrideD = _ssRegHandler.SetSsrItem(PackStride(r2LPsum[1].Length, r2LPsum[2].Length, r2LPsum[3].Length));
                Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstrideD));
                rgoc = _gprHandler.SetGprItem(goc);
                puOfConf1 = new(0, 0, 6, rgoc, new(-1, -1, false), rstrideD);
                _latestConfActions.PuOfConf1 = puOfConf1;
                Actions.Add(puOfConf1);
            }
        }

        int sh = 0;
        int sw = 0;
        if (tcuMode == TcuComputeMode.TransposeConv2d)
        {
            sh = r2LPsum[2].Start - l2GOf[2].Start;
            sw = r2LPsum[3].Start - l2GOf[3].Start;
        }

        int offset = ((sh * l2GOf[3].Length) + sw + (l1Pp * GNNEEnv.PsumL1ElePerChan / 2)) * 4;

        Gpr shapeRh = new(-1, -1, false);
        Gpr shapeRw = new(-1, -1, false);
        Ssr rshape = new(-1, -1, false);
        if (tcuMode == TcuComputeMode.TransposeConv2d)
        {
            Gpr raddrD = new(-1, offset, false);
            rshape = new(-1, PackShape(r2LPsum[0].Length, r2LPsum[1].Length, (int)Math.Ceiling(1.0 * r2LPsum[2].Length / strideH), (int)Math.Ceiling(1.0 * r2LPsum[3].Length / strideW)), false);
            GnneActionPuOfConf2 puOfConf2 = new(0, 0, 7, raddrD, rshape);
#if K230_INST_OPT
            if (_latestConfActions.PuOfConf2 is null || _latestConfActions.PuOfConf2! != puOfConf2)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(r2LPsum[0].Length);
                var shapeRc = _gprHandler.SetGprItem(r2LPsum[1].Length);
                shapeRh = _gprHandler.SetGprItem((int)Math.Ceiling(1.0 * r2LPsum[2].Length / strideH));
                shapeRw = _gprHandler.SetGprItem((int)Math.Ceiling(1.0 * r2LPsum[3].Length / strideW));
                rshape = _ssRegHandler.SetSsrItem(PackShape(r2LPsum[0].Length, r2LPsum[1].Length, (int)Math.Ceiling(1.0 * r2LPsum[2].Length / strideH), (int)Math.Ceiling(1.0 * r2LPsum[3].Length / strideW)));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));
                raddrD = _gprHandler.SetGprItem(offset);
                puOfConf2 = new(0, 0, 7, raddrD, rshape);
                _latestConfActions.PuOfConf2 = puOfConf2;
                Actions.Add(puOfConf2);
            }
        }
        else
        {
            Gpr raddrD = new(-1, offset, false);
            rshape = new(-1, PackShape(r2LPsum[0].Length, r2LPsum[1].Length, r2LPsum[2].Length, r2LPsum[3].Length), false);
            GnneActionPuOfConf2 puOfConf2 = new(0, 0, 7, raddrD, rshape);
#if K230_INST_OPT
            if (_latestConfActions.PuOfConf2 is null || _latestConfActions.PuOfConf2! != puOfConf2)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(r2LPsum[0].Length);
                var shapeRc = _gprHandler.SetGprItem(r2LPsum[1].Length);
                shapeRh = _gprHandler.SetGprItem(r2LPsum[2].Length);
                shapeRw = _gprHandler.SetGprItem(r2LPsum[3].Length);
                rshape = _ssRegHandler.SetSsrItem(PackShape(r2LPsum[0].Length, r2LPsum[1].Length, r2LPsum[2].Length, r2LPsum[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));
                raddrD = _gprHandler.SetGprItem(offset);
                puOfConf2 = new(0, 0, 7, raddrD, rshape);
                _latestConfActions.PuOfConf2 = puOfConf2;
                Actions.Add(puOfConf2);
            }
        }

        bool loadPsum = !loopStart;
        bool clrPsum = loopStart && tcuMode == TcuComputeMode.TransposeConv2d;
        var computeMode = tcuMode == TcuComputeMode.TransposeConv2d ? PU_COMPUTE_MODE.pu_mode_deconv : PU_COMPUTE_MODE.pu_mode_normal;
        var puDestTarget = loopEnd && computeMode != PU_COMPUTE_MODE.pu_mode_deconv ? PU_OUTPUT_DEST.act0 : PU_OUTPUT_DEST.psum;
        GnneActionPuComputeConf puComputeConf = new(0, 0, 8, loadPsum, clrPsum, puDestTarget, releaseIf, computeMode);
#if K230_INST_OPT
        if (_latestConfActions.PuComputeConf is null || _latestConfActions.PuComputeConf! != puComputeConf)
#endif
        {
            _latestConfActions.PuComputeConf = puComputeConf;
            Actions.Add(puComputeConf);
        }

        var shiftMode = PU_OF_SHIFT_MODE.none;
        if (ifType == DataTypes.Int16 || wType == DataTypes.Int16)
        {
            shiftMode = pos == 0 ? PU_OF_SHIFT_MODE.right_shift_4 : PU_OF_SHIFT_MODE.left_shift_4;
        }

        Actions.Add(new GnneActionPuCompute(0, shiftMode));

        if (tcuMode == TcuComputeMode.TransposeConv2d && loopEnd)
        {
            int addr = 0;
            int len = l2GOf[2].Length * l2GOf[3].Length;
            var raddr = _gprHandler.SetGprItem(addr);
            var rlen = _gprHandler.SetGprItem(len);
            Actions.Add(new GnneActionPuForwardPsum(0, 0, raddr, rlen));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateL2RPsum(Call dw, Call pool, Call act1, SegmentND l2RIf2, SegmentND ifmap2, SegmentND l2RDw, SegmentND l2RPsum, SegmentND l2GOf, SegmentND storeOf, int ofPp, int l1Pp, ACT0_OUTPUT_DEST destTarget, TcuComputeMode tcuMode, int ocPerGroup, WeightGroupHandler weightGroup, List<CcrClr> l2RIf2CcrsToClr = null!, List<CcrSet> ofCcrsToSet = null!, List<CcrClr> ofCcrsToClr = null!, int offsetSSrc2 = 0, int offsetAct = 0, int offsetAct1 = 0, int offsetOfmap = 0, int offsetSDw = 0, int offsetSDwQarg = 0, ItemName src2ItemName = ItemName.Ifmap2, List<CcrClr> dwCcrsToClr = null!, List<CcrClr> act1CcrsToClr = null!, bool swapAB = false)
    {
        if (act1 != null)
        {
            var input1Type = act1[GNNEActivation.InputA].CheckedDataType;
            var input2Type = input1Type;
            if (act1[GNNEActivation.InputB] != None.Default)
            {
                input2Type = act1[GNNEActivation.InputB].CheckedDataType;
            }

            var outputType = act1.CheckedDataType;
            var deqParams1 = ((TensorConst)act1[GNNEActivation.DeqAParams]).Value.ToArray<DeQuantizeParam>()[0];
            var deqParams2 = ((TensorConst)act1[GNNEActivation.DeqBParams]).Value.ToArray<DeQuantizeParam>()[0];
            int rshiftBits1 = ((TensorConst)act1[GNNEActivation.InAShiftBits]).Value.ToScalar<int>();
            int rshiftBits2 = ((TensorConst)act1[GNNEActivation.InBShiftBits]).Value.ToScalar<int>();
            int rshiftBitsD = ((TensorConst)act1[GNNEActivation.OutShiftBits]).Value.ToScalar<int>();
            if (act1[GNNEActivation.InputA] is Call { Target: GNNELoad } || swapAB)
            {
                input1Type = act1[GNNEActivation.InputB].CheckedDataType;
                input2Type = act1[GNNEActivation.InputA].CheckedDataType;
                deqParams1 = ((TensorConst)act1[GNNEActivation.DeqBParams]).Value.ToArray<DeQuantizeParam>()[0];
                deqParams2 = ((TensorConst)act1[GNNEActivation.DeqAParams]).Value.ToArray<DeQuantizeParam>()[0];
                rshiftBits1 = ((TensorConst)act1[GNNEActivation.InBShiftBits]).Value.ToScalar<int>();
                rshiftBits2 = ((TensorConst)act1[GNNEActivation.InAShiftBits]).Value.ToScalar<int>();
            }

            UpdateMfuAct1InFuse(l2RPsum, l2RIf2, ifmap2, l2GOf, storeOf, ACT1_SOURCE_TYPE.psum, ACT1_SOURCE_TYPE.l2, input1Type, input2Type, outputType, deqParams1, deqParams2, rshiftBits1, rshiftBits2, rshiftBitsD, ((TensorConst)act1[GNNEActivation.Is16Segments]).Value.ToScalar<bool>(), ofPp, l1Pp, l2RIf2CcrsToClr, ofCcrsToSet, ofCcrsToClr, offsetSSrc2, offsetAct1, offsetOfmap, src2ItemName, act1CcrsToClr, ((GNNEActivation)act1.Target).Type == GnneActivationType.Mul ? MFU_ACT1_FUNCTION.mul : MFU_ACT1_FUNCTION.add);
        }
        else
        {
            var mode = PU_PDP0_MODE.dw;
            DataType actType = DataTypes.UInt8, ifType = DataTypes.UInt8, wType = DataTypes.UInt8, ofType = DataTypes.UInt8;
            int shift = 0;
            int kernelH = 1;
            int kernelW = 1;
            int strideH = 1;
            int strideW = 1;
            int value = 0;
            int bias = 0;
            var act1Name = ItemName.DwAct1;
            if (dw != null)
            {
                actType = ((Call)dw[GNNEPdp0DW.Act])[GNNELoadW.Input].CheckedDataType;
                ifType = dw[GNNEPdp0DW.Input].CheckedDataType;
                wType = ((Call)dw[GNNEPdp0DW.Weights])[GNNELoadW.Input].CheckedDataType;
                ofType = dw.CheckedDataType;
                shift = ((TensorConst)dw[GNNEPdp0DW.ShiftBits]).Value.ToScalar<int>();
                kernelH = (int)dw[GNNEPdp0DW.Weights].CheckedShape[2].FixedValue;
                kernelW = (int)dw[GNNEPdp0DW.Weights].CheckedShape[3].FixedValue;
                value = ((TensorConst)dw[GNNEPdp0DW.PadValue]).Value.ToScalar<int>();

                bias = ((TensorConst)dw[GNNEPdp0DW.DeqBias]).Value.ToScalar<int>();
                strideH = ((TensorConst)dw[GNNEPdp0DW.Stride]).Value.ToArray<int>()[0];
                strideW = ((TensorConst)dw[GNNEPdp0DW.Stride]).Value.ToArray<int>()[1];
                act1Name = ItemName.DwAct1;
            }
            else if (pool != null)
            {
                actType = ((Call)pool[GNNEPdp0Reduce.Act])[GNNELoadW.Input].CheckedDataType;
                ifType = pool[GNNEPdp0Reduce.Input].CheckedDataType;
                ofType = pool.CheckedDataType;
                shift = ((TensorConst)pool[GNNEPdp0Reduce.ShiftBits]).Value.ToScalar<int>();
                kernelH = ((TensorConst)pool[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[0];
                kernelW = ((TensorConst)pool[GNNEPdp0Reduce.Filter]).Value.ToArray<int>()[1];
                mode = ((GNNEPdp0Reduce)pool.Target).ReduceOp;
                value = ((TensorConst)pool[GNNEPdp0Reduce.Value]).Value.ToScalar<int>();

                bias = ((TensorConst)pool[GNNEPdp0Reduce.DepuantParams]).Value.ToScalar<DeQuantizeParam>().ZeroPoint;
                strideH = ((TensorConst)pool[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[0];
                strideW = ((TensorConst)pool[GNNEPdp0Reduce.Stride]).Value.ToArray<int>()[1];
                act1Name = ItemName.PdpAct1;
            }

            int c = l2RPsum[1].Length;
            int h = l2RPsum[2].Length;
            int w = l2RPsum[3].Length;
            int m = l2GOf[1].Length;
            int e = l2GOf[2].Length;
            int f = l2GOf[3].Length;
            var ph = l2RPsum[2].Padding;
            var pw = l2RPsum[3].Padding;

            int padValue = value;
            if (ifType == DataTypes.UInt8)
            {
                padValue = value - bias;
            }

            if (pool != null)
            {
                GnneActionPuPdp0WConf pdp0WConf = new(0, 0, 6, kernelH, kernelW);
#if K230_INST_OPT
                if (_latestConfActions.Pdp0WConf is null || _latestConfActions.Pdp0WConf! != pdp0WConf)
#endif
                {
                    _latestConfActions.Pdp0WConf = pdp0WConf;
                    Actions.Add(pdp0WConf);
                }
            }

            UpdateDmLoadAct0(l2GOf, tcuMode, true, ACT0_CHANNEL.pdp0, actType, act1Name, offsetAct, act1CcrsToClr);
            UpdateDmStoreOf(l2GOf, storeOf, ofPp, ofType, ACT0_CHANNEL.pdp0, ofCcrsToSet, ofCcrsToClr, offsetOfmap);
            UpdateAct0Compute(l2GOf, shift, l1Pp, ifType, wType, ofType, destTarget, ACT0_CHANNEL.pdp0, true);

            GnneActionPuPdp0ModeConf pdp0ModeConf = new(0, 0, 0, mode);
#if K230_INST_OPT
            if (_latestConfActions.Pdp0ModeConf is null || _latestConfActions.Pdp0ModeConf! != pdp0ModeConf)
#endif
            {
                _latestConfActions.Pdp0ModeConf = pdp0ModeConf;
                Actions.Add(pdp0ModeConf);
            }

            GnneActionPuPdp0FetchifConf1 pdp0FetchifConf1 = new(0, 0, 1, strideW, strideH);
#if K230_INST_OPT
            if (_latestConfActions.Pdp0FetchifConf1 is null || _latestConfActions.Pdp0FetchifConf1! != pdp0FetchifConf1)
#endif
            {
                _latestConfActions.Pdp0FetchifConf1 = pdp0FetchifConf1;
                Actions.Add(pdp0FetchifConf1);
            }

            Gpr rgic = new(-1, c, false);
            GnneActionPuPdp0FetchifConf2 pdp0FetchifConf2 = new(0, 0, 2, rgic, new(-1, -1, false));
#if K230_INST_OPT
            if (_latestConfActions.Pdp0FetchifConf2 is null || _latestConfActions.Pdp0FetchifConf2! != pdp0FetchifConf2)
#endif
            {
                rgic = _gprHandler.SetGprItem(c);
                pdp0FetchifConf2 = new(0, 0, 2, rgic, new(-1, -1, false));
                _latestConfActions.Pdp0FetchifConf2 = pdp0FetchifConf2;
                Actions.Add(pdp0FetchifConf2);
            }

            Ssr rshape = new(-1, PackShape(1, c, h + ph.Sum(), w + pw.Sum()), false);
            GnneActionPuPdp0FetchifConf3 pdp0FetchifConf3 = new(0, 0, 3, rshape);
#if K230_INST_OPT
            if (_latestConfActions.Pdp0FetchifConf3 is null || _latestConfActions.Pdp0FetchifConf3! != pdp0FetchifConf3)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(1);
                var shapeRc = _gprHandler.SetGprItem(c);
                var shapeRh = _gprHandler.SetGprItem(h + ph.Sum());
                var shapeRw = _gprHandler.SetGprItem(w + pw.Sum());
                rshape = _ssRegHandler.SetSsrItem(PackShape(1, c, h + ph.Sum(), w + pw.Sum()));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

                pdp0FetchifConf3 = new(0, 0, 3, rshape);
                _latestConfActions.Pdp0FetchifConf3 = pdp0FetchifConf3;
                Actions.Add(pdp0FetchifConf3);
            }

            Gpr rpad_value = new(-1, padValue, false);
            Ssr sspad = new(-1, PackShape(ph.Before, ph.After, pw.Before, pw.After), false);
            GnneActionPuPdp0FetchifConf4 pdp0FetchifConf4 = new(0, 0, 4, rpad_value, sspad);
#if K230_INST_OPT
            if (_latestConfActions.Pdp0FetchifConf4 is null || _latestConfActions.Pdp0FetchifConf4! != pdp0FetchifConf4)
#endif
            {
                rpad_value = _gprHandler.SetGprItem(padValue);
                var shapeRn = _gprHandler.SetGprItem(ph.Before);
                var shapeRc = _gprHandler.SetGprItem(ph.After);
                var shapeRh = _gprHandler.SetGprItem(pw.Before);
                var shapeRw = _gprHandler.SetGprItem(pw.After);
                sspad = _ssRegHandler.SetSsrItem(PackShape(ph.Before, ph.After, pw.Before, pw.After));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, sspad));

                pdp0FetchifConf4 = new(0, 0, 4, rpad_value, sspad);
                _latestConfActions.Pdp0FetchifConf4 = pdp0FetchifConf4;
                Actions.Add(pdp0FetchifConf4);
            }

            Gpr rbx = new(-1, bias, false);
            GnneActionPuPdp0ConfDeq pdp0ConfDeq = new(0, 0, 5, rbx, ifType);
#if K230_INST_OPT
            if (_latestConfActions.Pdp0ConfDeq is null || _latestConfActions.Pdp0ConfDeq! != pdp0ConfDeq)
#endif
            {
                rbx = _gprHandler.SetGprItem(bias);
                pdp0ConfDeq = new(0, 0, 5, rbx, ifType);
                _latestConfActions.Pdp0ConfDeq = pdp0ConfDeq;
                Actions.Add(pdp0ConfDeq);
            }

            Ssr rstrideD = new(-1, PackStride(1, m, e), false);
            Ssr rshapeD = new(-1, PackShape(1, m, e, f), false);
            GnneActionPuPdp0OfConf pdp0OfConf = new(0, 0, 7, rstrideD, rshapeD);
#if K230_INST_OPT
            if (_latestConfActions.Pdp0OfConf is null || _latestConfActions.Pdp0OfConf! != pdp0OfConf)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(1);
                var shapeRc = _gprHandler.SetGprItem(m);
                var shapeRh = _gprHandler.SetGprItem(e);
                var shapeRw = _gprHandler.SetGprItem(f);
                rstrideD = _ssRegHandler.SetSsrItem(PackStride(1, m, e));
                Actions.Add(new GnneActionPackStrideReg(shapeRn, shapeRc, shapeRh, rstrideD));
                rshapeD = _ssRegHandler.SetSsrItem(PackShape(1, m, e, f));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshapeD));

                pdp0OfConf = new(0, 0, 7, rstrideD, rshapeD);
                _latestConfActions.Pdp0OfConf = pdp0OfConf;
                Actions.Add(pdp0OfConf);
            }

            if (dw != null)
            {
                UpdateG2RDw(dw, l2RDw, weightGroup, ocPerGroup, offsetSDw, offsetSDwQarg, dwCcrsToClr);
            }

            if (GNNEEnv.UseCcr)
            {
                List<CcrSet> act0CcrSet = new();
                List<CcrClr> act0CcrClr = new();
                act0CcrClr?.Add(new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Psum, l1Pp))));
                UpdateCcr(act0CcrSet, act0CcrClr!);
            }

            int addrS = l1Pp * GNNEEnv.PsumL1ElePerChan / 2 * 4;
            var raddrS = _gprHandler.SetGprItem(addrS);
            Actions.Add(new GnneActionPuPdp0Compute(0, raddrS));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateDmLoadAct0(SegmentND l2GOf, TcuComputeMode tcuMode, bool isByChannel, ACT0_CHANNEL destChannel, DataType actType, ItemName name = ItemName.Act, int offsetS = 0, List<CcrClr> act1CcrsToClr = null!)
    {
        int addrS = 0;
        int len = GNNEEnv.ActNumPerChan * GetBytesPerElement(actType);
        if (isByChannel)
        {
            addrS = (l2GOf[1].Start * GNNEEnv.ActNumPerChan * GetBytesPerElement(actType)) + offsetS;
            len = l2GOf[1].Length * GNNEEnv.ActNumPerChan * GetBytesPerElement(actType);
        }
        else if (tcuMode == TcuComputeMode.MatMul)
        {
            addrS = (l2GOf[0].Start * GNNEEnv.ActNumPerChan * GetBytesPerElement(actType)) + offsetS;

            // TODO: check difference with simple compiler
            len = l2GOf[0].Length * GNNEEnv.ActNumPerChan * GetBytesPerElement(actType);
        }

        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[name].Mmu.Id, addrS));
        var rlen = _gprHandler.SetGprItem(len);

        if (GNNEEnv.UseCcr)
        {
            List<CcrSet> act1CcrsToSet = new();
            UpdateCcr(act1CcrsToSet, act1CcrsToClr);
        }

        Actions.Add(new GnneActionDmLoadAct0(0, 0, raddrS, rlen, destChannel, isByChannel));
    }

    public void UpdateDmStoreOf(SegmentND l2GOf, SegmentND storeOf, int ofPp, DataType ofType, ACT0_CHANNEL channel = ACT0_CHANNEL.pu, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrs_to_clr = null!, int offsetD = 0, bool slice_from = true)
    {
        Ssr rstrideD = new(-1, PackStride(_glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3]), false);
        GnneActionDmStoreOfConf dmStoreOfConf = new(0, 0, 4, rstrideD, ofType);
#if K230_INST_OPT
        if (_latestConfActions.DmStoreOfConf is null || _latestConfActions.DmStoreOfConf! != dmStoreOfConf)
#endif
        {
            var strideRnS = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[1]);
            var strideRcS = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[2]);
            var strideRhS = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[3]);
            rstrideD = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstrideD));
            dmStoreOfConf = new(0, 0, 4, rstrideD, ofType);
            _latestConfActions.DmStoreOfConf = dmStoreOfConf;
            Actions.Add(dmStoreOfConf);
        }

        var shapeRn = _gprHandler.SetGprItem(l2GOf[0].Length);
        var shapeRc = _gprHandler.SetGprItem(l2GOf[1].Length);
        var shapeRh = _gprHandler.SetGprItem(l2GOf[2].Length);
        var shapeRw = _gprHandler.SetGprItem(l2GOf[3].Length);
        var rshape = _ssRegHandler.SetSsrItem(PackShape(l2GOf[0].Length, l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length));
        Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

        int offset0 = (l2GOf[0].Start - storeOf[0].Start) % storeOf[0].Length;
        int offset1 = (l2GOf[1].Start - storeOf[1].Start) % storeOf[1].Length;
        int offset2 = (l2GOf[2].Start - storeOf[2].Start) % storeOf[2].Length;
        int offset3 = (l2GOf[3].Start - storeOf[3].Start) % storeOf[3].Length;
        int addrD = offsetD + (ofPp * _glb.GlbMap[ItemName.Ofmap].AllocatedBytes);
        if (slice_from)
        {
            addrD += _glb.GlbMap[ItemName.Ofmap].GetAddr(offset0, offset1, offset2, offset3);
        }

        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.Ofmap].Mmu.Id, addrD));
        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrs_to_clr);
        }

        int lastElementAddr = ((l2GOf[0].Length - 1) * _glb.GlbMap[ItemName.Ofmap].Stride[0]) + ((l2GOf[1].Length - 1) * _glb.GlbMap[ItemName.Ofmap].Stride[1]) + ((l2GOf[2].Length - 1) * _glb.GlbMap[ItemName.Ofmap].Stride[2]) + ((l2GOf[3].Length - 1) * _glb.GlbMap[ItemName.Ofmap].Stride[3]);
        if (addrD + lastElementAddr > _glb.GlbMap[ItemName.Ofmap].Mmu.Depth * 32)
        {
            Console.WriteLine($"dim0: {l2GOf[0].Length}, dim1: {l2GOf[1].Length}, dim2: {l2GOf[2].Length}, dim3: {l2GOf[3].Length}");
            Console.WriteLine($"stride_n: {_glb.GlbMap[ItemName.Ofmap].Dimensions[1]}, stride_c: {_glb.GlbMap[ItemName.Ofmap].Dimensions[2]}, stride_h: {_glb.GlbMap[ItemName.Ofmap].Dimensions[3]}");
            Console.WriteLine($"mmu_size: {_glb.GlbMap[ItemName.Ofmap].Mmu.Depth * 32}");
            Console.WriteLine($"start_addr: {offsetD},  ofmap: {ofPp}, suboffset: {_glb.GlbMap[ItemName.Ofmap].GetAddr(offset0, offset1, offset2, offset3)}");
            throw new NotSupportedException("dm store exceed mmu size!");
        }

        Actions.Add(new GnneActionDmStoreOf(0, 0, raddrD, rshape, channel));
    }

    public void UpdateAct0Compute(SegmentND l2GOf, int shift, int l1Pp, DataType ifType, DataType wType, DataType ofType, ACT0_OUTPUT_DEST destTarget, ACT0_CHANNEL channel, bool isByChannel, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!)
    {
        int rshiftBits = shift;
        if (channel == ACT0_CHANNEL.pu && (ifType == DataTypes.Int16 || wType == DataTypes.Int16))
        {
            rshiftBits = shift - 4;
        }

        Ssr rshape = new(-1, PackShape(l2GOf[0].Length, l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length), false);
        GnneActionAct0Src1Conf act0Src1Conf = new(0, 0, 0, channel, rshape, rshiftBits);
#if K230_INST_OPT
        if (_latestConfActions.Act0Src1Conf is null || _latestConfActions.Act0Src1Conf! != act0Src1Conf)
#endif
        {
            var shapeRn = _gprHandler.SetGprItem(l2GOf[0].Length);
            var shapeRc = _gprHandler.SetGprItem(l2GOf[1].Length);
            var shapeRh = _gprHandler.SetGprItem(l2GOf[2].Length);
            var shapeRw = _gprHandler.SetGprItem(l2GOf[3].Length);
            rshape = _ssRegHandler.SetSsrItem(PackShape(l2GOf[0].Length, l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));
            act0Src1Conf = new(0, 0, 0, channel, rshape, rshiftBits);
            _latestConfActions.Act0Src1Conf = act0Src1Conf;
            Actions.Add(act0Src1Conf);
        }

        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        int addrD = l1Pp * GNNEEnv.PsumL1ElePerChan / 2 * 4;
        var raddrD = _gprHandler.SetGprItem(addrD);
        Actions.Add(new GnneActionAct0Compute(0, raddrD, channel, destTarget, ofType, isByChannel));
    }

    public void UpdateStoreT(SegmentND ofmap, Call store, int ofPp, Expr addressOf, int offset = 0, SegmentND ofmapStDdr = null!, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, ItemName name = ItemName.Ofmap, List<int> stridesS = null!, List<int> stridesD = null!, GNNEShape shape = null!, int[] layout = null!)
    {
        var outShape = store[GNNEStore.Input].CheckedShape;
        var glbType = store[GNNEStore.Input].CheckedDataType;
        var ddrType = store.CheckedDataType;

        if (ofmapStDdr is null)
        {
            ofmapStDdr = ofmap;
        }

        if (stridesS is null)
        {
            stridesS = new[] { _glb.GlbMap[name].Dimensions[1], _glb.GlbMap[name].Dimensions[2], _glb.GlbMap[name].Dimensions[3] }.ToList();
        }

        if (stridesD is null)
        {
            stridesD = new[] { (int)outShape[1].FixedValue!, (int)outShape[2].FixedValue!, (int)outShape[3].FixedValue! }.ToList();
        }

        if (shape is null)
        {
            shape = new(ofmapStDdr[0].Length, ofmapStDdr[1].Length, ofmapStDdr[2].Length, ofmapStDdr[3].Length);
        }

        // TODO: 判断是否可以reshape
        bool canBeReshaped = true;

        // if (node_cast<concat>(fuse.outer_connector(store.output()).connections()[0].owner()))
        //     can_be_reshaped = false;
        // else if (var bc = node_cast<bitcast>(fuse.outer_connector(store.output()).connections()[0].owner()))
        //     if ((!try_get_direct_child<gnne_load>(*bc) and try_get_direct_child<output_node>(*bc)) or try_get_direct_child<concat>(*bc))
        //         can_be_reshaped = false;
        if (canBeReshaped)
        {
            ReshapeSpecialScenario(ref stridesD, ref shape, ref stridesS);
        }

        Ssr strideRssS = new(-1, PackStride(stridesS[0], stridesS[1], stridesS[2]), false);
        Ssr strideRssD = new(-1, PackStride(stridesD[0], stridesD[1], stridesD[2]), false);
        GnneActionL2StoreConf l2StoreConf = new(strideRssD, strideRssS, glbType, ddrType);
#if K230_INST_OPT
        // if (_latestConfActions.L2StoreConf is null || _latestConfActions.L2StoreConf != l2StoreConf)
#endif
        {
            var strideRnS = _gprHandler.SetGprItem(stridesS[0]);
            var strideRcS = _gprHandler.SetGprItem(stridesS[1]);
            var strideRhS = _gprHandler.SetGprItem(stridesS[2]);
            strideRssS = _ssRegHandler.SetSsrItem(PackStride(stridesS[0], stridesS[1], stridesS[2]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, strideRssS));

            // if (canBeReshaped)
            {
                var strideRnD = _gprHandler.SetGprItem(stridesD[0]);
                var strideRcD = _gprHandler.SetGprItem(stridesD[1]);
                var strideRhD = _gprHandler.SetGprItem(stridesD[2]);
                strideRssD = _ssRegHandler.SetSsrItem(PackStride(stridesD[0], stridesD[1], stridesD[2]));
                Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, strideRssD, (GNNEStore)store.Target));
            }

            // else
            // {
            //     var strideRnD = _gprHandler.SetGprItem1(AddressOf.Dimensions[1]);
            //     var strideRcD = _gprHandler.SetGprItem1(AddressOf.Dimensions[2]);
            //     var strideRhD = _gprHandler.SetGprItem1(AddressOf.Dimensions[3]);
            //     strideRssD = _ssRegHandler.SetSsrItem(PackStride(stridesD[0], stridesD[1], stridesD[2]), true);
            //     Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, strideRssD, (GNNEStore)store.Target));
            // }
            l2StoreConf = new(strideRssD, strideRssS, glbType, ddrType);
            _latestConfActions.L2StoreConf = l2StoreConf;
            Actions.Add(l2StoreConf);
        }

        var shapeRn = _gprHandler.SetGprItem(shape[0]);
        var shapeRc = _gprHandler.SetGprItem(shape[1]);
        var shapeRh = _gprHandler.SetGprItem(shape[2]);
        var shapeRw = _gprHandler.SetGprItem(shape[3]);
        var shapeRss = _ssRegHandler.SetSsrItem(PackShape(shape[0], shape[1], shape[2], shape[3]));
        Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, shapeRss));
        Assert(shape[3] <= stridesS[2] || shape[2] == 1);

        int mmuItem = _glb.GlbMap[name].Mmu.Id;
        int addrS = (ofPp * _glb.GlbMap[name].AllocatedBytes) + offset;
        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(mmuItem, addrS));
        var raddrD = _gprHandler.SetGprItem1(0);

        // TODO: buffer中增加basemnet的定义
        Expr basemnet = Buffer.BufferIndexOf(addressOf);

        var rbasement = _gprHandler.SetGprItem1(basemnet);
        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        Actions.Add(new GnneActionL2Store(rbasement, raddrS, raddrD, shapeRss, store, ofmapStDdr, addressOf, store.CheckedShape.ToValueArray().ToArray()));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateMfuAct1(SegmentND ifmap1, SegmentND ifmap2, SegmentND ofmap, ACT1_SOURCE_TYPE sourceType1, ACT1_SOURCE_TYPE sourceType2, DataType quantType1, DataType quantType2, DataType quantTypeD, DeQuantizeParam deqParams1 = default, DeQuantizeParam deqParams2 = default, int rshiftBits1 = 0, int rshiftBits2 = 0, int rshiftBitsD = 0, bool is16Segments = false, int iPp = 0, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetS1 = 0, int offsetS2 = 0, int offsetD = 0, int offsetAct1 = 0, ItemName src2ItemName = ItemName.Ifmap2, ItemName src1ItemName = ItemName.Ifmap, ItemName act1Name = ItemName.MfuAct1, MFU_ACT1_FUNCTION act1Mode = MFU_ACT1_FUNCTION.add, ItemName dstItemName = ItemName.Ofmap, List<int> src1Stride = null!, List<int> src2Stride = null!, List<int> dstStride = null!, bool isByChannel = true)
    {
        // 1.
        if (src1Stride is null)
        {
            src1Stride = new List<int>(3) { _glb.GlbMap[src1ItemName].Dimensions[1], _glb.GlbMap[src1ItemName].Dimensions[2], _glb.GlbMap[src1ItemName].Dimensions[3], };
        }

        if (src2Stride is null && _glb.GlbMap.ContainsKey(src2ItemName))
        {
            src2Stride = new List<int>(3) { _glb.GlbMap[src2ItemName].Dimensions[1], _glb.GlbMap[src2ItemName].Dimensions[2], _glb.GlbMap[src2ItemName].Dimensions[3], };
        }
        else if (src2Stride is null)
        {
            src2Stride = new[] { 0, 0, 0 }.ToList();
        }

        if (dstStride is null)
        {
            dstStride = new List<int>(3) { _glb.GlbMap[dstItemName].Dimensions[1], _glb.GlbMap[dstItemName].Dimensions[2], _glb.GlbMap[dstItemName].Dimensions[3], };
        }

        Ssr rstrideS1 = new(-1, PackStride(src1Stride[0], src1Stride[1], src1Stride[2]), false);
        Ssr rstrideS2 = new(-1, PackStride(src2Stride[0], src2Stride[1], src2Stride[2]), false);
        Ssr rstrideD = new(-1, PackStride(dstStride[0], dstStride[1], dstStride[2]), false);
        GnneActionMfuAct1ConfStride act1ConfStride = new(8, rstrideS1, rstrideS2, rstrideD);
#if K230_INST_OPT
        if (_latestConfActions.Act1ConfStride is null || _latestConfActions.Act1ConfStride! != act1ConfStride)
#endif
        {
            var strideRnS1 = _gprHandler.SetGprItem(src1Stride[0]);
            var strideRcS1 = _gprHandler.SetGprItem(src1Stride[1]);
            var strideRhS1 = _gprHandler.SetGprItem(src1Stride[2]);
            rstrideS1 = _ssRegHandler.SetSsrItem(PackStride(src1Stride[0], src1Stride[1], src1Stride[2]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS1, strideRcS1, strideRhS1, rstrideS1));

            var strideRnS2 = _gprHandler.SetGprItem(src2Stride[0]);
            var strideRcS2 = _gprHandler.SetGprItem(src2Stride[1]);
            var strideRhS2 = _gprHandler.SetGprItem(src2Stride[2]);
            rstrideS2 = _ssRegHandler.SetSsrItem(PackStride(src2Stride[0], src2Stride[1], src2Stride[2]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS2, strideRcS2, strideRhS2, rstrideS2));

            var strideRnD = _gprHandler.SetGprItem(dstStride[0]);
            var strideRcD = _gprHandler.SetGprItem(dstStride[1]);
            var strideRhD = _gprHandler.SetGprItem(dstStride[2]);
            rstrideD = _ssRegHandler.SetSsrItem(PackStride(dstStride[0], dstStride[1], dstStride[2]));
            Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, rstrideD));

            act1ConfStride = new(8, rstrideS1, rstrideS2, rstrideD);
            _latestConfActions.Act1ConfStride = act1ConfStride;
            Actions.Add(act1ConfStride);
        }

        // 2.
        int[] src1Shape = { ifmap1[0].Length, ifmap1[1].Length, ifmap1[2].Length, ifmap1[3].Length };
        int[] src2Shape = { ifmap2[0].Length, ifmap2[1].Length, ifmap2[2].Length, ifmap2[3].Length };
        int[] destShape = { ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length };
        var repeatSrc1 = GetMfuRepeat(src1Shape, destShape);
        var repeatSrc2 = GetMfuRepeat(src2Shape, destShape);

        Ssr rshape;
        {
            int sliceLoc = 1; // todo 0--l1; 1-- glb
            Gpr rslice = new(-1, repeatSrc1.SliceLen, false);
            Gpr rrightRepeats = new(-1, repeatSrc1.ScalarRepeat, false);
            Gpr rsliceRepeats = new(-1, repeatSrc1.SliceRepeat, false);
            GnneActionMfuAct1ConfSrc1 act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 0, sliceLoc);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc1 is null || _latestConfActions.Act1ConfSrc1! != act1ConfSrc1)
#endif
            {
                rslice = _gprHandler.SetGprItem(repeatSrc1.SliceLen);
                rrightRepeats = _gprHandler.SetGprItem(repeatSrc1.ScalarRepeat);
                rsliceRepeats = _gprHandler.SetGprItem(repeatSrc1.SliceRepeat);

                act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 0, sliceLoc);
                _latestConfActions.Act1ConfSrc1 = act1ConfSrc1;
                Actions.Add(act1ConfSrc1);
            }

            rshape = new(-1, PackShape(ifmap1[0].Length, ifmap1[1].Length, ifmap1[2].Length, ifmap1[3].Length), false);
            int leftRepeats = repeatSrc1.LeftRepeats;
            Gpr rleftRepeats = new(-1, leftRepeats, false);
            GnneActionMfuAct1ConfSrc2 act1ConfSrc2 = new(10, rleftRepeats, rshape, 0, sourceType1);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc2 is null || _latestConfActions.Act1ConfSrc2! != act1ConfSrc2)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(ifmap1[0].Length);
                var shapeRc = _gprHandler.SetGprItem(ifmap1[1].Length);
                var shapeRh = _gprHandler.SetGprItem(ifmap1[2].Length);
                var shapeRw = _gprHandler.SetGprItem(ifmap1[3].Length);
                rshape = _ssRegHandler.SetSsrItem(PackShape(ifmap1[0].Length, ifmap1[1].Length, ifmap1[2].Length, ifmap1[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

                rleftRepeats = _gprHandler.SetGprItem(leftRepeats);

                act1ConfSrc2 = new(10, rleftRepeats, rshape, 0, sourceType1);
                _latestConfActions.Act1ConfSrc2 = act1ConfSrc2;
                Actions.Add(act1ConfSrc2);
            }
        }

        {
            int sliceLoc = 1; // todo 0--l1; 1-- glb
            Gpr rslice = new(-1, repeatSrc2.SliceLen, false);
            Gpr rrightRepeats = new(-1, repeatSrc2.ScalarRepeat, false);
            Gpr rsliceRepeats = new(-1, repeatSrc2.SliceRepeat, false);
            GnneActionMfuAct1ConfSrc1 act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 1, sliceLoc);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc1 is null || _latestConfActions.Act1ConfSrc1! != act1ConfSrc1)
#endif
            {
                rslice = _gprHandler.SetGprItem(repeatSrc2.SliceLen);
                rrightRepeats = _gprHandler.SetGprItem(repeatSrc2.ScalarRepeat);
                rsliceRepeats = _gprHandler.SetGprItem(repeatSrc2.SliceRepeat);

                act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 1, sliceLoc);
                _latestConfActions.Act1ConfSrc1 = act1ConfSrc1;
                Actions.Add(act1ConfSrc1);
            }

            rshape = new(-1, PackShape(ifmap2[0].Length, ifmap2[1].Length, ifmap2[2].Length, ifmap2[3].Length), false);
            int leftRepeats = repeatSrc2.LeftRepeats;
            Gpr rleftRepeats = new(-1, leftRepeats, false);
            GnneActionMfuAct1ConfSrc2 act1ConfSrc2 = new(10, rleftRepeats, rshape, 1, sourceType2);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc2 is null || _latestConfActions.Act1ConfSrc2! != act1ConfSrc2)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(ifmap2[0].Length);
                var shapeRc = _gprHandler.SetGprItem(ifmap2[1].Length);
                var shapeRh = _gprHandler.SetGprItem(ifmap2[2].Length);
                var shapeRw = _gprHandler.SetGprItem(ifmap2[3].Length);
                rshape = _ssRegHandler.SetSsrItem(PackShape(ifmap2[0].Length, ifmap2[1].Length, ifmap2[2].Length, ifmap2[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

                rleftRepeats = _gprHandler.SetGprItem(leftRepeats);

                act1ConfSrc2 = new(10, rleftRepeats, rshape, 1, sourceType2);
                _latestConfActions.Act1ConfSrc2 = act1ConfSrc2;
                Actions.Add(act1ConfSrc2);
            }
        }

        // 3.
        rshape = new(-1, PackShape(ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length), false);
        int len = ofmap.Shape_size;
        Gpr rlen = new(-1, len, false);
        GnneActionMfuAct1ConfDest act1ConfDest = new(11, rlen, rshape);
#if K230_INST_OPT
        if (_latestConfActions.Act1ConfDest is null || _latestConfActions.Act1ConfDest! != act1ConfDest)
#endif
        {
            var shapeRn = _gprHandler.SetGprItem(ofmap[0].Length);
            var shapeRc = _gprHandler.SetGprItem(ofmap[1].Length);
            var shapeRh = _gprHandler.SetGprItem(ofmap[2].Length);
            var shapeRw = _gprHandler.SetGprItem(ofmap[3].Length);
            rshape = _ssRegHandler.SetSsrItem(PackShape(ofmap[0].Length, ofmap[1].Length, ofmap[2].Length, ofmap[3].Length));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

            rlen = _gprHandler.SetGprItem(len);

            act1ConfDest = new(11, rlen, rshape);
            _latestConfActions.Act1ConfDest = act1ConfDest;
            Actions.Add(act1ConfDest);
        }

        // 4.
        // if (quant_type1 != DataTypes.Float16)
        {
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)deqParams1.Scale), 0);
            int bias = deqParams1.ZeroPoint;
            Gpr rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);
            GnneActionMfuAct1ConfDeq act1ConfDeq = new(12, rscale, rbias, quantType1, 0, rshiftBits1);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfDeq is null || _latestConfActions.Act1ConfDeq! != act1ConfDeq)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                act1ConfDeq = new(12, rscale, rbias, quantType1, 0, rshiftBits1);
                _latestConfActions.Act1ConfDeq = act1ConfDeq;
                Actions.Add(act1ConfDeq);
            }
        }

        // if (quant_type2 != DataTypes.Float16)
        {
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)deqParams2.Scale), 0);
            int bias = deqParams2.ZeroPoint;
            Gpr rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);
            GnneActionMfuAct1ConfDeq act1ConfDeq = new(12, rscale, rbias, quantType2, 1, rshiftBits2);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfDeq is null || _latestConfActions.Act1ConfDeq! != act1ConfDeq)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                act1ConfDeq = new(12, rscale, rbias, quantType2, 1, rshiftBits2);
                _latestConfActions.Act1ConfDeq = act1ConfDeq;
                Actions.Add(act1ConfDeq);
            }
        }

        // 5.
        GnneActionMfuAct1ConfQuant act1ConfQuant = new(13, quantTypeD, rshiftBitsD);
#if K230_INST_OP
        if (_latestConfActions.act1_conf_quant != act1_conf_quant)
#endif
        {
            _latestConfActions.Act1ConfQuant = act1ConfQuant;
            Actions.Add(act1ConfQuant);
        }

        // 6.
        GnneActionMfuAct1Conf act1Conf = new(14, act1Mode, !is16Segments && isByChannel, is16Segments);
#if K230_INST_OPT
        if (_latestConfActions.Act1Conf is null || _latestConfActions.Act1Conf! != act1Conf)
#endif
        {
            _latestConfActions.Act1Conf = act1Conf;
            Actions.Add(act1Conf);
        }

        // 7.
        int addrD = (iPp * _glb.GlbMap[dstItemName].AllocatedBytes) + offsetD;
        int addrS1 = (iPp * _glb.GlbMap[src1ItemName].AllocatedBytes) + offsetS1;
        int mmuItem1 = _glb.GlbMap[src1ItemName].Mmu.Id;
        if (src1ItemName != ItemName.Ifmap)
        {
            addrS1 = offsetS1;
            mmuItem1 = _glb.GlbMap[src1ItemName].Mmu.Id;
        }

        int addrS2 = 0;
        int mmuItem2 = 0;
        if (src2Shape.Any(x => x != 0))
        {
            addrS2 = offsetS2 + (iPp * _glb.GlbMap[src2ItemName].AllocatedBytes);
            mmuItem2 = _glb.GlbMap[src2ItemName].Mmu.Id;
        }

        int addrArg = offsetAct1;
        int mmuItemOf = _glb.GlbMap[dstItemName].Mmu.Id;
        if (dstItemName != ItemName.Ofmap)
        {
            addrD = offsetD;
            mmuItemOf = _glb.GlbMap[dstItemName].Mmu.Id;
        }

        if (!is16Segments)
        {
            addrArg = (ofmap[1].Start * GNNEEnv.ActNumPerChan * GetBytesPerElement(DataTypes.Float16)) + offsetAct1;
        }

        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(mmuItemOf, addrD));
        var raddrS1 = _gprHandler.SetGprItem(ToGlbAddr(mmuItem1, addrS1));
        var raddrS2 = _gprHandler.SetGprItem(ToGlbAddr(mmuItem2, addrS2));
        var raddrArg = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[act1Name].Mmu.Id, addrArg));
        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        Actions.Add(new GnneActionMfuAct1Compute(raddrD, raddrS1, raddrS2, raddrArg));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateMfuAct1InFuse(SegmentND l2RPsum, SegmentND l2RIf2, SegmentND ifmap2, SegmentND l2GOf, SegmentND ofmap, ACT1_SOURCE_TYPE sourceType1, ACT1_SOURCE_TYPE sourceType2, DataType quantType1, DataType quantType2, DataType quantTypeD, DeQuantizeParam deqParams1, DeQuantizeParam deqParams2, int rshiftBits1, int rshiftBits2, int rshiftBitsD, bool is16Segments, int ofPp, int l1Pp, List<CcrClr> l2RIf2CcrsToClr = null!, List<CcrSet> ofCcrsToSet = null!, List<CcrClr> ofCcrsToClr = null!, int offsetSSrc2 = 0, int offsetAct1 = 0, int offsetOfmap = 0, ItemName src2ItemName = ItemName.Ifmap2, List<CcrClr> act1CcrsToClr = null!, MFU_ACT1_FUNCTION actType = MFU_ACT1_FUNCTION.add)
    {
        // when fuse act1 with conv2d, ifmap1 is form L1
        // 1.
        Ssr rstrideS2 = new(-1, PackStride(_glb.GlbMap[ItemName.Ifmap2].Dimensions[1], _glb.GlbMap[ItemName.Ifmap2].Dimensions[2], _glb.GlbMap[ItemName.Ifmap2].Dimensions[3]), false);
        Ssr rstrideD = new(-1, PackStride(_glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3]), false);
        GnneActionMfuAct1ConfStride act1ConfStride = new(8, rstrideS2, rstrideS2, rstrideD);
#if K230_INST_OPT
        if (_latestConfActions.Act1ConfStride is null || _latestConfActions.Act1ConfStride! != act1ConfStride)
#endif
        {
            var strideRnS2 = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ifmap2].Dimensions[1]);
            var strideRcS2 = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ifmap2].Dimensions[2]);
            var strideRhS2 = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ifmap2].Dimensions[3]);
            rstrideS2 = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ItemName.Ifmap2].Dimensions[1], _glb.GlbMap[ItemName.Ifmap2].Dimensions[2], _glb.GlbMap[ItemName.Ifmap2].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS2, strideRcS2, strideRhS2, rstrideS2));

            var strideRnD = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[1]);
            var strideRcD = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[2]);
            var strideRhD = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[3]);
            rstrideD = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, rstrideD));

            act1ConfStride = new(8, rstrideS2, rstrideS2, rstrideD);
            _latestConfActions.Act1ConfStride = act1ConfStride;
            Actions.Add(act1ConfStride);
        }

        // 2.
        int[] src1Shape = new[] { l2RPsum[0].Length, l2RPsum[1].Length, l2RPsum[2].Length, l2RPsum[3].Length };
        int[] src2Shape = new[] { l2RIf2[0].Length, l2RIf2[1].Length, l2RIf2[2].Length, l2RIf2[3].Length };
        int[] destShape = new[] { l2GOf[0].Length, l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length };
        var repeatSrc1 = GetMfuRepeat(src1Shape, destShape);
        var repeatSrc2 = GetMfuRepeat(src2Shape, destShape);

        Ssr rshape;
        {
            int sliceLoc = 0; // todo 0--l1; 1-- glb
            Gpr rslice = new(-1, repeatSrc1.SliceLen, false);
            Gpr rrightRepeats = new(-1, repeatSrc1.ScalarRepeat, false);
            Gpr rsliceRepeats = new(-1, repeatSrc1.SliceRepeat, false);
            GnneActionMfuAct1ConfSrc1 act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 0, sliceLoc);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc1 is null || _latestConfActions.Act1ConfSrc1! != act1ConfSrc1)
#endif
            {
                rslice = _gprHandler.SetGprItem(repeatSrc1.SliceLen);
                rrightRepeats = _gprHandler.SetGprItem(repeatSrc1.ScalarRepeat);
                rsliceRepeats = _gprHandler.SetGprItem(repeatSrc1.SliceRepeat);

                act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 0, sliceLoc);
                _latestConfActions.Act1ConfSrc1 = act1ConfSrc1;
                Actions.Add(act1ConfSrc1);
            }

            rshape = new(-1, PackShape(l2RPsum[0].Length, l2RPsum[1].Length, l2RPsum[2].Length, l2RPsum[3].Length), false);
            int leftRepeats = repeatSrc1.LeftRepeats;
            Gpr rleftRepeats = new(-1, leftRepeats, false);
            GnneActionMfuAct1ConfSrc2 act1ConfSrc2 = new(10, rleftRepeats, rshape, 0, sourceType1);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc2 is null || _latestConfActions.Act1ConfSrc2! != act1ConfSrc2)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(l2RPsum[0].Length);
                var shapeRc = _gprHandler.SetGprItem(l2RPsum[1].Length);
                var shapeRh = _gprHandler.SetGprItem(l2RPsum[2].Length);
                var shapeRw = _gprHandler.SetGprItem(l2RPsum[3].Length);
                rshape = _ssRegHandler.SetSsrItem(PackShape(l2RPsum[0].Length, l2RPsum[1].Length, l2RPsum[2].Length, l2RPsum[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

                rleftRepeats = _gprHandler.SetGprItem(leftRepeats);

                act1ConfSrc2 = new(10, rleftRepeats, rshape, 0, sourceType1);
                _latestConfActions.Act1ConfSrc2 = act1ConfSrc2;
                Actions.Add(act1ConfSrc2);
            }
        }

        {
            int sliceLoc = 1; // todo 0--l1; 1-- glb
            Gpr rslice = new(-1, repeatSrc2.SliceLen, false);
            Gpr rrightRepeats = new(-1, repeatSrc2.ScalarRepeat, false);
            Gpr rsliceRepeats = new(-1, repeatSrc2.SliceRepeat, false);
            GnneActionMfuAct1ConfSrc1 act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 1, sliceLoc);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc1 is null || _latestConfActions.Act1ConfSrc1! != act1ConfSrc1)
#endif
            {
                rslice = _gprHandler.SetGprItem(repeatSrc2.SliceLen);
                rrightRepeats = _gprHandler.SetGprItem(repeatSrc2.ScalarRepeat);
                rsliceRepeats = _gprHandler.SetGprItem(repeatSrc2.SliceRepeat);

                act1ConfSrc1 = new(9, rslice, rrightRepeats, rsliceRepeats, 1, sliceLoc);
                _latestConfActions.Act1ConfSrc1 = act1ConfSrc1;
                Actions.Add(act1ConfSrc1);
            }

            rshape = new(-1, PackShape(l2RIf2[0].Length, l2RIf2[1].Length, l2RIf2[2].Length, l2RIf2[3].Length), false);
            int leftRepeats = repeatSrc2.LeftRepeats;
            Gpr rleftRepeats = new(-1, leftRepeats, false);
            GnneActionMfuAct1ConfSrc2 act1ConfSrc2 = new(10, rleftRepeats, rshape, 1, sourceType2);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfSrc2 is null || _latestConfActions.Act1ConfSrc2! != act1ConfSrc2)
#endif
            {
                var shapeRn = _gprHandler.SetGprItem(l2RIf2[0].Length);
                var shapeRc = _gprHandler.SetGprItem(l2RIf2[1].Length);
                var shapeRh = _gprHandler.SetGprItem(l2RIf2[2].Length);
                var shapeRw = _gprHandler.SetGprItem(l2RIf2[3].Length);
                rshape = _ssRegHandler.SetSsrItem(PackShape(l2RIf2[0].Length, l2RIf2[1].Length, l2RIf2[2].Length, l2RIf2[3].Length));
                Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

                rleftRepeats = _gprHandler.SetGprItem(leftRepeats);

                act1ConfSrc2 = new(10, rleftRepeats, rshape, 1, sourceType2);
                _latestConfActions.Act1ConfSrc2 = act1ConfSrc2;
                Actions.Add(act1ConfSrc2);
            }
        }

        // 3.
        rshape = new(-1, PackShape(l2GOf[0].Length, l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length), false);
        int len = l2GOf.Shape_size;
        Gpr rlen = new(-1, len, false);
        GnneActionMfuAct1ConfDest act1ConfDest = new(11, rlen, rshape);
#if K230_INST_OPT
        if (_latestConfActions.Act1ConfDest is null || _latestConfActions.Act1ConfDest! != act1ConfDest)
#endif
        {
            var shapeRn = _gprHandler.SetGprItem(l2GOf[0].Length);
            var shapeRc = _gprHandler.SetGprItem(l2GOf[1].Length);
            var shapeRh = _gprHandler.SetGprItem(l2GOf[2].Length);
            var shapeRw = _gprHandler.SetGprItem(l2GOf[3].Length);
            rshape = _ssRegHandler.SetSsrItem(PackShape(l2GOf[0].Length, l2GOf[1].Length, l2GOf[2].Length, l2GOf[3].Length));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

            rlen = _gprHandler.SetGprItem(len);

            act1ConfDest = new(11, rlen, rshape);
            _latestConfActions.Act1ConfDest = act1ConfDest;
            Actions.Add(act1ConfDest);
        }

        // 4.
        {
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)deqParams1.Scale), 0);
            int bias = deqParams1.ZeroPoint;
            Gpr rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);
            GnneActionMfuAct1ConfDeq act1ConfDeq = new(12, rscale, rbias, quantType1, 0, rshiftBits1);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfDeq is null || _latestConfActions.Act1ConfDeq! != act1ConfDeq)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                act1ConfDeq = new(12, rscale, rbias, quantType1, 0, rshiftBits1);
                _latestConfActions.Act1ConfDeq = act1ConfDeq;
                Actions.Add(act1ConfDeq);
            }
        }

        {
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)deqParams2.Scale), 0);
            int bias = deqParams2.ZeroPoint;
            Gpr rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);
            GnneActionMfuAct1ConfDeq act1ConfDeq = new(12, rscale, rbias, quantType2, 1, rshiftBits2);
#if K230_INST_OPT
            if (_latestConfActions.Act1ConfDeq is null || _latestConfActions.Act1ConfDeq! != act1ConfDeq)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                act1ConfDeq = new(12, rscale, rbias, quantType2, 1, rshiftBits2);
                _latestConfActions.Act1ConfDeq = act1ConfDeq;
                Actions.Add(act1ConfDeq);
            }
        }

        // 5.
        GnneActionMfuAct1ConfQuant act1ConfQuant = new(13, quantTypeD, rshiftBitsD);
#if K230_INST_OPT
        if (_latestConfActions.Act1ConfQuant is null || _latestConfActions.Act1ConfQuant! != act1ConfQuant)
#endif
        {
            _latestConfActions.Act1ConfQuant = act1ConfQuant;
            Actions.Add(act1ConfQuant);
        }

        // 6.
        GnneActionMfuAct1Conf act1Conf = new(14, actType, !is16Segments, is16Segments);
#if K230_INST_OPT
        if (_latestConfActions.Act1Conf is null || _latestConfActions.Act1Conf! != act1Conf)
#endif
        {
            _latestConfActions.Act1Conf = act1Conf;
            Actions.Add(act1Conf);
        }

        // 7.
        if (GNNEEnv.UseCcr)
        {
            var ccrsToClr = ofCcrsToClr;
            if (src2Shape.Any(x => x != 0))
            {
                ccrsToClr.AddRange(l2RIf2CcrsToClr);
            }

            CcrClr act0CcrClr = new(_ccrHandler.GetCcrItem(_ccrHandler.GetName(ItemName.Psum, l1Pp)));
            ccrsToClr.Add(act0CcrClr);
            if (act1CcrsToClr is not null)
            {
                ccrsToClr.AddRange(act1CcrsToClr);
            }

            UpdateCcr(ofCcrsToSet, ccrsToClr);
        }

        int offset0 = (l2GOf[0].Start - ofmap[0].Start) % ofmap[0].Length;
        int offset1 = (l2GOf[1].Start - ofmap[1].Start) % ofmap[1].Length;
        int offset2 = (l2GOf[2].Start - ofmap[2].Start) % ofmap[2].Length;
        int offset3 = (l2GOf[3].Start - ofmap[3].Start) % ofmap[3].Length;
        int addrD = offsetOfmap + (ofPp * _glb.GlbMap[ItemName.Ofmap].AllocatedBytes) + _glb.GlbMap[ItemName.Ofmap].GetAddr(offset0, offset1, offset2, offset3);
        int addrS1 = l1Pp * GNNEEnv.PsumL1ElePerChan / 2 * 4;
        int addrS2 = 0;
        int mmuItem1 = 0;
        int mmuItem2 = 0;
        if (src2Shape.Any(x => x != 0))
        {
            offset0 = (l2RIf2[0].Start - ifmap2[0].Start) % ifmap2[0].Length;
            offset1 = (l2RIf2[1].Start - ifmap2[1].Start) % ifmap2[1].Length;
            offset2 = (l2RIf2[2].Start - ifmap2[2].Start) % ifmap2[2].Length;
            offset3 = (l2RIf2[3].Start - ifmap2[3].Start) % ifmap2[3].Length;
            addrS2 = offsetSSrc2 + (ofPp * _glb.GlbMap[ItemName.Ifmap2].AllocatedBytes) + _glb.GlbMap[ItemName.Ifmap2].GetAddr(offset0, offset1, offset2, offset3);
            mmuItem2 = _glb.GlbMap[src2ItemName].Mmu.Id;
        }

        int addrArg;
        if (!is16Segments)
        {
            addrArg = (l2GOf[1].Start * GNNEEnv.ActNumPerChan * GetBytesPerElement(DataTypes.Float16)) + offsetAct1;
        }
        else
        {
            addrArg = offsetAct1;
        }

        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.Ofmap].Mmu.Id, addrD));
        var raddrS1 = _gprHandler.SetGprItem(ToGlbAddr(mmuItem1, addrS1));
        var raddrS2 = _gprHandler.SetGprItem(ToGlbAddr(mmuItem2, addrS2));
        var raddrArg = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.MfuAct1].Mmu.Id, addrArg));
        Actions.Add(new GnneActionMfuAct1Compute(raddrD, raddrS1, raddrS2, raddrArg));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateMfuTranspose(SegmentND ifmap, SegmentND ofmap, DataType l2Datatype, MFU_TRANS_PERMUTE perm, int iPp, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetS = 0, int offsetD = 0, ItemName ifName = ItemName.Ifmap, ItemName ofName = ItemName.Ofmap, int of_pp = -1)
    {
        // 1.
        Ssr rstrideS = new(-1, PackStride(_glb.GlbMap[ifName].Dimensions[1], _glb.GlbMap[ifName].Dimensions[2], _glb.GlbMap[ifName].Dimensions[3]), false);
        Ssr rstrideD = new(-1, PackStride(_glb.GlbMap[ofName].Dimensions[1], _glb.GlbMap[ofName].Dimensions[2], _glb.GlbMap[ofName].Dimensions[3]), false);
        GnneActionMfuTransposeConf transposeConf = new(0, rstrideD, rstrideS, l2Datatype, perm);
#if K230_INST_OPT
        if (_latestConfActions.TransposeConf is null || _latestConfActions.TransposeConf! != transposeConf)
#endif
        {
            var strideRnS = _gprHandler.SetGprItem(_glb.GlbMap[ifName].Dimensions[1]);
            var strideRcS = _gprHandler.SetGprItem(_glb.GlbMap[ifName].Dimensions[2]);
            var strideRhS = _gprHandler.SetGprItem(_glb.GlbMap[ifName].Dimensions[3]);
            rstrideS = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ifName].Dimensions[1], _glb.GlbMap[ifName].Dimensions[2], _glb.GlbMap[ifName].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstrideS));

            var strideRnD = _gprHandler.SetGprItem(_glb.GlbMap[ofName].Dimensions[1]);
            var strideRcD = _gprHandler.SetGprItem(_glb.GlbMap[ofName].Dimensions[2]);
            var strideRhD = _gprHandler.SetGprItem(_glb.GlbMap[ofName].Dimensions[3]);
            rstrideD = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ofName].Dimensions[1], _glb.GlbMap[ofName].Dimensions[2], _glb.GlbMap[ofName].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, rstrideD));

            transposeConf = new(0, rstrideD, rstrideS, l2Datatype, perm);
            _latestConfActions.TransposeConf = transposeConf;
            Actions.Add(transposeConf);
        }

        // 2.
        var shapeRn = _gprHandler.SetGprItem(ifmap[0].Length);
        var shapeRc = _gprHandler.SetGprItem(ifmap[1].Length);
        var shapeRh = _gprHandler.SetGprItem(ifmap[2].Length);
        var shapeRw = _gprHandler.SetGprItem(ifmap[3].Length);
        var rshape = _ssRegHandler.SetSsrItem(PackShape(ifmap[0].Length, ifmap[1].Length, ifmap[2].Length, ifmap[3].Length));
        Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

        if (of_pp == -1)
        {
            of_pp = iPp;
        }

        int addrD = (of_pp * _glb.GlbMap[ofName].AllocatedBytes) + offsetD;
        int addrS = (iPp * _glb.GlbMap[ifName].AllocatedBytes) + offsetS;
        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ofName].Mmu.Id, addrD));
        var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ifName].Mmu.Id, addrS));
        if (GNNEEnv.UseCcr)
        {
            UpdateCcr(ccrsToSet, ccrsToClr);
        }

        Actions.Add(new GnneActionMfuTranspose(raddrD, raddrS, rshape));

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateMfuPdp1(Call pdp, SegmentND ifmap, SegmentND ofmap, int iPp, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetS = 0, int offsetD = 0)
    {
        int strideH = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)pdp[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
        int kernelH = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[0];
        int kernelW = ((TensorConst)pdp[GNNEPdp1.Filter]).Value.ToArray<int>()[1];

        var inputType = pdp[GNNEPdp1.Input].CheckedDataType;
        var outputType = pdp.CheckedDataType;

        PDP_FUNCTION PdpFunc(MFU_PDP_OP op)
        {
            return op switch
            {
                MFU_PDP_OP.MIN => PDP_FUNCTION.min,
                MFU_PDP_OP.MAX => PDP_FUNCTION.max,
                MFU_PDP_OP.AVERAGE => PDP_FUNCTION.average,
                MFU_PDP_OP.SUM => PDP_FUNCTION.sum,
                _ => PDP_FUNCTION.min,
            };
        }

        var pdpOp = PdpFunc(((GNNEPdp1)pdp.Target).ReduceOp);

        // 1.
        Ssr rstrideS = new(-1, PackStride(_glb.GlbMap[ItemName.Ifmap].Dimensions[1], _glb.GlbMap[ItemName.Ifmap].Dimensions[2], _glb.GlbMap[ItemName.Ifmap].Dimensions[3]), false);
        Ssr rstrideD = new(-1, PackStride(_glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3]), false);
        int debugStrideW = _glb.GlbMap[ItemName.Ofmap].Dimensions[3];
        GnneActionMfuPdp1Conf1 pdp1Conf1 = new(1, strideW, strideH, rstrideD, rstrideS, pdpOp);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf1 is null || _latestConfActions.Pdp1Conf1! != pdp1Conf1)
#endif
        {
            var strideRnS = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ifmap].Dimensions[1]);
            var strideRcS = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ifmap].Dimensions[2]);
            var strideRhS = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ifmap].Dimensions[3]);
            rstrideS = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ItemName.Ifmap].Dimensions[1], _glb.GlbMap[ItemName.Ifmap].Dimensions[2], _glb.GlbMap[ItemName.Ifmap].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstrideS));

            var strideRnD = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[1]);
            var strideRcD = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[2]);
            var strideRhD = _gprHandler.SetGprItem(_glb.GlbMap[ItemName.Ofmap].Dimensions[3]);
            rstrideD = _ssRegHandler.SetSsrItem(PackStride(_glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3]));
            Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, rstrideD));

            pdp1Conf1 = new(1, strideW, strideH, rstrideD, rstrideS, pdpOp);
            _latestConfActions.Pdp1Conf1 = pdp1Conf1;
            Actions.Add(pdp1Conf1);
        }

        // 2.
        int countW = ofmap[3].Length;
        int countH = ofmap[2].Length;
        int activeH = Math.Min(GNNEEnv.MfuPuHeight, ifmap[1].Length * kernelH);
        int peChannels = activeH / kernelH;
        int peH = kernelH * peChannels;
        int peLastChannels = peChannels;
        if (ifmap[1].Length % peChannels != 0)
        {
            peLastChannels = ifmap[1].Length % peChannels;
        }

        int peLastH = kernelH * peLastChannels;

        // pad_value is fp16 for pdp1
        int padValue = BitConverter.ToInt16(BitConverter.GetBytes((Half)((TensorConst)pdp[GNNEPdp1.Value]).Value.ToScalar<float>()), 0);

        Gpr rcountW = new(-1, countW, false);
        Gpr rcountH = new(-1, countH, false);
        Gpr rpeH = new(-1, peH, false);
        Gpr rpeLastH = new(-1, peLastH, false);
        GnneActionMfuPdp1Conf2 pdp1Conf2 = new(2, rcountW, rcountH, rpeH, rpeLastH);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf2 is null || _latestConfActions.Pdp1Conf2! != pdp1Conf2)
#endif
        {
            rcountW = _gprHandler.SetGprItem(countW);
            rcountH = _gprHandler.SetGprItem(countH);
            rpeH = _gprHandler.SetGprItem(peH);
            rpeLastH = _gprHandler.SetGprItem(peLastH);
            pdp1Conf2 = new(2, rcountW, rcountH, rpeH, rpeLastH);
            _latestConfActions.Pdp1Conf2 = pdp1Conf2;
            Actions.Add(pdp1Conf2);
        }

        // 3.
        Ssr sspad = new(-1, PackShape(ifmap.PadH.Before, ifmap.PadH.After, ifmap.PadW.Before, ifmap.PadW.After), false);
        Gpr rpeChannels = new(-1, peChannels, false);
        Gpr rpeLastChannels = new(-1, peLastChannels, false);
        Gpr rpadValue = new(-1, padValue, false);
        GnneActionMfuPdp1Conf3 pdp1Conf3 = new(3, rpeChannels, rpeLastChannels, rpadValue, sspad);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf3 is null || _latestConfActions.Pdp1Conf3! != pdp1Conf3)
#endif
        {
            var shapeRn = _gprHandler.SetGprItem(ifmap.PadH.Before);
            var shapeRc = _gprHandler.SetGprItem(ifmap.PadH.After);
            var shapeRh = _gprHandler.SetGprItem(ifmap.PadW.Before);
            var shapeRw = _gprHandler.SetGprItem(ifmap.PadW.After);
            sspad = _ssRegHandler.SetSsrItem(PackShape(ifmap.PadH.Before, ifmap.PadH.After, ifmap.PadW.Before, ifmap.PadW.After));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, sspad));

            rpeChannels = _gprHandler.SetGprItem(peChannels);
            rpeLastChannels = _gprHandler.SetGprItem(peLastChannels);
            rpadValue = _gprHandler.SetGprItem(padValue);
            pdp1Conf3 = new(3, rpeChannels, rpeLastChannels, rpadValue, sspad);
            _latestConfActions.Pdp1Conf3 = pdp1Conf3;
            Actions.Add(pdp1Conf3);
        }

        // 4.
        Gpr rwindowW = new(-1, kernelW, false);
        Gpr rwindowH = new(-1, kernelH, false);
        var scaleSumPooling = (Half)1.0; // TODO: should use the real value;
        Gpr rscale = new(-1, BitConverter.ToInt16(BitConverter.GetBytes(scaleSumPooling), 0), false);
        bool enableH2C = false;
        bool enableBw = false;
        GnneActionMfuPdp1Conf4 pdp1Conf4 = new(4, rwindowW, rwindowH, rscale, enableH2C, enableBw);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf4 is null || _latestConfActions.Pdp1Conf4! != pdp1Conf4)
#endif
        {
            rwindowW = _gprHandler.SetGprItem(kernelW);
            rwindowH = _gprHandler.SetGprItem(kernelH);
            rscale = _gprHandler.SetGprItem(BitConverter.ToInt16(BitConverter.GetBytes(scaleSumPooling), 0));
            pdp1Conf4 = new(4, rwindowW, rwindowH, rscale, enableH2C, enableBw);
            _latestConfActions.Pdp1Conf4 = pdp1Conf4;
            Actions.Add(pdp1Conf4);
        }

        // 5.
        // if (input_type != DataTypes.Float16)
        {
            var deqP = ((TensorConst)pdp[GNNEPdp1.DequantParams]).Value.ToScalar<DeQuantizeParam>();
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)deqP.Scale), 0);
            int bias = deqP.ZeroPoint;
            rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);
            GnneActionMfuPdp1ConfDeq pdp1ConfDeq = new(6, rscale, rbias, inputType, ((TensorConst)pdp[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
#if K230_INST_OPT
            if (_latestConfActions.Pdp1ConfDeq is null || _latestConfActions.Pdp1ConfDeq! != pdp1ConfDeq)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                pdp1ConfDeq = new(6, rscale, rbias, inputType, ((TensorConst)pdp[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
                _latestConfActions.Pdp1ConfDeq = pdp1ConfDeq;
                Actions.Add(pdp1ConfDeq);
            }
        }

        // 6.
        // if (output_type != DataTypes.Float16)
        {
            var qP = ((TensorConst)pdp[GNNEPdp1.QuantParams]).Value.ToScalar<QuantizeParam>();
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)qP.Scale), 0);
            int bias = BitConverter.ToInt16(BitConverter.GetBytes((Half)qP.ZeroPoint), 0);
            rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);

            // TODO: 区分shiftbits
            GnneActionMfuPdp1ConfQuant pdp1_conf_quant = new(7, rscale, rbias, outputType, ((TensorConst)pdp[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
#if K230_INST_OPT
            if (_latestConfActions.Pdp1ConfQuant is null || _latestConfActions.Pdp1ConfQuant! != pdp1_conf_quant)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                pdp1_conf_quant = new(7, rscale, rbias, outputType, ((TensorConst)pdp[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
                _latestConfActions.Pdp1ConfQuant = pdp1_conf_quant;
                Actions.Add(pdp1_conf_quant);
            }
        }

        // 7.
        {
            var shapeRn = _gprHandler.SetGprItem(ifmap[0].Length);
            var shapeRc = _gprHandler.SetGprItem(ifmap[1].Length);
            var shapeRh = _gprHandler.SetGprItem(ifmap[2].Length);
            var shapeRw = _gprHandler.SetGprItem(ifmap[3].Length);
            var rshape = _ssRegHandler.SetSsrItem(PackShape(ifmap[0].Length, ifmap[1].Length, ifmap[2].Length, ifmap[3].Length));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

            int addrD = (iPp * _glb.GlbMap[ItemName.Ofmap].AllocatedBytes) + offsetD;
            int addrS = (iPp * _glb.GlbMap[ItemName.Ifmap].AllocatedBytes) + offsetS;
            var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.Ofmap].Mmu.Id, addrD));
            var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.Ifmap].Mmu.Id, addrS));
            if (GNNEEnv.UseCcr)
            {
                UpdateCcr(ccrsToSet, ccrsToClr);
            }

            Actions.Add(new GnneActionMfuPdp1Compute(raddrD, raddrS, rshape));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateMfuGlobalPdp1(Call call, DataType inputType, DataType outputType, PDP_FUNCTION pdpOp, SegmentND ifmap, SegmentND ofmap, int iPp, Half sumScale, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!, int offsetS = 0, int offsetD = 0, ItemName ifName = ItemName.Ifmap, List<int> ifStride = null!, List<int> ofStride = null!)
    {
        int strideH = ((TensorConst)call[GNNEPdp1.Stride]).Value.ToArray<int>()[0];
        int strideW = ((TensorConst)call[GNNEPdp1.Stride]).Value.ToArray<int>()[1];
        int kernelH = ifmap[2].Length;
        int kernelW = ifmap[3].Length;

        if (ifStride is null)
        {
            ifStride = new[] { _glb.GlbMap[ifName].Dimensions[1], _glb.GlbMap[ifName].Dimensions[2], _glb.GlbMap[ifName].Dimensions[3] }.ToList();
        }

        // 1.
        Ssr rstride_s = new(-1, PackStride(ifStride[0], ifStride[1], ifStride[2]), false);
        if (ofStride is null)
        {
            ofStride = new[] { _glb.GlbMap[ItemName.Ofmap].Dimensions[1], _glb.GlbMap[ItemName.Ofmap].Dimensions[2], _glb.GlbMap[ItemName.Ofmap].Dimensions[3] }.ToList();
            if (inputType == DataTypes.Float16 && (outputType == DataTypes.UInt8 || outputType == DataTypes.Int8))
            {
                ofStride[2] *= 2;
            }
        }

        Ssr rstrideD = new(-1, PackStride(ofStride[0], ofStride[1], ofStride[2]), false);
        GnneActionMfuPdp1Conf1 pdp1Conf1 = new(1, strideW, strideH, rstrideD, rstride_s, pdpOp);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf1 is null || _latestConfActions.Pdp1Conf1! != pdp1Conf1)
#endif
        {
            var strideRnS = _gprHandler.SetGprItem(ifStride[0]);
            var strideRcS = _gprHandler.SetGprItem(ifStride[1]);
            var strideRhS = _gprHandler.SetGprItem(ifStride[2]);
            rstride_s = _ssRegHandler.SetSsrItem(PackStride(ifStride[0], ifStride[1], ifStride[2]));
            Actions.Add(new GnneActionPackStrideReg(strideRnS, strideRcS, strideRhS, rstride_s));

            var strideRnD = _gprHandler.SetGprItem(ofStride[0]);
            var strideRcD = _gprHandler.SetGprItem(ofStride[1]);
            var strideRhD = _gprHandler.SetGprItem(ofStride[2]);
            rstrideD = _ssRegHandler.SetSsrItem(PackStride(ofStride[0], ofStride[1], ofStride[2]));
            Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, rstrideD));

            pdp1Conf1 = new(1, strideW, strideH, rstrideD, rstride_s, pdpOp);
            _latestConfActions.Pdp1Conf1 = pdp1Conf1;
            Actions.Add(pdp1Conf1);
        }

        // 2.
        int countW = ofmap[3].Length;
        int countH = ofmap[2].Length;
        int activeH = Math.Min(GNNEEnv.MfuPuHeight, ifmap[1].Length * kernelH);
        int peChannels = activeH / kernelH;
        int peH = kernelH * peChannels;
        int peLastChannels = peChannels;
        if (ifmap[1].Length % peChannels != 0)
        {
            peLastChannels = ifmap[1].Length % peChannels;
        }

        int peLastH = kernelH * peLastChannels;

        // pad_value is fp16 for pdp1
        int padValue = BitConverter.ToInt16(BitConverter.GetBytes(((TensorConst)call[GNNEPdp1.Value]).Value.ToScalar<Half>()), 0);

        Gpr rcountW = new(-1, countW, false);
        Gpr rcountH = new(-1, countH, false);
        Gpr rpeH = new(-1, peH, false);
        Gpr rpeLastH = new(-1, peLastH, false);
        GnneActionMfuPdp1Conf2 pdp1Conf2 = new(2, rcountW, rcountH, rpeH, rpeLastH);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf2 is null || _latestConfActions.Pdp1Conf2! != pdp1Conf2)
#endif
        {
            rcountW = _gprHandler.SetGprItem(countW);
            rcountH = _gprHandler.SetGprItem(countH);
            rpeH = _gprHandler.SetGprItem(peH);
            rpeLastH = _gprHandler.SetGprItem(peLastH);
            pdp1Conf2 = new(2, rcountW, rcountH, rpeH, rpeLastH);
            _latestConfActions.Pdp1Conf2 = pdp1Conf2;
            Actions.Add(pdp1Conf2);
        }

        // 3.
        Ssr sspad = new(-1, PackShape(ifmap.PadH.Before, ifmap.PadH.After, ifmap.PadW.Before, ifmap.PadW.After), false);
        Gpr rpeChannels = new(-1, peChannels, false);
        Gpr rpeLastChannels = new(-1, peLastChannels, false);
        Gpr rpadValue = new(-1, padValue, false);
        GnneActionMfuPdp1Conf3 pdp1Conf3 = new(3, rpeChannels, rpeLastChannels, rpadValue, sspad);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf3 is null || _latestConfActions.Pdp1Conf3! != pdp1Conf3)
#endif
        {
            var shapeRn = _gprHandler.SetGprItem(ifmap.PadH.Before);
            var shapeRc = _gprHandler.SetGprItem(ifmap.PadH.After);
            var shapeRh = _gprHandler.SetGprItem(ifmap.PadW.Before);
            var shapeRw = _gprHandler.SetGprItem(ifmap.PadW.After);
            sspad = _ssRegHandler.SetSsrItem(PackShape(ifmap.PadH.Before, ifmap.PadH.After, ifmap.PadW.Before, ifmap.PadW.After));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, sspad));

            rpeChannels = _gprHandler.SetGprItem(peChannels);
            rpeLastChannels = _gprHandler.SetGprItem(peLastChannels);
            rpadValue = _gprHandler.SetGprItem(padValue);
            pdp1Conf3 = new(3, rpeChannels, rpeLastChannels, rpadValue, sspad);
            _latestConfActions.Pdp1Conf3 = pdp1Conf3;
            Actions.Add(pdp1Conf3);
        }

        // 4.
        Gpr rwindowW = new(-1, kernelW, false);
        Gpr rwindowH = new(-1, kernelH, false);
        int scaleSumPooling = BitConverter.ToInt16(BitConverter.GetBytes(sumScale), 0);
        Gpr rscale = new(-1, scaleSumPooling, false);
        bool enableH2C = false;
        bool enableBw = false;
        GnneActionMfuPdp1Conf4 pdp1Conf4 = new(4, rwindowW, rwindowH, rscale, enableH2C, enableBw);
#if K230_INST_OPT
        if (_latestConfActions.Pdp1Conf4 is null || _latestConfActions.Pdp1Conf4! != pdp1Conf4)
#endif
        {
            rwindowW = _gprHandler.SetGprItem(kernelW);
            rwindowH = _gprHandler.SetGprItem(kernelH);
            rscale = _gprHandler.SetGprItem(scaleSumPooling);
            pdp1Conf4 = new(4, rwindowW, rwindowH, rscale, enableH2C, enableBw);
            _latestConfActions.Pdp1Conf4 = pdp1Conf4;
            Actions.Add(pdp1Conf4);
        }

        // 5.
        // if (input_type != DataTypes.Float16)
        {
            var deqP = ((TensorConst)call[GNNEPdp1.DequantParams]).Value.ToScalar<DeQuantizeParam>();
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)deqP.Scale), 0);
            int bias = deqP.ZeroPoint;
            rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);
            GnneActionMfuPdp1ConfDeq pdp1ConfDeq = new(6, rscale, rbias, inputType, ((TensorConst)call[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
#if K230_INST_OPT
            if (_latestConfActions.Pdp1ConfDeq is null || _latestConfActions.Pdp1ConfDeq! != pdp1ConfDeq)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                pdp1ConfDeq = new(6, rscale, rbias, inputType, ((TensorConst)call[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
                _latestConfActions.Pdp1ConfDeq = pdp1ConfDeq;
                Actions.Add(pdp1ConfDeq);
            }
        }

        // 6.
        // if (output_type != DataTypes.Float16)
        {
            var qP = ((TensorConst)call[GNNEPdp1.QuantParams]).Value.ToScalar<QuantizeParam>();
            int scale = BitConverter.ToInt16(BitConverter.GetBytes((Half)qP.Scale), 0);
            int bias = BitConverter.ToInt16(BitConverter.GetBytes((Half)qP.ZeroPoint), 0);
            rscale = new(-1, scale, false);
            Gpr rbias = new(-1, bias, false);

            // TODO: 区分shiftbits
            GnneActionMfuPdp1ConfQuant pdp1ConfQuant = new(7, rscale, rbias, outputType, ((TensorConst)call[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
#if K230_INST_OPT
            if (_latestConfActions.Pdp1ConfQuant is null || _latestConfActions.Pdp1ConfQuant! != pdp1ConfQuant)
#endif
            {
                rscale = _gprHandler.SetGprItem(scale);
                rbias = _gprHandler.SetGprItem(bias);
                pdp1ConfQuant = new(7, rscale, rbias, outputType, ((TensorConst)call[GNNEPdp1.ShiftBits]).Value.ToScalar<int>());
                _latestConfActions.Pdp1ConfQuant = pdp1ConfQuant;
                Actions.Add(pdp1ConfQuant);
            }
        }

        // 7.
        {
            var shapeRn = _gprHandler.SetGprItem(ifmap[0].Length);
            var shapeRc = _gprHandler.SetGprItem(ifmap[1].Length);
            var shapeRh = _gprHandler.SetGprItem(ifmap[2].Length);
            var shapeRw = _gprHandler.SetGprItem(ifmap[3].Length);
            var rshape = _ssRegHandler.SetSsrItem(PackShape(ifmap[0].Length, ifmap[1].Length, ifmap[2].Length, ifmap[3].Length));
            Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

            int addrD = (iPp * _glb.GlbMap[ItemName.Ofmap].AllocatedBytes) + offsetD;
            int addrS = (iPp * _glb.GlbMap[ifName].AllocatedBytes) + offsetS;
            var raddrD = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ItemName.Ofmap].Mmu.Id, addrD));
            var raddrS = _gprHandler.SetGprItem(ToGlbAddr(_glb.GlbMap[ifName].Mmu.Id, addrS));
            if (GNNEEnv.UseCcr)
            {
                UpdateCcr(ccrsToSet, ccrsToClr);
            }

            Actions.Add(new GnneActionMfuPdp1Compute(raddrD, raddrS, rshape));
        }

        if (!GNNEEnv.UseCcr)
        {
            Actions.Add(new GnneActionFence());
        }
    }

    public void UpdateMfuMemset(SegmentND slice, SegmentND tensor, int value, DataType l2Datatype, int mmuItem, int iPp, int offsetD = 0, List<CcrSet> ccrsToSet = null!, List<CcrClr> ccrsToClr = null!)
    {
        var strideRnD = _gprHandler.SetGprItem(tensor[1].Length);
        var strideRcD = _gprHandler.SetGprItem(tensor[2].Length);
        var strideRhD = _gprHandler.SetGprItem(tensor[3].Length);
        var rstrideD = _ssRegHandler.SetSsrItem(PackStride(tensor[1].Length, tensor[2].Length, tensor[3].Length));
        Actions.Add(new GnneActionPackStrideReg(strideRnD, strideRcD, strideRhD, rstrideD));

        var shapeRn = _gprHandler.SetGprItem(slice[0].Length);
        var shapeRc = _gprHandler.SetGprItem(slice[1].Length);
        var shapeRh = _gprHandler.SetGprItem(slice[2].Length);
        var shapeRw = _gprHandler.SetGprItem(slice[3].Length);
        var rshape = _ssRegHandler.SetSsrItem(PackShape(slice[0].Length, slice[1].Length, slice[2].Length, slice[3].Length));
        Actions.Add(new GnneActionPackShapeReg(shapeRn, shapeRc, shapeRh, shapeRw, rshape));

        int padValue = value;
        var rpadValue = _gprHandler.SetGprItem(padValue);

        int addrD = (iPp * _glb.GlbMap[ItemName.Ofmap].AllocatedBytes) + offsetD;
        var raddrD = _gprHandler.SetGprItem(ToGlbAddr(mmuItem, addrD));

        Actions.Add(new GnneActionMfuMemset(raddrD, rpadValue, rstrideD, rshape, l2Datatype));
        Actions.Add(new GnneActionFence());
    }

    // TODO: 支持ai2d
    // void update_ai2d_pad(Call pad, int i_pp, SegmentND ifmap, SegmentND ifmap_sram, SegmentND ofmap, SegmentND ofmap_sram, List<CcrSet> ccrs_to_set = null, List<CcrClr> ccrs_to_clr = null, int offset_s = 0, bool is_last_ai2d_invoke = false)
    // {
    //     ai2d_config config;
    //     config.channel = ifmap_sram[1].Length;
    //     config.dst_channel = config.channel;
    //     config.csc_en = 0;
    //
    //     config.src_format = (uint)ai2d_format::NCHW_FMT;
    //     config.dst_format = config.src_format;
    //     config.src_ind = 0;
    //     config.dst_ind = config.src_ind;
    //     config.shift = 0;
    //     config.sign = pad[Ai2dPad.Input].CheckedDataType == DataTypes.UInt8 ? 0 : 1;
    //
    //     // TODO: 缺少pad_mode
    //     switch (pad.pad_mode())
    //     {
    //     case pad_mode_t::pad_constant:
    //         config.pad_mod = (uint)ai2d_pad_mode::constant;
    //         break;
    //     case pad_mode_t::pad_edge:
    //         config.pad_mod = (uint)ai2d_pad_mode::copy;
    //         break;
    //     case pad_mode_t::pad_reflect:
    //         config.pad_mod = (uint)ai2d_pad_mode::mirror;
    //         break;
    //     default:
    //         throw new Exception("unsupported ai2d_pad mode");
    //     }
    //     if (pad.pad_mode() == pad_mode_t::pad_constant)
    //     {
    //         if (pad[Ai2dPad.Input].CheckedDataType == DataTypes.UInt8)
    //             config.const_pad_ch0 = (uint)pad.pad_value().as<uint8_t>();
    //         else if (pad[Ai2dPad.Input].CheckedDataType == DataTypes.Int8)
    //             config.const_pad_ch0 = (uint)pad.pad_value().as<int8_t>();
    //         else if (pad[Ai2dPad.Input].CheckedDataType == DataTypes.UInt16)
    //             config.const_pad_ch0 = (uint)pad.pad_value().as<uint16_t>();
    //         else if (pad[Ai2dPad.Input].CheckedDataType == DataTypes.Int16)
    //             config.const_pad_ch0 = (uint)pad.pad_value().as<int16_t>();
    //         else
    //             assert(false && "unsupported ai2d_pad dtype");
    //
    //         config.const_pad_ch1 = config.const_pad_ch0;
    //         config.const_pad_ch2 = config.const_pad_ch0;
    //         config.const_pad_ch3 = config.const_pad_ch0;
    //     }
    //
    //     config.src_ch0_width_layout = ofmap[3].Length;
    //     config.src_ch1_width_layout = config.src_ch0_width_layout;
    //     config.src_ch2_width_layout = config.src_ch0_width_layout;
    //     config.src_ch3_width_layout = config.src_ch0_width_layout;
    //
    //     config.dst_ch0_width_layout = ofmap[3].Length;
    //     config.dst_ch1_width_layout = config.dst_ch0_width_layout;
    //     config.dst_ch2_width_layout = config.dst_ch0_width_layout;
    //     config.dst_ch3_width_layout = config.dst_ch0_width_layout;
    //
    //     config.src_x = 0;
    //     config.src_y = 0;
    //     config.dst_x = 0;
    //     config.dst_y = 0;
    //
    //     config.src_height_shape = ifmap_sram[2].Length;
    //     config.src_width_shape = ifmap_sram[3].Length;
    //     // weird, but hardware needs this
    //     config.dst_height_shape = ifmap_sram[2].Length;
    //     config.dst_width_shape = ifmap_sram[3].Length;
    //
    //     config.pad_t = ifmap_sram.PadH.Before;
    //     config.pad_b = ifmap_sram.PadH.After;
    //     config.pad_l = ifmap_sram.PadW.Before;
    //     config.pad_r = ifmap_sram.PadW.After;
    //
    //     config.src_ch0_ptr = i_pp * glb_.glb_map[K230.item_name.ofmap].allocated_bytes
    //         + ((ifmap_sram[2].Start - ifmap[2].Start) * config.dst_ch0_width_layout + (ifmap_sram[3].Start - ifmap[3].Start)) * TileUtilities.get_bytes_per_element(pad[Ai2dPad.Input].CheckedDataType)
    //         + offset_s * TileUtilities.get_bytes_per_element(pad[Ai2dPad.Input].CheckedDataType);
    //     config.src_ch0_ptr += (glb_.glb_map[K230.item_name.ofmap].mmu.id << 28);
    //     config.src_ch1_ptr = config.src_ch0_ptr + ofmap[2].Length * ofmap[3].Length;
    //     config.src_ch2_ptr = config.src_ch1_ptr + ofmap[2].Length * ofmap[3].Length;
    //     config.src_ch3_ptr = config.src_ch2_ptr + ofmap[2].Length * ofmap[3].Length;
    //
    //     config.dst_ch0_ptr = i_pp * glb_.glb_map[K230.item_name.ofmap].allocated_bytes
    //         + ((ofmap_sram[2].Start - ofmap[2].Start) * config.dst_ch0_width_layout + (ofmap_sram[3].Start - ofmap[3].Start)) * TileUtilities.get_bytes_per_element(pad[Ai2dPad.Input].CheckedDataType);
    //     config.dst_ch0_ptr += (glb_.glb_map[K230.item_name.ofmap].mmu.id << 28);
    //     config.dst_ch1_ptr = config.dst_ch0_ptr + ofmap[2].Length * ofmap[2].Length;
    //     config.dst_ch2_ptr = config.dst_ch1_ptr + ofmap[3].Length * ofmap[2].Length;
    //     config.dst_ch3_ptr = config.dst_ch2_ptr + ofmap[2].Length * ofmap[2].Length;
    //
    //     config.intr_mask = is_last_ai2d_invoke ? 0 : 1;
    //
    //     for (int idx = 0; idx < 36; idx++)
    //     {
    //         uint value = config.get_addr_value(idx);
    //         var rs = gpr_handler_.set_gpr_item(value);
    //         if (idx < 8)
    //             actions_.Add(new gnne_action_extraw ( idx * 4, rs.value, rs.index, 0 ));
    //         else
    //             actions_.Add(new gnne_action_extrw ( idx * 4, rs.value, rs.index, 0 ));
    //     }
    //
    //     if (K230.GNNEEnv.use_ccr)
    //     {
    //         update_ccr(ccrs_to_set, ccrs_to_clr);
    //     }
    //     actions_.Add(new gnne_action_ai2d_compute {});
    //
    //     if (!K230.GNNEEnv.use_ccr)
    //     {
    //         actions_.Add(new gnne_action_fence());
    //     }
    // }
    public void UpdateAi2dResize(Ai2dConfig config, List<CcrSet> ccrs_to_set = null!, List<CcrClr> ccrs_to_clr = null!, bool write_all = true)
    {
        for (int idx = write_all ? 0 : 28; idx < 36; idx++)
        {
            var value = config.GetAddrValue((uint)idx);
            var rs = _gprHandler.SetGprItem((int)value);
            if (idx < 8)
            {
                Actions.Add(new GnneActionExtraw(idx * 4, rs, rs.Index, 0));
            }
            else
            {
                Actions.Add(new GnneActionExtrw(idx * 4, rs, rs.Index, 0));
            }
        }

        if (config.IntrMask == 0)
        {
            if (GNNEEnv.UseCcr)
            {
                UpdateCcr(ccrs_to_set, ccrs_to_clr);
            }

            Actions.Add(new GnneActionAi2dCompute());

            if (!GNNEEnv.UseCcr)
            {
                Actions.Add(new GnneActionFence());
            }
        }
    }

    public void ResetActions()
    {
        Actions.Clear();
    }

    public void ReshapeSpecialScenario(ref List<int> strideDdr, ref GNNEShape shape, ref List<int> strideGlb)
    {
        if (strideDdr[0] == shape[1]
            && strideDdr[1] == shape[2]
            && strideDdr[2] == shape[3]
            && strideGlb[0] == shape[1]
            && strideGlb[1] == shape[2]
            && strideGlb[2] == shape[3]
            && strideDdr[0] * strideDdr[1] * strideDdr[2] < 65536
            && strideGlb[0] * strideGlb[1] * strideGlb[2] < 65536
            && shape[0] * shape[1] * shape[2] * shape[3] < 65536)
        {
            strideDdr = new[] { 1, 1, strideDdr[0] * strideDdr[1] * strideDdr[2] }.ToList();
            shape = new GNNEShape(1, 1, 1, shape[0] * shape[1] * shape[2] * shape[3]);
            strideGlb = new[] { 1, 1, strideGlb[0] * strideGlb[1] * strideGlb[2] }.ToList();
        }
        else if (strideDdr[1] == shape[2]
                 && strideDdr[2] == shape[3]
                 && strideGlb[1] == shape[2]
                 && strideGlb[2] == shape[3]
                 && strideDdr[0] * strideDdr[1] * strideDdr[2] < 65536
                 && strideGlb[0] * strideGlb[1] * strideGlb[2] < 65536
                 && shape[1] * shape[2] * shape[3] < 65536)
        {
            strideDdr = new[] { 1, 1, strideDdr[0] * strideDdr[1] * strideDdr[2] }.ToList();
            shape = new GNNEShape(shape[0], 1, 1, shape[1] * shape[2] * shape[3]);
            strideGlb = new[] { 1, 1, strideGlb[0] * strideGlb[1] * strideGlb[2] }.ToList();
        }
        else if (strideDdr[2] == shape[3]
                 && strideGlb[2] == shape[3]
                 && strideDdr[1] * strideDdr[2] < 65536
                 && strideGlb[1] * strideGlb[2] < 65536
                 && shape[2] * shape[3] < 65536)
        {
            strideDdr = new[] { strideDdr[0], 1, strideDdr[1] * strideDdr[2] }.ToList();
            shape = new GNNEShape(shape[0], shape[1], 1, shape[2] * shape[3]);
            strideGlb = new[] { strideGlb[0], 1, strideGlb[1] * strideGlb[2] }.ToList();
        }
    }

    private int ToGlbAddr(int mmuItem, int addr)
    {
        return (mmuItem << 28) + addr;
    }
}
