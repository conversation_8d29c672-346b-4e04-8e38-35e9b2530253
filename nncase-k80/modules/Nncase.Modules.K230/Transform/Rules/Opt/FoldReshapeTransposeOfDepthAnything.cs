// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.K230;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public partial class FoldReshapeTransposeOfDepthAnything : RewriteRule<Pattern>
{
    /// <inheritdoc/>
    public override Pattern Pattern { get; } =
        IsReshape(
            "reshape1",
            "reshape1Call",
            _ => true,
            IsTranspose(
                "transpose1",
                "transpose1Call",
                _ => true,
                IsReshape(
                    "reshape2",
                    "reshape2Call",
                    _ => true,
                    IsTranspose(
                        "transpose2",
                        "transpose2Call",
                        _ => true,
                        IsReshape(
                            "reshape3",
                            "reshape3Call",
                            _ => true,
                            <PERSON><PERSON><PERSON><PERSON><PERSON>("input"),
                            IsTensorConst("shape3")),
                        IsTensorConst("perm2")),
                    IsTensorConst("shape2")),
                IsTensorConst("perm1")),
            IsTensorConst("shape1")) with
        { TypePattern = HasRank(5) & HasFixedShape() };

    public Expr? GetReplace(Expr input, int[] perm1, int[] perm2, int[] shape1)
    {
        if (perm1.SequenceEqual(new[] { 0, 1, 3, 2 }) && perm2.SequenceEqual(new[] { 2, 0, 3, 1 }))
        {
            return IR.F.Tensors.Reshape(IR.F.Tensors.Transpose(IR.F.Tensors.Reshape(input, new[] { shape1[3], shape1[0], shape1[2], shape1[4] }), new[] { 1, 2, 0, 3 }), shape1);
        }

        return null;
    }
}
