// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using static Nncase.IR.F.Tensors;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class OptCast : IRewriteRule
{
    /// <inheritdoc />
    public IPattern Pattern { get; }
        = IsCast(
            "cast",
            "call",
            cast => cast.NewType == DataTypes.Float16 || cast.NewType == DataTypes.Float32,
            IsWildcard("input") with { TypePattern = HasFixedShape() & HasRank(r => r <= 4, "Only support rank <= 4") });

    private Expr? GetReplace(IR.Tensors.Cast cast, Call call, Expr input)
    {
        if (input.CheckedDataType != DataTypes.Float16 && input.CheckedDataType != DataTypes.Float32)
        {
            return null;
        }

        var inShape = input.CheckedShape.ToValueArray();
        if (inShape.Any(i => i > 65535))
        {
            return null;
        }

        var newInShape = new[] { 1, 1, 1, 1 };
        Array.Copy(input.CheckedShape.ToValueArray(), 0, newInShape, newInShape.Length - input.CheckedShape.Rank, input.CheckedShape.Rank);
        var midType = DataTypes.Float16;
        if (input.CheckedShape.Rank < 4)
        {
            var reshape = Reshape(input, newInShape);
            var sof = GNNEStore(call.CheckedDataType, GNNELoad((PrimType)midType, reshape));
            return Reshape(sof, call.CheckedShape);
        }
        else
        {
            var sof = GNNEStore(call.CheckedDataType, GNNELoad((PrimType)midType, input));
            return sof;
        }
    }
}
