﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.IR;
using Nncase.IR.Math;
using Nncase.PatternMatch;
using static Nncase.IR.K230.F.Tensors;
using static Nncase.IR.K230.GNNETypePatternUtility;
using static Nncase.PatternMatch.F.K230;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.K230;

[RuleGenerator]
public sealed partial class PadQLinearMatmul : IRewriteRule
{
    /// <inheritdoc/>
    public IPattern Pattern { get; } = IsQLinearMatMul(
        "qLinearMatMul",
        "qLinearMatMulCall",
        _ => true,
        IsWildcard("lhs"),
        IsTensorConst("rhs"),
        IsWildcard("lhsScale"),
        IsTensorConst("lhsZeroPoint"),
        IsTensorConst("rhsScale"),
        IsTensorConst("rhsZeroPoint"),
        IsTensorConst("outputScale"),
        IsTensorConst("outputZeroPoint"));

    private Expr? GetReplace(QLinearMatMul qLinearMatMul, Call qLinearMatMulCall, Expr lhs, Tensor rhs, Expr lhsScale, TensorConst lhsZeroPoint, TensorConst rhsScale, TensorConst rhsZeroPoint, TensorConst outputScale, TensorConst outputZeroPoint)
    {
        var isPadded = false;
        var pads = IR.Shapes.Paddings.Zeros(lhs.CheckedShape.Rank).ToDimensionArray();
        var maxShape = CompilerServices.GetMaxShape(lhs.CheckedShape);
        for (var i = 0; i < lhs.CheckedShape.Rank; i++)
        {
            if (!lhs.CheckedShape[i].IsFixed)
            {
                pads[i, 1] = maxShape[i] - lhs.CheckedShape[i];
                isPadded = true;
            }
        }

        if (isPadded)
        {
            var padValue = Tensor.FromScalar(DataTypes.Float32, 0f).CastTo(lhs.CheckedDataType);
            var pad = IR.F.NN.Pad(lhs, Dimension.ConcatPadding(pads), PadMode.Constant, padValue);
            var newMm = IR.F.Math.QLinearMatMul(pad, rhs, lhsScale, lhsZeroPoint, rhsScale, rhsZeroPoint, outputScale, outputZeroPoint, qLinearMatMul.OutputDataType);
            return IR.F.Tensors.Slice(newMm, Enumerable.Repeat(0L, qLinearMatMulCall.CheckedShape.Rank).ToArray(), qLinearMatMulCall.CheckedShape, qLinearMatMulCall.CheckedShape.Rank);
        }

        return null;
    }
}
