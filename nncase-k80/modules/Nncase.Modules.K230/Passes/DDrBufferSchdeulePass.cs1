// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Diagnostics.CodeAnalysis;
using System.Linq;
using System.Reactive;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.Passes.Analysis;
using Nncase.Passes.Rules;
using Nncase.PatternMatch;
using Nncase.Schedule.Bufferize;
using Nncase.TIR;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes;

/// <summary>
/// merge call/ assgin ddr buffer start/layout.
/// </summary>
public sealed class DDrBufferSchdeulePass : ModulePass
{
    private readonly Dictionary<string, Dictionary<MemoryLocation, long>> _moduleUsage = new();

    private readonly Dictionary<string, Dictionary<Const, ValueRange<long>>> _moduleRdataMaps = new();

    private readonly bool _enbaleMergeCall;

    public DDrBufferSchdeulePass(bool enableMergeCall = false)
    {
        _enbaleMergeCall = enableMergeCall;
    }

    private IAnalyzerManager AnalyzerManager => CompileSession.GetRequiredService<IAnalyzerManager>();

    /// <inheritdoc/>
    protected override async Task<IRModule> RunCoreAsync(IRModule module, RunPassContext options)
    {
        for (int i = 0; i < module.Functions.Count; i++)
        {
            if (module.Functions[i] is TIR.PrimFunction prim_func)
            {
                if (!prim_func.SchedResult.IsScheduled)
                {
                    var rewriter = new DDrBufferRewriter(_moduleUsage, _moduleRdataMaps);
                    var post = (TIR.PrimFunction)rewriter.Rewrite(prim_func); // changed ddr buffer.
                    if (rewriter.IsMutated)
                    {
                        post.SchedResult.DataUsage = rewriter.DataUsage;
                        post.SchedResult.IsScheduled = true;
                    }

                    module.Replace(i, prim_func);
                }
            }
        }

        _moduleRdataMaps.Clear();
        _moduleUsage.Clear();

        return await Task.FromResult(module);
    }
}

internal sealed class DDrBufferRewriter : ExprRewriter
{
    public DDrBufferRewriter()
    {
    }

    public PrimFunction Entry => (PrimFunction)VisitRoot!;

    protected override BaseExpr RewriteLeafFusion(Fusion expr)
    {
        var bufferizeVisitor = new BufferizeVisitor(new[] { expr });
        bufferizeVisitor.Bufferize();

        return expr;
    }
}
