//---------------------------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated by T4 template.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
//---------------------------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.Reactive;

namespace Nncase.IR;

public partial class ExprCloner<TContext>
{
    /// <inheritdoc />
    protected override BaseExpr VisitLeafCall(Call expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Target, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Arguments, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                target: Clone(expr.Target, context),
                arguments: CloneArray(expr.Arguments, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafFunction(Function expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Parameters, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (!CanVisitFunctionBody(expr))
        {
            return expr;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                parameters: CloneArray(expr.Parameters, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafFusion(Fusion expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Parameters, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (!CanVisitFunctionBody(expr))
        {
            return expr;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                parameters: CloneArray(expr.Parameters, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafIf(If expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Condition, context))
            {
                return true;
            }

            if (IsMutated(expr.Then, context))
            {
                return true;
            }

            if (IsMutated(expr.Else, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Arguments, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                condition: Clone(expr.Condition, context),
                then: Clone(expr.Then, context),
                @else: Clone(expr.Else, context),
                arguments: CloneArray(expr.Arguments, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafMarker(Marker expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Target, context))
            {
                return true;
            }

            if (IsMutated(expr.Attribute, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                target: Clone(expr.Target, context),
                attribute: Clone(expr.Attribute, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafNone(None expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafOp(Op expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafPrimFunctionWrapper(PrimFunctionWrapper expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Target, context))
            {
                return true;
            }

            return false;
        }

        if (!CanVisitFunctionBody(expr))
        {
            return expr;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                target: Clone(expr.Target, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafFunctionWrapper(FunctionWrapper expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Target, context))
            {
                return true;
            }

            return false;
        }

        if (!CanVisitFunctionBody(expr))
        {
            return expr;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                target: Clone(expr.Target, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafTensorConst(TensorConst expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafTuple(IR.Tuple expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Fields, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                fields: CloneArray(expr.Fields, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafTupleConst(TupleConst expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafPhysicalBuffer(TIR.PhysicalBuffer expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Start, context))
            {
                return true;
            }

            if (IsMutated(expr.Size, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                start: Clone(expr.Start, context),
                size: Clone(expr.Size, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafMemSpan(TIR.MemSpan expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Buffer, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                buffer: Clone(expr.Buffer, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafVar(Var expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafBlock(TIR.Block expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            if (IsMutated(expr.InitBody, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.IterVars, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Reads, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Writes, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.AllocBuffers, context))
            {
                return true;
            }

            if (IsMutated(expr.Predicate, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                body: Clone(expr.Body, context),
                initBody: Clone(expr.InitBody, context),
                iterVars: CloneArray(expr.IterVars, context),
                reads: CloneArray(expr.Reads, context),
                writes: CloneArray(expr.Writes, context),
                allocBuffers: CloneArray(expr.AllocBuffers, context),
                predicate: Clone(expr.Predicate, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafBuffer(TIR.Buffer expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.MemSpan, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Dimensions, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Strides, context))
            {
                return true;
            }

            if (IsMutatedType(expr.DistributedType, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                memSpan: Clone(expr.MemSpan, context),
                dimensions: CloneArray(expr.Dimensions, context),
                strides: CloneArray(expr.Strides, context),
                distributedType: CloneType(expr.DistributedType, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafBufferRegion(TIR.BufferRegion expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Buffer, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Region, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                buffer: Clone(expr.Buffer, context),
                region: CloneArray(expr.Region, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafFor(TIR.For expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.LoopVar, context))
            {
                return true;
            }

            if (IsMutated(expr.Domain, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                loopVar: Clone(expr.LoopVar, context),
                domain: Clone(expr.Domain, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafIfThenElse(TIR.IfThenElse expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Condition, context))
            {
                return true;
            }

            if (IsMutated(expr.Then, context))
            {
                return true;
            }

            if (IsMutated(expr.Else, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                condition: Clone(expr.Condition, context),
                then: Clone(expr.Then, context),
                @else: Clone(expr.Else, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafLet(TIR.Let expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Var, context))
            {
                return true;
            }

            if (IsMutated(expr.Expression, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                var: Clone(expr.Var, context),
                expression: Clone(expr.Expression, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafPrimFunction(TIR.PrimFunction expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Parameters, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (!CanVisitFunctionBody(expr))
        {
            return expr;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                parameters: CloneArray(expr.Parameters, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafSequential(TIR.Sequential expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Fields, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                fields: CloneArray(expr.Fields, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafRange(TIR.Range expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Start, context))
            {
                return true;
            }

            if (IsMutated(expr.Stop, context))
            {
                return true;
            }

            if (IsMutated(expr.Step, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                start: Clone(expr.Start, context),
                stop: Clone(expr.Stop, context),
                step: Clone(expr.Step, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafIterVar(TIR.IterVar expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Value, context))
            {
                return true;
            }

            if (IsMutated(expr.Dom, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                value: Clone(expr.Value, context),
                dom: Clone(expr.Dom, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafReturn(TIR.Return expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Values, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                values: CloneArray(expr.Values, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineDim(Affine.AffineDim expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineExtent(Affine.AffineExtent expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineSymbol(Affine.AffineSymbol expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineConstant(Affine.AffineConstant expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineAddBinary(Affine.AffineAddBinary expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Lhs, context))
            {
                return true;
            }

            if (IsMutated(expr.Rhs, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                lhs: Clone(expr.Lhs, context),
                rhs: Clone(expr.Rhs, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineMulBinary(Affine.AffineMulBinary expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Lhs, context))
            {
                return true;
            }

            if (IsMutated(expr.Rhs, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                lhs: Clone(expr.Lhs, context),
                rhs: Clone(expr.Rhs, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineDivBinary(Affine.AffineDivBinary expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Lhs, context))
            {
                return true;
            }

            if (IsMutated(expr.Rhs, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                lhs: Clone(expr.Lhs, context),
                rhs: Clone(expr.Rhs, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineDomain(Affine.AffineDomain expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Offset, context))
            {
                return true;
            }

            if (IsMutated(expr.Extent, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                offset: Clone(expr.Offset, context),
                extent: Clone(expr.Extent, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineRange(Affine.AffineRange expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Offset, context))
            {
                return true;
            }

            if (IsMutated(expr.Extent, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                offset: Clone(expr.Offset, context),
                extent: Clone(expr.Extent, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineMap(Affine.AffineMap expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Domains, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Symbols, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Results, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                domains: CloneArray(expr.Domains, context),
                symbols: CloneArray(expr.Symbols, context),
                results: CloneArray(expr.Results, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAffineRelation(Affine.AffineRelation expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Domains, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Symbols, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Results, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                domains: CloneArray(expr.Domains, context),
                symbols: CloneArray(expr.Symbols, context),
                results: CloneArray(expr.Results, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafGrid(Affine.Grid expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.DomainParameter, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.BodyParameters, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.AccessMaps, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Buffers, context))
            {
                return true;
            }

            if (IsMutatedArray(expr.Reads, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                domainParameter: Clone(expr.DomainParameter, context),
                bodyParameters: CloneArray(expr.BodyParameters, context),
                accessMaps: CloneArray(expr.AccessMaps, context),
                buffers: CloneArray(expr.Buffers, context),
                reads: CloneArray(expr.Reads, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafLoad(Affine.Load expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Source, context))
            {
                return true;
            }

            if (IsMutated(expr.Region, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                source: Clone(expr.Source, context),
                region: Clone(expr.Region, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafFor(Affine.For expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Domain, context))
            {
                return true;
            }

            if (IsMutated(expr.Body, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                domain: Clone(expr.Domain, context),
                body: Clone(expr.Body, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafBufferOf(Buffers.BufferOf expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Input, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                input: Clone(expr.Input, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafThreadIdDim(Distributed.ThreadIdDim expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafAsDim(AsDim expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Dim, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                dim: Clone(expr.Dim, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafUnknownDim(UnknownDim expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimVar(DimVar expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimConst(DimConst expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimPower(DimPower expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Dim, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                dim: Clone(expr.Dim, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimFraction(DimFraction expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Numerator, context))
            {
                return true;
            }

            if (IsMutated(expr.Denominator, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                numerator: Clone(expr.Numerator, context),
                denominator: Clone(expr.Denominator, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimRemainder(DimRemainder expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Numerator, context))
            {
                return true;
            }

            if (IsMutated(expr.Denominator, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                numerator: Clone(expr.Numerator, context),
                denominator: Clone(expr.Denominator, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimProduct(DimProduct expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Operands, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operands: CloneArray(expr.Operands, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimSum(DimSum expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Operands, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operands: CloneArray(expr.Operands, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimAbs(DimAbs expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Operand, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operand: Clone(expr.Operand, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimClamp(DimClamp expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Operand, context))
            {
                return true;
            }

            if (IsMutated(expr.MinValue, context))
            {
                return true;
            }

            if (IsMutated(expr.MaxValue, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operand: Clone(expr.Operand, context),
                minValue: Clone(expr.MinValue, context),
                maxValue: Clone(expr.MaxValue, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimCompareAndSelect(DimCompareAndSelect expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Value, context))
            {
                return true;
            }

            if (IsMutated(expr.Expected, context))
            {
                return true;
            }

            if (IsMutated(expr.TrueValue, context))
            {
                return true;
            }

            if (IsMutated(expr.FalseValue, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                value: Clone(expr.Value, context),
                expected: Clone(expr.Expected, context),
                trueValue: Clone(expr.TrueValue, context),
                falseValue: Clone(expr.FalseValue, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimMin(DimMin expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Operands, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operands: CloneArray(expr.Operands, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimMax(DimMax expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Operands, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operands: CloneArray(expr.Operands, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimPositive(DimPositive expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Operand, context))
            {
                return true;
            }

            if (IsMutated(expr.Extent, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                operand: Clone(expr.Operand, context),
                extent: Clone(expr.Extent, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafDimAt(DimAt expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Shape, context))
            {
                return true;
            }

            if (IsMutated(expr.Index, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                shape: Clone(expr.Shape, context),
                index: Clone(expr.Index, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafPadding(IR.Shapes.Padding expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Before, context))
            {
                return true;
            }

            if (IsMutated(expr.After, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                before: Clone(expr.Before, context),
                after: Clone(expr.After, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafPaddings(IR.Shapes.Paddings expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Values, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                values: CloneArray(expr.Values, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafRankedShape(RankedShape expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutatedArray(expr.Dimensions, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                dimensions: CloneArray(expr.Dimensions, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafUnrankedShape(UnrankedShape expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Value, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                value: Clone(expr.Value, context)
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafInvalidShape(InvalidShape expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafShapeVar(ShapeVar expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
            );
        }

        return expr;
    }

    /// <inheritdoc />
    protected override BaseExpr VisitLeafShapeOf(IR.Shapes.ShapeOf expr, TContext context)
    {
        bool IsOperandsMutated()
        {
            if (IsMutated(expr.Value, context))
            {
                return true;
            }

            return false;
        }

        if (CloneUnmutated || IsOperandsMutated())
        {
            return expr.With(
                value: Clone(expr.Value, context)
            );
        }

        return expr;
    }

}
