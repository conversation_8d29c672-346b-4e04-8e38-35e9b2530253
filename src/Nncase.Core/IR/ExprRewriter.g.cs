//---------------------------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated by T4 template.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
//---------------------------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.Reactive;

namespace Nncase.IR;

public partial class ExprRewriter<TContext>
{
    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafBaseFunction(BaseFunction expr, TContext context)
    {
        return RewriteLeafBaseFunction(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafCall(Call expr, TContext context)
    {
        return RewriteLeafCall(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafConst(Const expr, TContext context)
    {
        return RewriteLeafConst(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafFunction(Function expr, TContext context)
    {
        return RewriteLeafFunction(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafFusion(Fusion expr, TContext context)
    {
        return RewriteLeafFusion(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafIf(If expr, TContext context)
    {
        return RewriteLeafIf(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafMarker(Marker expr, TContext context)
    {
        return RewriteLeafMarker(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafNone(None expr, TContext context)
    {
        return RewriteLeafNone(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafOp(Op expr, TContext context)
    {
        return RewriteLeafOp(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafPrimFunctionWrapper(PrimFunctionWrapper expr, TContext context)
    {
        return RewriteLeafPrimFunctionWrapper(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafFunctionWrapper(FunctionWrapper expr, TContext context)
    {
        return RewriteLeafFunctionWrapper(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafTensorConst(TensorConst expr, TContext context)
    {
        return RewriteLeafTensorConst(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafTuple(IR.Tuple expr, TContext context)
    {
        return RewriteLeafTuple(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafTupleConst(TupleConst expr, TContext context)
    {
        return RewriteLeafTupleConst(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafPhysicalBuffer(TIR.PhysicalBuffer expr, TContext context)
    {
        return RewriteLeafPhysicalBuffer(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafMemSpan(TIR.MemSpan expr, TContext context)
    {
        return RewriteLeafMemSpan(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafVar(Var expr, TContext context)
    {
        return RewriteLeafVar(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafBlock(TIR.Block expr, TContext context)
    {
        return RewriteLeafBlock(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafBuffer(TIR.Buffer expr, TContext context)
    {
        return RewriteLeafBuffer(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafBufferRegion(TIR.BufferRegion expr, TContext context)
    {
        return RewriteLeafBufferRegion(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafFor(TIR.For expr, TContext context)
    {
        return RewriteLeafFor(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafIfThenElse(TIR.IfThenElse expr, TContext context)
    {
        return RewriteLeafIfThenElse(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafLet(TIR.Let expr, TContext context)
    {
        return RewriteLeafLet(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafPrimFunction(TIR.PrimFunction expr, TContext context)
    {
        return RewriteLeafPrimFunction(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafSequential(TIR.Sequential expr, TContext context)
    {
        return RewriteLeafSequential(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafRange(TIR.Range expr, TContext context)
    {
        return RewriteLeafRange(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafIterVar(TIR.IterVar expr, TContext context)
    {
        return RewriteLeafIterVar(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafReturn(TIR.Return expr, TContext context)
    {
        return RewriteLeafReturn(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineExpr(Affine.AffineExpr expr, TContext context)
    {
        return RewriteLeafAffineExpr(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineDim(Affine.AffineDim expr, TContext context)
    {
        return RewriteLeafAffineDim(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineExtent(Affine.AffineExtent expr, TContext context)
    {
        return RewriteLeafAffineExtent(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineSymbol(Affine.AffineSymbol expr, TContext context)
    {
        return RewriteLeafAffineSymbol(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineConstant(Affine.AffineConstant expr, TContext context)
    {
        return RewriteLeafAffineConstant(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineAddBinary(Affine.AffineAddBinary expr, TContext context)
    {
        return RewriteLeafAffineAddBinary(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineMulBinary(Affine.AffineMulBinary expr, TContext context)
    {
        return RewriteLeafAffineMulBinary(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineDivBinary(Affine.AffineDivBinary expr, TContext context)
    {
        return RewriteLeafAffineDivBinary(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineDomain(Affine.AffineDomain expr, TContext context)
    {
        return RewriteLeafAffineDomain(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineRange(Affine.AffineRange expr, TContext context)
    {
        return RewriteLeafAffineRange(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineMap(Affine.AffineMap expr, TContext context)
    {
        return RewriteLeafAffineMap(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAffineRelation(Affine.AffineRelation expr, TContext context)
    {
        return RewriteLeafAffineRelation(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafGrid(Affine.Grid expr, TContext context)
    {
        return RewriteLeafGrid(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafLoad(Affine.Load expr, TContext context)
    {
        return RewriteLeafLoad(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafFor(Affine.For expr, TContext context)
    {
        return RewriteLeafFor(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafBufferOf(Buffers.BufferOf expr, TContext context)
    {
        return RewriteLeafBufferOf(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafThreadIdDim(Distributed.ThreadIdDim expr, TContext context)
    {
        return RewriteLeafThreadIdDim(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimension(Dimension expr, TContext context)
    {
        return RewriteLeafDimension(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafAsDim(AsDim expr, TContext context)
    {
        return RewriteLeafAsDim(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafUnknownDim(UnknownDim expr, TContext context)
    {
        return RewriteLeafUnknownDim(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimVar(DimVar expr, TContext context)
    {
        return RewriteLeafDimVar(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimConst(DimConst expr, TContext context)
    {
        return RewriteLeafDimConst(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimPower(DimPower expr, TContext context)
    {
        return RewriteLeafDimPower(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimFraction(DimFraction expr, TContext context)
    {
        return RewriteLeafDimFraction(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimRemainder(DimRemainder expr, TContext context)
    {
        return RewriteLeafDimRemainder(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimProduct(DimProduct expr, TContext context)
    {
        return RewriteLeafDimProduct(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimSum(DimSum expr, TContext context)
    {
        return RewriteLeafDimSum(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimAbs(DimAbs expr, TContext context)
    {
        return RewriteLeafDimAbs(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimClamp(DimClamp expr, TContext context)
    {
        return RewriteLeafDimClamp(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimCompareAndSelect(DimCompareAndSelect expr, TContext context)
    {
        return RewriteLeafDimCompareAndSelect(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimMin(DimMin expr, TContext context)
    {
        return RewriteLeafDimMin(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimMax(DimMax expr, TContext context)
    {
        return RewriteLeafDimMax(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimPositive(DimPositive expr, TContext context)
    {
        return RewriteLeafDimPositive(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafDimAt(DimAt expr, TContext context)
    {
        return RewriteLeafDimAt(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafPadding(IR.Shapes.Padding expr, TContext context)
    {
        return RewriteLeafPadding(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafPaddings(IR.Shapes.Paddings expr, TContext context)
    {
        return RewriteLeafPaddings(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafShape(Shape expr, TContext context)
    {
        return RewriteLeafShape(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafRankedShape(RankedShape expr, TContext context)
    {
        return RewriteLeafRankedShape(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafUnrankedShape(UnrankedShape expr, TContext context)
    {
        return RewriteLeafUnrankedShape(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafInvalidShape(InvalidShape expr, TContext context)
    {
        return RewriteLeafInvalidShape(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafShapeVar(ShapeVar expr, TContext context)
    {
        return RewriteLeafShapeVar(expr, context);
    }

    /// <inheritdoc/>
    protected sealed override BaseExpr VisitLeafShapeOf(IR.Shapes.ShapeOf expr, TContext context)
    {
        return RewriteLeafShapeOf(expr, context);
    }

    /// <summary>
    /// Rewrite leaf <see cref="BaseFunction"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBaseFunction(BaseFunction expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Call"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafCall(Call expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Const"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafConst(Const expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Function"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFunction(Function expr, TContext context) => RewriteLeafBaseFunction(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Fusion"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFusion(Fusion expr, TContext context) => RewriteLeafBaseFunction(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="If"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafIf(If expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Marker"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafMarker(Marker expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="None"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafNone(None expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Op"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafOp(Op expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="PrimFunctionWrapper"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPrimFunctionWrapper(PrimFunctionWrapper expr, TContext context) => RewriteLeafBaseFunction(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="FunctionWrapper"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFunctionWrapper(FunctionWrapper expr, TContext context) => RewriteLeafBaseFunction(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TensorConst"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafTensorConst(TensorConst expr, TContext context) => RewriteLeafConst(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Tuple"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafTuple(IR.Tuple expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TupleConst"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafTupleConst(TupleConst expr, TContext context) => RewriteLeafConst(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.PhysicalBuffer"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPhysicalBuffer(TIR.PhysicalBuffer expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.MemSpan"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafMemSpan(TIR.MemSpan expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Var"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafVar(Var expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Block"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBlock(TIR.Block expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Buffer"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBuffer(TIR.Buffer expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.BufferRegion"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBufferRegion(TIR.BufferRegion expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.For"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFor(TIR.For expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.IfThenElse"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafIfThenElse(TIR.IfThenElse expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Let"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafLet(TIR.Let expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.PrimFunction"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPrimFunction(TIR.PrimFunction expr, TContext context) => RewriteLeafBaseFunction(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Sequential"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafSequential(TIR.Sequential expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Range"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafRange(TIR.Range expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.IterVar"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafIterVar(TIR.IterVar expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Return"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafReturn(TIR.Return expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineExpr"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineExpr(Affine.AffineExpr expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineDim(Affine.AffineDim expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineExtent"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineExtent(Affine.AffineExtent expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineSymbol"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineSymbol(Affine.AffineSymbol expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineConstant"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineConstant(Affine.AffineConstant expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineAddBinary"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineAddBinary(Affine.AffineAddBinary expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineMulBinary"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineMulBinary(Affine.AffineMulBinary expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineDivBinary"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineDivBinary(Affine.AffineDivBinary expr, TContext context) => RewriteLeafAffineExpr(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineDomain"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineDomain(Affine.AffineDomain expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineRange"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineRange(Affine.AffineRange expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineMap"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineMap(Affine.AffineMap expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineRelation"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineRelation(Affine.AffineRelation expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.Grid"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafGrid(Affine.Grid expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.Load"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafLoad(Affine.Load expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.For"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFor(Affine.For expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Buffers.BufferOf"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBufferOf(Buffers.BufferOf expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Distributed.ThreadIdDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafThreadIdDim(Distributed.ThreadIdDim expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Dimension"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimension(Dimension expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="AsDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAsDim(AsDim expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="UnknownDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafUnknownDim(UnknownDim expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimVar"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimVar(DimVar expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimConst"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimConst(DimConst expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimPower"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimPower(DimPower expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimFraction"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimFraction(DimFraction expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimRemainder"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimRemainder(DimRemainder expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimProduct"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimProduct(DimProduct expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimSum"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimSum(DimSum expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimAbs"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimAbs(DimAbs expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimClamp"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimClamp(DimClamp expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimCompareAndSelect"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimCompareAndSelect(DimCompareAndSelect expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimMin"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimMin(DimMin expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimMax"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimMax(DimMax expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimPositive"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimPositive(DimPositive expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="DimAt"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimAt(DimAt expr, TContext context) => RewriteLeafDimension(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Shapes.Padding"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPadding(IR.Shapes.Padding expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Shapes.Paddings"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPaddings(IR.Shapes.Paddings expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="Shape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafShape(Shape expr, TContext context) => DefaultRewriteLeaf(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="RankedShape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafRankedShape(RankedShape expr, TContext context) => RewriteLeafShape(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="UnrankedShape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafUnrankedShape(UnrankedShape expr, TContext context) => RewriteLeafShape(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="InvalidShape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafInvalidShape(InvalidShape expr, TContext context) => RewriteLeafShape(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="ShapeVar"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafShapeVar(ShapeVar expr, TContext context) => RewriteLeafShape(expr, context);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Shapes.ShapeOf"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafShapeOf(IR.Shapes.ShapeOf expr, TContext context) => RewriteLeafShape(expr, context);

}

public partial class ExprRewriter
{
    /// <summary>
    /// Rewrite leaf <see cref="BaseFunction"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBaseFunction(BaseFunction expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafBaseFunction(BaseFunction expr, Unit context) => RewriteLeafBaseFunction(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Call"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafCall(Call expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafCall(Call expr, Unit context) => RewriteLeafCall(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Const"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafConst(Const expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafConst(Const expr, Unit context) => RewriteLeafConst(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Function"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFunction(Function expr) => RewriteLeafBaseFunction(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafFunction(Function expr, Unit context) => RewriteLeafFunction(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Fusion"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFusion(Fusion expr) => RewriteLeafBaseFunction(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafFusion(Fusion expr, Unit context) => RewriteLeafFusion(expr);

    /// <summary>
    /// Rewrite leaf <see cref="If"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafIf(If expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafIf(If expr, Unit context) => RewriteLeafIf(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Marker"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafMarker(Marker expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafMarker(Marker expr, Unit context) => RewriteLeafMarker(expr);

    /// <summary>
    /// Rewrite leaf <see cref="None"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafNone(None expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafNone(None expr, Unit context) => RewriteLeafNone(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Op"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafOp(Op expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafOp(Op expr, Unit context) => RewriteLeafOp(expr);

    /// <summary>
    /// Rewrite leaf <see cref="PrimFunctionWrapper"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPrimFunctionWrapper(PrimFunctionWrapper expr) => RewriteLeafBaseFunction(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafPrimFunctionWrapper(PrimFunctionWrapper expr, Unit context) => RewriteLeafPrimFunctionWrapper(expr);

    /// <summary>
    /// Rewrite leaf <see cref="FunctionWrapper"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFunctionWrapper(FunctionWrapper expr) => RewriteLeafBaseFunction(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafFunctionWrapper(FunctionWrapper expr, Unit context) => RewriteLeafFunctionWrapper(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TensorConst"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafTensorConst(TensorConst expr) => RewriteLeafConst(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafTensorConst(TensorConst expr, Unit context) => RewriteLeafTensorConst(expr);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Tuple"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafTuple(IR.Tuple expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafTuple(IR.Tuple expr, Unit context) => RewriteLeafTuple(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TupleConst"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafTupleConst(TupleConst expr) => RewriteLeafConst(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafTupleConst(TupleConst expr, Unit context) => RewriteLeafTupleConst(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.PhysicalBuffer"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPhysicalBuffer(TIR.PhysicalBuffer expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafPhysicalBuffer(TIR.PhysicalBuffer expr, Unit context) => RewriteLeafPhysicalBuffer(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.MemSpan"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafMemSpan(TIR.MemSpan expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafMemSpan(TIR.MemSpan expr, Unit context) => RewriteLeafMemSpan(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Var"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafVar(Var expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafVar(Var expr, Unit context) => RewriteLeafVar(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Block"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBlock(TIR.Block expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafBlock(TIR.Block expr, Unit context) => RewriteLeafBlock(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Buffer"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBuffer(TIR.Buffer expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafBuffer(TIR.Buffer expr, Unit context) => RewriteLeafBuffer(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.BufferRegion"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBufferRegion(TIR.BufferRegion expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafBufferRegion(TIR.BufferRegion expr, Unit context) => RewriteLeafBufferRegion(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.For"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFor(TIR.For expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafFor(TIR.For expr, Unit context) => RewriteLeafFor(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.IfThenElse"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafIfThenElse(TIR.IfThenElse expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafIfThenElse(TIR.IfThenElse expr, Unit context) => RewriteLeafIfThenElse(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Let"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafLet(TIR.Let expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafLet(TIR.Let expr, Unit context) => RewriteLeafLet(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.PrimFunction"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPrimFunction(TIR.PrimFunction expr) => RewriteLeafBaseFunction(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafPrimFunction(TIR.PrimFunction expr, Unit context) => RewriteLeafPrimFunction(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Sequential"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafSequential(TIR.Sequential expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafSequential(TIR.Sequential expr, Unit context) => RewriteLeafSequential(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Range"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafRange(TIR.Range expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafRange(TIR.Range expr, Unit context) => RewriteLeafRange(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.IterVar"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafIterVar(TIR.IterVar expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafIterVar(TIR.IterVar expr, Unit context) => RewriteLeafIterVar(expr);

    /// <summary>
    /// Rewrite leaf <see cref="TIR.Return"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafReturn(TIR.Return expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafReturn(TIR.Return expr, Unit context) => RewriteLeafReturn(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineExpr"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineExpr(Affine.AffineExpr expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineExpr(Affine.AffineExpr expr, Unit context) => RewriteLeafAffineExpr(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineDim(Affine.AffineDim expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineDim(Affine.AffineDim expr, Unit context) => RewriteLeafAffineDim(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineExtent"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineExtent(Affine.AffineExtent expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineExtent(Affine.AffineExtent expr, Unit context) => RewriteLeafAffineExtent(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineSymbol"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineSymbol(Affine.AffineSymbol expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineSymbol(Affine.AffineSymbol expr, Unit context) => RewriteLeafAffineSymbol(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineConstant"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineConstant(Affine.AffineConstant expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineConstant(Affine.AffineConstant expr, Unit context) => RewriteLeafAffineConstant(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineAddBinary"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineAddBinary(Affine.AffineAddBinary expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineAddBinary(Affine.AffineAddBinary expr, Unit context) => RewriteLeafAffineAddBinary(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineMulBinary"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineMulBinary(Affine.AffineMulBinary expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineMulBinary(Affine.AffineMulBinary expr, Unit context) => RewriteLeafAffineMulBinary(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineDivBinary"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineDivBinary(Affine.AffineDivBinary expr) => RewriteLeafAffineExpr(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineDivBinary(Affine.AffineDivBinary expr, Unit context) => RewriteLeafAffineDivBinary(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineDomain"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineDomain(Affine.AffineDomain expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineDomain(Affine.AffineDomain expr, Unit context) => RewriteLeafAffineDomain(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineRange"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineRange(Affine.AffineRange expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineRange(Affine.AffineRange expr, Unit context) => RewriteLeafAffineRange(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineMap"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineMap(Affine.AffineMap expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineMap(Affine.AffineMap expr, Unit context) => RewriteLeafAffineMap(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.AffineRelation"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAffineRelation(Affine.AffineRelation expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAffineRelation(Affine.AffineRelation expr, Unit context) => RewriteLeafAffineRelation(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.Grid"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafGrid(Affine.Grid expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafGrid(Affine.Grid expr, Unit context) => RewriteLeafGrid(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.Load"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafLoad(Affine.Load expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafLoad(Affine.Load expr, Unit context) => RewriteLeafLoad(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Affine.For"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafFor(Affine.For expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafFor(Affine.For expr, Unit context) => RewriteLeafFor(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Buffers.BufferOf"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafBufferOf(Buffers.BufferOf expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafBufferOf(Buffers.BufferOf expr, Unit context) => RewriteLeafBufferOf(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Distributed.ThreadIdDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafThreadIdDim(Distributed.ThreadIdDim expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafThreadIdDim(Distributed.ThreadIdDim expr, Unit context) => RewriteLeafThreadIdDim(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Dimension"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimension(Dimension expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimension(Dimension expr, Unit context) => RewriteLeafDimension(expr);

    /// <summary>
    /// Rewrite leaf <see cref="AsDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafAsDim(AsDim expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafAsDim(AsDim expr, Unit context) => RewriteLeafAsDim(expr);

    /// <summary>
    /// Rewrite leaf <see cref="UnknownDim"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafUnknownDim(UnknownDim expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafUnknownDim(UnknownDim expr, Unit context) => RewriteLeafUnknownDim(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimVar"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimVar(DimVar expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimVar(DimVar expr, Unit context) => RewriteLeafDimVar(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimConst"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimConst(DimConst expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimConst(DimConst expr, Unit context) => RewriteLeafDimConst(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimPower"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimPower(DimPower expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimPower(DimPower expr, Unit context) => RewriteLeafDimPower(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimFraction"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimFraction(DimFraction expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimFraction(DimFraction expr, Unit context) => RewriteLeafDimFraction(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimRemainder"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimRemainder(DimRemainder expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimRemainder(DimRemainder expr, Unit context) => RewriteLeafDimRemainder(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimProduct"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimProduct(DimProduct expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimProduct(DimProduct expr, Unit context) => RewriteLeafDimProduct(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimSum"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimSum(DimSum expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimSum(DimSum expr, Unit context) => RewriteLeafDimSum(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimAbs"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimAbs(DimAbs expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimAbs(DimAbs expr, Unit context) => RewriteLeafDimAbs(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimClamp"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimClamp(DimClamp expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimClamp(DimClamp expr, Unit context) => RewriteLeafDimClamp(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimCompareAndSelect"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimCompareAndSelect(DimCompareAndSelect expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimCompareAndSelect(DimCompareAndSelect expr, Unit context) => RewriteLeafDimCompareAndSelect(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimMin"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimMin(DimMin expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimMin(DimMin expr, Unit context) => RewriteLeafDimMin(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimMax"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimMax(DimMax expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimMax(DimMax expr, Unit context) => RewriteLeafDimMax(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimPositive"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimPositive(DimPositive expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimPositive(DimPositive expr, Unit context) => RewriteLeafDimPositive(expr);

    /// <summary>
    /// Rewrite leaf <see cref="DimAt"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafDimAt(DimAt expr) => RewriteLeafDimension(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafDimAt(DimAt expr, Unit context) => RewriteLeafDimAt(expr);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Shapes.Padding"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPadding(IR.Shapes.Padding expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafPadding(IR.Shapes.Padding expr, Unit context) => RewriteLeafPadding(expr);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Shapes.Paddings"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafPaddings(IR.Shapes.Paddings expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafPaddings(IR.Shapes.Paddings expr, Unit context) => RewriteLeafPaddings(expr);

    /// <summary>
    /// Rewrite leaf <see cref="Shape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafShape(Shape expr) => DefaultRewriteLeaf(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafShape(Shape expr, Unit context) => RewriteLeafShape(expr);

    /// <summary>
    /// Rewrite leaf <see cref="RankedShape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafRankedShape(RankedShape expr) => RewriteLeafShape(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafRankedShape(RankedShape expr, Unit context) => RewriteLeafRankedShape(expr);

    /// <summary>
    /// Rewrite leaf <see cref="UnrankedShape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafUnrankedShape(UnrankedShape expr) => RewriteLeafShape(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafUnrankedShape(UnrankedShape expr, Unit context) => RewriteLeafUnrankedShape(expr);

    /// <summary>
    /// Rewrite leaf <see cref="InvalidShape"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafInvalidShape(InvalidShape expr) => RewriteLeafShape(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafInvalidShape(InvalidShape expr, Unit context) => RewriteLeafInvalidShape(expr);

    /// <summary>
    /// Rewrite leaf <see cref="ShapeVar"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafShapeVar(ShapeVar expr) => RewriteLeafShape(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafShapeVar(ShapeVar expr, Unit context) => RewriteLeafShapeVar(expr);

    /// <summary>
    /// Rewrite leaf <see cref="IR.Shapes.ShapeOf"/>.
    /// </summary>
    protected virtual BaseExpr RewriteLeafShapeOf(IR.Shapes.ShapeOf expr) => RewriteLeafShape(expr);

    /// <inheritdoc />
    protected sealed override BaseExpr RewriteLeafShapeOf(IR.Shapes.ShapeOf expr, Unit context) => RewriteLeafShapeOf(expr);

}
