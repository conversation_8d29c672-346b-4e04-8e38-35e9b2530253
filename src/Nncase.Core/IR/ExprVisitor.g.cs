//---------------------------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated by T4 template.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
//---------------------------------------------------------------------------------------------------

using System;
using System.Collections.Generic;
using System.Reactive;

namespace Nncase.IR;

public partial class ExprVisitor<TExprResult, TTypeResult, TContext>
{
    /// <inheritdoc />
    protected internal override TExprResult VisitCall(Call expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafCall(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitFunction(Function expr, TContext context)
    {
        if (CanVisitFunctionBody(expr))
        {
            VisitOperands(expr, context);
        }

        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafFunction(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitFusion(Fusion expr, TContext context)
    {
        if (CanVisitFunctionBody(expr))
        {
            VisitOperands(expr, context);
        }

        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafFusion(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitIf(If expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafIf(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitMarker(Marker expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafMarker(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitNone(None expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafNone(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitOp(Op expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafOp(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitPrimFunctionWrapper(PrimFunctionWrapper expr, TContext context)
    {
        if (CanVisitFunctionBody(expr))
        {
            VisitOperands(expr, context);
        }

        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafPrimFunctionWrapper(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitFunctionWrapper(FunctionWrapper expr, TContext context)
    {
        if (CanVisitFunctionBody(expr))
        {
            VisitOperands(expr, context);
        }

        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafFunctionWrapper(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitTensorConst(TensorConst expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafTensorConst(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitTuple(IR.Tuple expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafTuple(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitTupleConst(TupleConst expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafTupleConst(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitPhysicalBuffer(TIR.PhysicalBuffer expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafPhysicalBuffer(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitMemSpan(TIR.MemSpan expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafMemSpan(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitVar(Var expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafVar(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitBlock(TIR.Block expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafBlock(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitBuffer(TIR.Buffer expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafBuffer(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitBufferRegion(TIR.BufferRegion expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafBufferRegion(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitFor(TIR.For expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafFor(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitIfThenElse(TIR.IfThenElse expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafIfThenElse(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitLet(TIR.Let expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafLet(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitPrimFunction(TIR.PrimFunction expr, TContext context)
    {
        if (CanVisitFunctionBody(expr))
        {
            VisitOperands(expr, context);
        }

        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafPrimFunction(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitSequential(TIR.Sequential expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafSequential(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitRange(TIR.Range expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafRange(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitIterVar(TIR.IterVar expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafIterVar(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitReturn(TIR.Return expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafReturn(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineDim(Affine.AffineDim expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineDim(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineExtent(Affine.AffineExtent expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineExtent(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineSymbol(Affine.AffineSymbol expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineSymbol(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineConstant(Affine.AffineConstant expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineConstant(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineAddBinary(Affine.AffineAddBinary expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineAddBinary(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineMulBinary(Affine.AffineMulBinary expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineMulBinary(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineDivBinary(Affine.AffineDivBinary expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineDivBinary(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineDomain(Affine.AffineDomain expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineDomain(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineRange(Affine.AffineRange expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineRange(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineMap(Affine.AffineMap expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineMap(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAffineRelation(Affine.AffineRelation expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAffineRelation(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitGrid(Affine.Grid expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafGrid(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitLoad(Affine.Load expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafLoad(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitFor(Affine.For expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafFor(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitBufferOf(Buffers.BufferOf expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafBufferOf(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitThreadIdDim(Distributed.ThreadIdDim expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafThreadIdDim(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitAsDim(AsDim expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafAsDim(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitUnknownDim(UnknownDim expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafUnknownDim(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimVar(DimVar expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimVar(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimConst(DimConst expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimConst(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimPower(DimPower expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimPower(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimFraction(DimFraction expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimFraction(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimRemainder(DimRemainder expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimRemainder(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimProduct(DimProduct expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimProduct(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimSum(DimSum expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimSum(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimAbs(DimAbs expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimAbs(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimClamp(DimClamp expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimClamp(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimCompareAndSelect(DimCompareAndSelect expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimCompareAndSelect(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimMin(DimMin expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimMin(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimMax(DimMax expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimMax(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimPositive(DimPositive expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimPositive(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitDimAt(DimAt expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafDimAt(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitPadding(IR.Shapes.Padding expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafPadding(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitPaddings(IR.Shapes.Paddings expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafPaddings(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitRankedShape(RankedShape expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafRankedShape(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitUnrankedShape(UnrankedShape expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafUnrankedShape(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitInvalidShape(InvalidShape expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafInvalidShape(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitShapeVar(ShapeVar expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafShapeVar(expr, context);
    }

    /// <inheritdoc />
    protected internal override TExprResult VisitShapeOf(IR.Shapes.ShapeOf expr, TContext context)
    {
        VisitOperands(expr, context);
        if (CanVisitAttributes(expr))
        {
            VisitAttributes(expr, context);
        }

        return VisitLeafShapeOf(expr, context);
    }

    /// <summary>
    /// Visit leaf <see cref="BaseFunction"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBaseFunction(BaseFunction expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Call"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafCall(Call expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Const"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafConst(Const expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Function"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFunction(Function expr, TContext context) => VisitLeafBaseFunction(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Fusion"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFusion(Fusion expr, TContext context) => VisitLeafBaseFunction(expr, context);

    /// <summary>
    /// Visit leaf <see cref="If"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafIf(If expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Marker"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafMarker(Marker expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="None"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafNone(None expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Op"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafOp(Op expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="PrimFunctionWrapper"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPrimFunctionWrapper(PrimFunctionWrapper expr, TContext context) => VisitLeafBaseFunction(expr, context);

    /// <summary>
    /// Visit leaf <see cref="FunctionWrapper"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFunctionWrapper(FunctionWrapper expr, TContext context) => VisitLeafBaseFunction(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TensorConst"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafTensorConst(TensorConst expr, TContext context) => VisitLeafConst(expr, context);

    /// <summary>
    /// Visit leaf <see cref="IR.Tuple"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafTuple(IR.Tuple expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TupleConst"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafTupleConst(TupleConst expr, TContext context) => VisitLeafConst(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.PhysicalBuffer"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPhysicalBuffer(TIR.PhysicalBuffer expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.MemSpan"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafMemSpan(TIR.MemSpan expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Var"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafVar(Var expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.Block"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBlock(TIR.Block expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.Buffer"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBuffer(TIR.Buffer expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.BufferRegion"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBufferRegion(TIR.BufferRegion expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.For"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFor(TIR.For expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.IfThenElse"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafIfThenElse(TIR.IfThenElse expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.Let"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafLet(TIR.Let expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.PrimFunction"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPrimFunction(TIR.PrimFunction expr, TContext context) => VisitLeafBaseFunction(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.Sequential"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafSequential(TIR.Sequential expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.Range"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafRange(TIR.Range expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.IterVar"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafIterVar(TIR.IterVar expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="TIR.Return"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafReturn(TIR.Return expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineExpr"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineExpr(Affine.AffineExpr expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineDim(Affine.AffineDim expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineExtent"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineExtent(Affine.AffineExtent expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineSymbol"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineSymbol(Affine.AffineSymbol expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineConstant"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineConstant(Affine.AffineConstant expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineAddBinary"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineAddBinary(Affine.AffineAddBinary expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineMulBinary"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineMulBinary(Affine.AffineMulBinary expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineDivBinary"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineDivBinary(Affine.AffineDivBinary expr, TContext context) => VisitLeafAffineExpr(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineDomain"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineDomain(Affine.AffineDomain expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineRange"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineRange(Affine.AffineRange expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineMap"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineMap(Affine.AffineMap expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineRelation"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineRelation(Affine.AffineRelation expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.Grid"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafGrid(Affine.Grid expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.Load"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafLoad(Affine.Load expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Affine.For"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFor(Affine.For expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Buffers.BufferOf"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBufferOf(Buffers.BufferOf expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Distributed.ThreadIdDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafThreadIdDim(Distributed.ThreadIdDim expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Dimension"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimension(Dimension expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="AsDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAsDim(AsDim expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="UnknownDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafUnknownDim(UnknownDim expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimVar"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimVar(DimVar expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimConst"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimConst(DimConst expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimPower"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimPower(DimPower expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimFraction"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimFraction(DimFraction expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimRemainder"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimRemainder(DimRemainder expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimProduct"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimProduct(DimProduct expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimSum"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimSum(DimSum expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimAbs"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimAbs(DimAbs expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimClamp"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimClamp(DimClamp expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimCompareAndSelect"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimCompareAndSelect(DimCompareAndSelect expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimMin"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimMin(DimMin expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimMax"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimMax(DimMax expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimPositive"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimPositive(DimPositive expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="DimAt"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimAt(DimAt expr, TContext context) => VisitLeafDimension(expr, context);

    /// <summary>
    /// Visit leaf <see cref="IR.Shapes.Padding"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPadding(IR.Shapes.Padding expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="IR.Shapes.Paddings"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPaddings(IR.Shapes.Paddings expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="Shape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafShape(Shape expr, TContext context) => DefaultVisitLeaf(expr, context);

    /// <summary>
    /// Visit leaf <see cref="RankedShape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafRankedShape(RankedShape expr, TContext context) => VisitLeafShape(expr, context);

    /// <summary>
    /// Visit leaf <see cref="UnrankedShape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafUnrankedShape(UnrankedShape expr, TContext context) => VisitLeafShape(expr, context);

    /// <summary>
    /// Visit leaf <see cref="InvalidShape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafInvalidShape(InvalidShape expr, TContext context) => VisitLeafShape(expr, context);

    /// <summary>
    /// Visit leaf <see cref="ShapeVar"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafShapeVar(ShapeVar expr, TContext context) => VisitLeafShape(expr, context);

    /// <summary>
    /// Visit leaf <see cref="IR.Shapes.ShapeOf"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafShapeOf(IR.Shapes.ShapeOf expr, TContext context) => VisitLeafShape(expr, context);

}

public partial class ExprVisitor<TExprResult, TTypeResult>
{
    /// <summary>
    /// Visit <see cref="Call"/>.
    /// </summary>
    internal protected virtual TExprResult VisitCall(Call expr) => base.VisitCall(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitCall(Call expr, Unit context) => VisitCall(expr);
    /// <summary>
    /// Visit <see cref="Function"/>.
    /// </summary>
    internal protected virtual TExprResult VisitFunction(Function expr) => base.VisitFunction(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitFunction(Function expr, Unit context) => VisitFunction(expr);
    /// <summary>
    /// Visit <see cref="Fusion"/>.
    /// </summary>
    internal protected virtual TExprResult VisitFusion(Fusion expr) => base.VisitFusion(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitFusion(Fusion expr, Unit context) => VisitFusion(expr);
    /// <summary>
    /// Visit <see cref="If"/>.
    /// </summary>
    internal protected virtual TExprResult VisitIf(If expr) => base.VisitIf(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitIf(If expr, Unit context) => VisitIf(expr);
    /// <summary>
    /// Visit <see cref="Marker"/>.
    /// </summary>
    internal protected virtual TExprResult VisitMarker(Marker expr) => base.VisitMarker(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitMarker(Marker expr, Unit context) => VisitMarker(expr);
    /// <summary>
    /// Visit <see cref="None"/>.
    /// </summary>
    internal protected virtual TExprResult VisitNone(None expr) => base.VisitNone(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitNone(None expr, Unit context) => VisitNone(expr);
    /// <summary>
    /// Visit <see cref="Op"/>.
    /// </summary>
    internal protected virtual TExprResult VisitOp(Op expr) => base.VisitOp(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitOp(Op expr, Unit context) => VisitOp(expr);
    /// <summary>
    /// Visit <see cref="PrimFunctionWrapper"/>.
    /// </summary>
    internal protected virtual TExprResult VisitPrimFunctionWrapper(PrimFunctionWrapper expr) => base.VisitPrimFunctionWrapper(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitPrimFunctionWrapper(PrimFunctionWrapper expr, Unit context) => VisitPrimFunctionWrapper(expr);
    /// <summary>
    /// Visit <see cref="FunctionWrapper"/>.
    /// </summary>
    internal protected virtual TExprResult VisitFunctionWrapper(FunctionWrapper expr) => base.VisitFunctionWrapper(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitFunctionWrapper(FunctionWrapper expr, Unit context) => VisitFunctionWrapper(expr);
    /// <summary>
    /// Visit <see cref="TensorConst"/>.
    /// </summary>
    internal protected virtual TExprResult VisitTensorConst(TensorConst expr) => base.VisitTensorConst(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitTensorConst(TensorConst expr, Unit context) => VisitTensorConst(expr);
    /// <summary>
    /// Visit <see cref="IR.Tuple"/>.
    /// </summary>
    internal protected virtual TExprResult VisitTuple(IR.Tuple expr) => base.VisitTuple(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitTuple(IR.Tuple expr, Unit context) => VisitTuple(expr);
    /// <summary>
    /// Visit <see cref="TupleConst"/>.
    /// </summary>
    internal protected virtual TExprResult VisitTupleConst(TupleConst expr) => base.VisitTupleConst(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitTupleConst(TupleConst expr, Unit context) => VisitTupleConst(expr);
    /// <summary>
    /// Visit <see cref="TIR.PhysicalBuffer"/>.
    /// </summary>
    internal protected virtual TExprResult VisitPhysicalBuffer(TIR.PhysicalBuffer expr) => base.VisitPhysicalBuffer(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitPhysicalBuffer(TIR.PhysicalBuffer expr, Unit context) => VisitPhysicalBuffer(expr);
    /// <summary>
    /// Visit <see cref="TIR.MemSpan"/>.
    /// </summary>
    internal protected virtual TExprResult VisitMemSpan(TIR.MemSpan expr) => base.VisitMemSpan(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitMemSpan(TIR.MemSpan expr, Unit context) => VisitMemSpan(expr);
    /// <summary>
    /// Visit <see cref="Var"/>.
    /// </summary>
    internal protected virtual TExprResult VisitVar(Var expr) => base.VisitVar(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitVar(Var expr, Unit context) => VisitVar(expr);
    /// <summary>
    /// Visit <see cref="TIR.Block"/>.
    /// </summary>
    internal protected virtual TExprResult VisitBlock(TIR.Block expr) => base.VisitBlock(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitBlock(TIR.Block expr, Unit context) => VisitBlock(expr);
    /// <summary>
    /// Visit <see cref="TIR.Buffer"/>.
    /// </summary>
    internal protected virtual TExprResult VisitBuffer(TIR.Buffer expr) => base.VisitBuffer(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitBuffer(TIR.Buffer expr, Unit context) => VisitBuffer(expr);
    /// <summary>
    /// Visit <see cref="TIR.BufferRegion"/>.
    /// </summary>
    internal protected virtual TExprResult VisitBufferRegion(TIR.BufferRegion expr) => base.VisitBufferRegion(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitBufferRegion(TIR.BufferRegion expr, Unit context) => VisitBufferRegion(expr);
    /// <summary>
    /// Visit <see cref="TIR.For"/>.
    /// </summary>
    internal protected virtual TExprResult VisitFor(TIR.For expr) => base.VisitFor(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitFor(TIR.For expr, Unit context) => VisitFor(expr);
    /// <summary>
    /// Visit <see cref="TIR.IfThenElse"/>.
    /// </summary>
    internal protected virtual TExprResult VisitIfThenElse(TIR.IfThenElse expr) => base.VisitIfThenElse(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitIfThenElse(TIR.IfThenElse expr, Unit context) => VisitIfThenElse(expr);
    /// <summary>
    /// Visit <see cref="TIR.Let"/>.
    /// </summary>
    internal protected virtual TExprResult VisitLet(TIR.Let expr) => base.VisitLet(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitLet(TIR.Let expr, Unit context) => VisitLet(expr);
    /// <summary>
    /// Visit <see cref="TIR.PrimFunction"/>.
    /// </summary>
    internal protected virtual TExprResult VisitPrimFunction(TIR.PrimFunction expr) => base.VisitPrimFunction(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitPrimFunction(TIR.PrimFunction expr, Unit context) => VisitPrimFunction(expr);
    /// <summary>
    /// Visit <see cref="TIR.Sequential"/>.
    /// </summary>
    internal protected virtual TExprResult VisitSequential(TIR.Sequential expr) => base.VisitSequential(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitSequential(TIR.Sequential expr, Unit context) => VisitSequential(expr);
    /// <summary>
    /// Visit <see cref="TIR.Range"/>.
    /// </summary>
    internal protected virtual TExprResult VisitRange(TIR.Range expr) => base.VisitRange(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitRange(TIR.Range expr, Unit context) => VisitRange(expr);
    /// <summary>
    /// Visit <see cref="TIR.IterVar"/>.
    /// </summary>
    internal protected virtual TExprResult VisitIterVar(TIR.IterVar expr) => base.VisitIterVar(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitIterVar(TIR.IterVar expr, Unit context) => VisitIterVar(expr);
    /// <summary>
    /// Visit <see cref="TIR.Return"/>.
    /// </summary>
    internal protected virtual TExprResult VisitReturn(TIR.Return expr) => base.VisitReturn(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitReturn(TIR.Return expr, Unit context) => VisitReturn(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineDim"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineDim(Affine.AffineDim expr) => base.VisitAffineDim(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineDim(Affine.AffineDim expr, Unit context) => VisitAffineDim(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineExtent"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineExtent(Affine.AffineExtent expr) => base.VisitAffineExtent(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineExtent(Affine.AffineExtent expr, Unit context) => VisitAffineExtent(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineSymbol"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineSymbol(Affine.AffineSymbol expr) => base.VisitAffineSymbol(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineSymbol(Affine.AffineSymbol expr, Unit context) => VisitAffineSymbol(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineConstant"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineConstant(Affine.AffineConstant expr) => base.VisitAffineConstant(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineConstant(Affine.AffineConstant expr, Unit context) => VisitAffineConstant(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineAddBinary"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineAddBinary(Affine.AffineAddBinary expr) => base.VisitAffineAddBinary(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineAddBinary(Affine.AffineAddBinary expr, Unit context) => VisitAffineAddBinary(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineMulBinary"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineMulBinary(Affine.AffineMulBinary expr) => base.VisitAffineMulBinary(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineMulBinary(Affine.AffineMulBinary expr, Unit context) => VisitAffineMulBinary(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineDivBinary"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineDivBinary(Affine.AffineDivBinary expr) => base.VisitAffineDivBinary(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineDivBinary(Affine.AffineDivBinary expr, Unit context) => VisitAffineDivBinary(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineDomain"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineDomain(Affine.AffineDomain expr) => base.VisitAffineDomain(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineDomain(Affine.AffineDomain expr, Unit context) => VisitAffineDomain(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineRange"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineRange(Affine.AffineRange expr) => base.VisitAffineRange(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineRange(Affine.AffineRange expr, Unit context) => VisitAffineRange(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineMap"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineMap(Affine.AffineMap expr) => base.VisitAffineMap(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineMap(Affine.AffineMap expr, Unit context) => VisitAffineMap(expr);
    /// <summary>
    /// Visit <see cref="Affine.AffineRelation"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAffineRelation(Affine.AffineRelation expr) => base.VisitAffineRelation(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAffineRelation(Affine.AffineRelation expr, Unit context) => VisitAffineRelation(expr);
    /// <summary>
    /// Visit <see cref="Affine.Grid"/>.
    /// </summary>
    internal protected virtual TExprResult VisitGrid(Affine.Grid expr) => base.VisitGrid(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitGrid(Affine.Grid expr, Unit context) => VisitGrid(expr);
    /// <summary>
    /// Visit <see cref="Affine.Load"/>.
    /// </summary>
    internal protected virtual TExprResult VisitLoad(Affine.Load expr) => base.VisitLoad(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitLoad(Affine.Load expr, Unit context) => VisitLoad(expr);
    /// <summary>
    /// Visit <see cref="Affine.For"/>.
    /// </summary>
    internal protected virtual TExprResult VisitFor(Affine.For expr) => base.VisitFor(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitFor(Affine.For expr, Unit context) => VisitFor(expr);
    /// <summary>
    /// Visit <see cref="Buffers.BufferOf"/>.
    /// </summary>
    internal protected virtual TExprResult VisitBufferOf(Buffers.BufferOf expr) => base.VisitBufferOf(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitBufferOf(Buffers.BufferOf expr, Unit context) => VisitBufferOf(expr);
    /// <summary>
    /// Visit <see cref="Distributed.ThreadIdDim"/>.
    /// </summary>
    internal protected virtual TExprResult VisitThreadIdDim(Distributed.ThreadIdDim expr) => base.VisitThreadIdDim(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitThreadIdDim(Distributed.ThreadIdDim expr, Unit context) => VisitThreadIdDim(expr);
    /// <summary>
    /// Visit <see cref="AsDim"/>.
    /// </summary>
    internal protected virtual TExprResult VisitAsDim(AsDim expr) => base.VisitAsDim(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitAsDim(AsDim expr, Unit context) => VisitAsDim(expr);
    /// <summary>
    /// Visit <see cref="UnknownDim"/>.
    /// </summary>
    internal protected virtual TExprResult VisitUnknownDim(UnknownDim expr) => base.VisitUnknownDim(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitUnknownDim(UnknownDim expr, Unit context) => VisitUnknownDim(expr);
    /// <summary>
    /// Visit <see cref="DimVar"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimVar(DimVar expr) => base.VisitDimVar(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimVar(DimVar expr, Unit context) => VisitDimVar(expr);
    /// <summary>
    /// Visit <see cref="DimConst"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimConst(DimConst expr) => base.VisitDimConst(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimConst(DimConst expr, Unit context) => VisitDimConst(expr);
    /// <summary>
    /// Visit <see cref="DimPower"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimPower(DimPower expr) => base.VisitDimPower(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimPower(DimPower expr, Unit context) => VisitDimPower(expr);
    /// <summary>
    /// Visit <see cref="DimFraction"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimFraction(DimFraction expr) => base.VisitDimFraction(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimFraction(DimFraction expr, Unit context) => VisitDimFraction(expr);
    /// <summary>
    /// Visit <see cref="DimRemainder"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimRemainder(DimRemainder expr) => base.VisitDimRemainder(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimRemainder(DimRemainder expr, Unit context) => VisitDimRemainder(expr);
    /// <summary>
    /// Visit <see cref="DimProduct"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimProduct(DimProduct expr) => base.VisitDimProduct(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimProduct(DimProduct expr, Unit context) => VisitDimProduct(expr);
    /// <summary>
    /// Visit <see cref="DimSum"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimSum(DimSum expr) => base.VisitDimSum(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimSum(DimSum expr, Unit context) => VisitDimSum(expr);
    /// <summary>
    /// Visit <see cref="DimAbs"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimAbs(DimAbs expr) => base.VisitDimAbs(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimAbs(DimAbs expr, Unit context) => VisitDimAbs(expr);
    /// <summary>
    /// Visit <see cref="DimClamp"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimClamp(DimClamp expr) => base.VisitDimClamp(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimClamp(DimClamp expr, Unit context) => VisitDimClamp(expr);
    /// <summary>
    /// Visit <see cref="DimCompareAndSelect"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimCompareAndSelect(DimCompareAndSelect expr) => base.VisitDimCompareAndSelect(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimCompareAndSelect(DimCompareAndSelect expr, Unit context) => VisitDimCompareAndSelect(expr);
    /// <summary>
    /// Visit <see cref="DimMin"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimMin(DimMin expr) => base.VisitDimMin(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimMin(DimMin expr, Unit context) => VisitDimMin(expr);
    /// <summary>
    /// Visit <see cref="DimMax"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimMax(DimMax expr) => base.VisitDimMax(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimMax(DimMax expr, Unit context) => VisitDimMax(expr);
    /// <summary>
    /// Visit <see cref="DimPositive"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimPositive(DimPositive expr) => base.VisitDimPositive(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimPositive(DimPositive expr, Unit context) => VisitDimPositive(expr);
    /// <summary>
    /// Visit <see cref="DimAt"/>.
    /// </summary>
    internal protected virtual TExprResult VisitDimAt(DimAt expr) => base.VisitDimAt(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitDimAt(DimAt expr, Unit context) => VisitDimAt(expr);
    /// <summary>
    /// Visit <see cref="IR.Shapes.Padding"/>.
    /// </summary>
    internal protected virtual TExprResult VisitPadding(IR.Shapes.Padding expr) => base.VisitPadding(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitPadding(IR.Shapes.Padding expr, Unit context) => VisitPadding(expr);
    /// <summary>
    /// Visit <see cref="IR.Shapes.Paddings"/>.
    /// </summary>
    internal protected virtual TExprResult VisitPaddings(IR.Shapes.Paddings expr) => base.VisitPaddings(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitPaddings(IR.Shapes.Paddings expr, Unit context) => VisitPaddings(expr);
    /// <summary>
    /// Visit <see cref="RankedShape"/>.
    /// </summary>
    internal protected virtual TExprResult VisitRankedShape(RankedShape expr) => base.VisitRankedShape(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitRankedShape(RankedShape expr, Unit context) => VisitRankedShape(expr);
    /// <summary>
    /// Visit <see cref="UnrankedShape"/>.
    /// </summary>
    internal protected virtual TExprResult VisitUnrankedShape(UnrankedShape expr) => base.VisitUnrankedShape(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitUnrankedShape(UnrankedShape expr, Unit context) => VisitUnrankedShape(expr);
    /// <summary>
    /// Visit <see cref="InvalidShape"/>.
    /// </summary>
    internal protected virtual TExprResult VisitInvalidShape(InvalidShape expr) => base.VisitInvalidShape(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitInvalidShape(InvalidShape expr, Unit context) => VisitInvalidShape(expr);
    /// <summary>
    /// Visit <see cref="ShapeVar"/>.
    /// </summary>
    internal protected virtual TExprResult VisitShapeVar(ShapeVar expr) => base.VisitShapeVar(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitShapeVar(ShapeVar expr, Unit context) => VisitShapeVar(expr);
    /// <summary>
    /// Visit <see cref="IR.Shapes.ShapeOf"/>.
    /// </summary>
    internal protected virtual TExprResult VisitShapeOf(IR.Shapes.ShapeOf expr) => base.VisitShapeOf(expr, default);
    
    /// <inheritdoc/>
    internal protected sealed override TExprResult VisitShapeOf(IR.Shapes.ShapeOf expr, Unit context) => VisitShapeOf(expr);
    /// <summary>
    /// Visit leaf <see cref="BaseFunction"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBaseFunction(BaseFunction expr) => base.VisitLeafBaseFunction(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafBaseFunction(BaseFunction expr, Unit context) => VisitLeafBaseFunction(expr);

    /// <summary>
    /// Visit leaf <see cref="Call"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafCall(Call expr) => base.VisitLeafCall(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafCall(Call expr, Unit context) => VisitLeafCall(expr);

    /// <summary>
    /// Visit leaf <see cref="Const"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafConst(Const expr) => base.VisitLeafConst(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafConst(Const expr, Unit context) => VisitLeafConst(expr);

    /// <summary>
    /// Visit leaf <see cref="Function"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFunction(Function expr) => base.VisitLeafFunction(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafFunction(Function expr, Unit context) => VisitLeafFunction(expr);

    /// <summary>
    /// Visit leaf <see cref="Fusion"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFusion(Fusion expr) => base.VisitLeafFusion(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafFusion(Fusion expr, Unit context) => VisitLeafFusion(expr);

    /// <summary>
    /// Visit leaf <see cref="If"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafIf(If expr) => base.VisitLeafIf(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafIf(If expr, Unit context) => VisitLeafIf(expr);

    /// <summary>
    /// Visit leaf <see cref="Marker"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafMarker(Marker expr) => base.VisitLeafMarker(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafMarker(Marker expr, Unit context) => VisitLeafMarker(expr);

    /// <summary>
    /// Visit leaf <see cref="None"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafNone(None expr) => base.VisitLeafNone(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafNone(None expr, Unit context) => VisitLeafNone(expr);

    /// <summary>
    /// Visit leaf <see cref="Op"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafOp(Op expr) => base.VisitLeafOp(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafOp(Op expr, Unit context) => VisitLeafOp(expr);

    /// <summary>
    /// Visit leaf <see cref="PrimFunctionWrapper"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPrimFunctionWrapper(PrimFunctionWrapper expr) => base.VisitLeafPrimFunctionWrapper(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafPrimFunctionWrapper(PrimFunctionWrapper expr, Unit context) => VisitLeafPrimFunctionWrapper(expr);

    /// <summary>
    /// Visit leaf <see cref="FunctionWrapper"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFunctionWrapper(FunctionWrapper expr) => base.VisitLeafFunctionWrapper(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafFunctionWrapper(FunctionWrapper expr, Unit context) => VisitLeafFunctionWrapper(expr);

    /// <summary>
    /// Visit leaf <see cref="TensorConst"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafTensorConst(TensorConst expr) => base.VisitLeafTensorConst(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafTensorConst(TensorConst expr, Unit context) => VisitLeafTensorConst(expr);

    /// <summary>
    /// Visit leaf <see cref="IR.Tuple"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafTuple(IR.Tuple expr) => base.VisitLeafTuple(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafTuple(IR.Tuple expr, Unit context) => VisitLeafTuple(expr);

    /// <summary>
    /// Visit leaf <see cref="TupleConst"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafTupleConst(TupleConst expr) => base.VisitLeafTupleConst(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafTupleConst(TupleConst expr, Unit context) => VisitLeafTupleConst(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.PhysicalBuffer"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPhysicalBuffer(TIR.PhysicalBuffer expr) => base.VisitLeafPhysicalBuffer(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafPhysicalBuffer(TIR.PhysicalBuffer expr, Unit context) => VisitLeafPhysicalBuffer(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.MemSpan"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafMemSpan(TIR.MemSpan expr) => base.VisitLeafMemSpan(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafMemSpan(TIR.MemSpan expr, Unit context) => VisitLeafMemSpan(expr);

    /// <summary>
    /// Visit leaf <see cref="Var"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafVar(Var expr) => base.VisitLeafVar(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafVar(Var expr, Unit context) => VisitLeafVar(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.Block"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBlock(TIR.Block expr) => base.VisitLeafBlock(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafBlock(TIR.Block expr, Unit context) => VisitLeafBlock(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.Buffer"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBuffer(TIR.Buffer expr) => base.VisitLeafBuffer(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafBuffer(TIR.Buffer expr, Unit context) => VisitLeafBuffer(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.BufferRegion"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBufferRegion(TIR.BufferRegion expr) => base.VisitLeafBufferRegion(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafBufferRegion(TIR.BufferRegion expr, Unit context) => VisitLeafBufferRegion(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.For"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFor(TIR.For expr) => base.VisitLeafFor(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafFor(TIR.For expr, Unit context) => VisitLeafFor(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.IfThenElse"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafIfThenElse(TIR.IfThenElse expr) => base.VisitLeafIfThenElse(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafIfThenElse(TIR.IfThenElse expr, Unit context) => VisitLeafIfThenElse(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.Let"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafLet(TIR.Let expr) => base.VisitLeafLet(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafLet(TIR.Let expr, Unit context) => VisitLeafLet(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.PrimFunction"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPrimFunction(TIR.PrimFunction expr) => base.VisitLeafPrimFunction(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafPrimFunction(TIR.PrimFunction expr, Unit context) => VisitLeafPrimFunction(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.Sequential"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafSequential(TIR.Sequential expr) => base.VisitLeafSequential(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafSequential(TIR.Sequential expr, Unit context) => VisitLeafSequential(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.Range"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafRange(TIR.Range expr) => base.VisitLeafRange(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafRange(TIR.Range expr, Unit context) => VisitLeafRange(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.IterVar"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafIterVar(TIR.IterVar expr) => base.VisitLeafIterVar(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafIterVar(TIR.IterVar expr, Unit context) => VisitLeafIterVar(expr);

    /// <summary>
    /// Visit leaf <see cref="TIR.Return"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafReturn(TIR.Return expr) => base.VisitLeafReturn(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafReturn(TIR.Return expr, Unit context) => VisitLeafReturn(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineExpr"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineExpr(Affine.AffineExpr expr) => base.VisitLeafAffineExpr(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineExpr(Affine.AffineExpr expr, Unit context) => VisitLeafAffineExpr(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineDim(Affine.AffineDim expr) => base.VisitLeafAffineDim(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineDim(Affine.AffineDim expr, Unit context) => VisitLeafAffineDim(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineExtent"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineExtent(Affine.AffineExtent expr) => base.VisitLeafAffineExtent(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineExtent(Affine.AffineExtent expr, Unit context) => VisitLeafAffineExtent(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineSymbol"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineSymbol(Affine.AffineSymbol expr) => base.VisitLeafAffineSymbol(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineSymbol(Affine.AffineSymbol expr, Unit context) => VisitLeafAffineSymbol(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineConstant"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineConstant(Affine.AffineConstant expr) => base.VisitLeafAffineConstant(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineConstant(Affine.AffineConstant expr, Unit context) => VisitLeafAffineConstant(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineAddBinary"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineAddBinary(Affine.AffineAddBinary expr) => base.VisitLeafAffineAddBinary(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineAddBinary(Affine.AffineAddBinary expr, Unit context) => VisitLeafAffineAddBinary(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineMulBinary"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineMulBinary(Affine.AffineMulBinary expr) => base.VisitLeafAffineMulBinary(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineMulBinary(Affine.AffineMulBinary expr, Unit context) => VisitLeafAffineMulBinary(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineDivBinary"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineDivBinary(Affine.AffineDivBinary expr) => base.VisitLeafAffineDivBinary(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineDivBinary(Affine.AffineDivBinary expr, Unit context) => VisitLeafAffineDivBinary(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineDomain"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineDomain(Affine.AffineDomain expr) => base.VisitLeafAffineDomain(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineDomain(Affine.AffineDomain expr, Unit context) => VisitLeafAffineDomain(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineRange"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineRange(Affine.AffineRange expr) => base.VisitLeafAffineRange(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineRange(Affine.AffineRange expr, Unit context) => VisitLeafAffineRange(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineMap"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineMap(Affine.AffineMap expr) => base.VisitLeafAffineMap(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineMap(Affine.AffineMap expr, Unit context) => VisitLeafAffineMap(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.AffineRelation"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAffineRelation(Affine.AffineRelation expr) => base.VisitLeafAffineRelation(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAffineRelation(Affine.AffineRelation expr, Unit context) => VisitLeafAffineRelation(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.Grid"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafGrid(Affine.Grid expr) => base.VisitLeafGrid(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafGrid(Affine.Grid expr, Unit context) => VisitLeafGrid(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.Load"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafLoad(Affine.Load expr) => base.VisitLeafLoad(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafLoad(Affine.Load expr, Unit context) => VisitLeafLoad(expr);

    /// <summary>
    /// Visit leaf <see cref="Affine.For"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafFor(Affine.For expr) => base.VisitLeafFor(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafFor(Affine.For expr, Unit context) => VisitLeafFor(expr);

    /// <summary>
    /// Visit leaf <see cref="Buffers.BufferOf"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafBufferOf(Buffers.BufferOf expr) => base.VisitLeafBufferOf(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafBufferOf(Buffers.BufferOf expr, Unit context) => VisitLeafBufferOf(expr);

    /// <summary>
    /// Visit leaf <see cref="Distributed.ThreadIdDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafThreadIdDim(Distributed.ThreadIdDim expr) => base.VisitLeafThreadIdDim(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafThreadIdDim(Distributed.ThreadIdDim expr, Unit context) => VisitLeafThreadIdDim(expr);

    /// <summary>
    /// Visit leaf <see cref="Dimension"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimension(Dimension expr) => base.VisitLeafDimension(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimension(Dimension expr, Unit context) => VisitLeafDimension(expr);

    /// <summary>
    /// Visit leaf <see cref="AsDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafAsDim(AsDim expr) => base.VisitLeafAsDim(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafAsDim(AsDim expr, Unit context) => VisitLeafAsDim(expr);

    /// <summary>
    /// Visit leaf <see cref="UnknownDim"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafUnknownDim(UnknownDim expr) => base.VisitLeafUnknownDim(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafUnknownDim(UnknownDim expr, Unit context) => VisitLeafUnknownDim(expr);

    /// <summary>
    /// Visit leaf <see cref="DimVar"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimVar(DimVar expr) => base.VisitLeafDimVar(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimVar(DimVar expr, Unit context) => VisitLeafDimVar(expr);

    /// <summary>
    /// Visit leaf <see cref="DimConst"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimConst(DimConst expr) => base.VisitLeafDimConst(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimConst(DimConst expr, Unit context) => VisitLeafDimConst(expr);

    /// <summary>
    /// Visit leaf <see cref="DimPower"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimPower(DimPower expr) => base.VisitLeafDimPower(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimPower(DimPower expr, Unit context) => VisitLeafDimPower(expr);

    /// <summary>
    /// Visit leaf <see cref="DimFraction"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimFraction(DimFraction expr) => base.VisitLeafDimFraction(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimFraction(DimFraction expr, Unit context) => VisitLeafDimFraction(expr);

    /// <summary>
    /// Visit leaf <see cref="DimRemainder"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimRemainder(DimRemainder expr) => base.VisitLeafDimRemainder(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimRemainder(DimRemainder expr, Unit context) => VisitLeafDimRemainder(expr);

    /// <summary>
    /// Visit leaf <see cref="DimProduct"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimProduct(DimProduct expr) => base.VisitLeafDimProduct(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimProduct(DimProduct expr, Unit context) => VisitLeafDimProduct(expr);

    /// <summary>
    /// Visit leaf <see cref="DimSum"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimSum(DimSum expr) => base.VisitLeafDimSum(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimSum(DimSum expr, Unit context) => VisitLeafDimSum(expr);

    /// <summary>
    /// Visit leaf <see cref="DimAbs"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimAbs(DimAbs expr) => base.VisitLeafDimAbs(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimAbs(DimAbs expr, Unit context) => VisitLeafDimAbs(expr);

    /// <summary>
    /// Visit leaf <see cref="DimClamp"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimClamp(DimClamp expr) => base.VisitLeafDimClamp(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimClamp(DimClamp expr, Unit context) => VisitLeafDimClamp(expr);

    /// <summary>
    /// Visit leaf <see cref="DimCompareAndSelect"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimCompareAndSelect(DimCompareAndSelect expr) => base.VisitLeafDimCompareAndSelect(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimCompareAndSelect(DimCompareAndSelect expr, Unit context) => VisitLeafDimCompareAndSelect(expr);

    /// <summary>
    /// Visit leaf <see cref="DimMin"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimMin(DimMin expr) => base.VisitLeafDimMin(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimMin(DimMin expr, Unit context) => VisitLeafDimMin(expr);

    /// <summary>
    /// Visit leaf <see cref="DimMax"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimMax(DimMax expr) => base.VisitLeafDimMax(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimMax(DimMax expr, Unit context) => VisitLeafDimMax(expr);

    /// <summary>
    /// Visit leaf <see cref="DimPositive"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimPositive(DimPositive expr) => base.VisitLeafDimPositive(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimPositive(DimPositive expr, Unit context) => VisitLeafDimPositive(expr);

    /// <summary>
    /// Visit leaf <see cref="DimAt"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafDimAt(DimAt expr) => base.VisitLeafDimAt(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafDimAt(DimAt expr, Unit context) => VisitLeafDimAt(expr);

    /// <summary>
    /// Visit leaf <see cref="IR.Shapes.Padding"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPadding(IR.Shapes.Padding expr) => base.VisitLeafPadding(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafPadding(IR.Shapes.Padding expr, Unit context) => VisitLeafPadding(expr);

    /// <summary>
    /// Visit leaf <see cref="IR.Shapes.Paddings"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafPaddings(IR.Shapes.Paddings expr) => base.VisitLeafPaddings(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafPaddings(IR.Shapes.Paddings expr, Unit context) => VisitLeafPaddings(expr);

    /// <summary>
    /// Visit leaf <see cref="Shape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafShape(Shape expr) => base.VisitLeafShape(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafShape(Shape expr, Unit context) => VisitLeafShape(expr);

    /// <summary>
    /// Visit leaf <see cref="RankedShape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafRankedShape(RankedShape expr) => base.VisitLeafRankedShape(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafRankedShape(RankedShape expr, Unit context) => VisitLeafRankedShape(expr);

    /// <summary>
    /// Visit leaf <see cref="UnrankedShape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafUnrankedShape(UnrankedShape expr) => base.VisitLeafUnrankedShape(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafUnrankedShape(UnrankedShape expr, Unit context) => VisitLeafUnrankedShape(expr);

    /// <summary>
    /// Visit leaf <see cref="InvalidShape"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafInvalidShape(InvalidShape expr) => base.VisitLeafInvalidShape(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafInvalidShape(InvalidShape expr, Unit context) => VisitLeafInvalidShape(expr);

    /// <summary>
    /// Visit leaf <see cref="ShapeVar"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafShapeVar(ShapeVar expr) => base.VisitLeafShapeVar(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafShapeVar(ShapeVar expr, Unit context) => VisitLeafShapeVar(expr);

    /// <summary>
    /// Visit leaf <see cref="IR.Shapes.ShapeOf"/>.
    /// </summary>
    protected virtual TExprResult VisitLeafShapeOf(IR.Shapes.ShapeOf expr) => base.VisitLeafShapeOf(expr, default);
    
    /// <inheritdoc/>
    protected sealed override TExprResult VisitLeafShapeOf(IR.Shapes.ShapeOf expr, Unit context) => VisitLeafShapeOf(expr);

}
