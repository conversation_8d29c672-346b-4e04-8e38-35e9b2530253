/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "elfload.h"
#include <cstdint>
#include <nncase/compiler_defs.h>
#include <span>
#include <string_view>

BEGIN_NS_NNCASE_RUNTIME

class elf_loader {
  public:
    elf_loader() noexcept;
    ~elf_loader();

    void load(std::span<const std::byte> pe);
    void load_from_file(std::string_view path);
    uintptr_t handle() const noexcept { return (uintptr_t)handle_; }
    void *entry() const noexcept;

  private:
    std::byte *buffer_;
    std::byte *image_;
    void *handle_;
    el_ctx ctx_;
    void *entry_;
};

END_NS_NNCASE_RUNTIME
