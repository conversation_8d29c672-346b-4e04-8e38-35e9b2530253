{"version": 2, "dependencies": {"net8.0": {"Avalonia": {"type": "Direct", "requested": "[11.0.2, )", "resolved": "11.0.2", "contentHash": "ZYJd++jRQr8Xjod38o+iI+kyWE2sPB6CMg7zn7ncDMP+LabRt3w9MPTwkTCTdt060FjkXozySgRBNB8TPh+aOg==", "dependencies": {"Avalonia.BuildServices": "0.0.28", "Avalonia.Remote.Protocol": "11.0.2", "MicroCom.Runtime": "0.11.0", "System.ComponentModel.Annotations": "4.5.0"}}, "Avalonia.Desktop": {"type": "Direct", "requested": "[11.0.2, )", "resolved": "11.0.2", "contentHash": "/jbliSXjM6vw8Pnru7sHkP/89p5fscj998gu4nM0qJ7ofRBfY27duqjW6ZssEQScYNn2+cPmsJVVptfReG87og==", "dependencies": {"Avalonia": "11.0.2", "Avalonia.Native": "11.0.2", "Avalonia.Skia": "11.0.2", "Avalonia.Win32": "11.0.2", "Avalonia.X11": "11.0.2"}}, "Avalonia.Fonts.Inter": {"type": "Direct", "requested": "[11.0.2, )", "resolved": "11.0.2", "contentHash": "6Z7QiofhwQN63VmXSiEvnntYE3xymF2JMChtJ7nL8LaDMdvpsxnP5v+9s0OJ7a0arcKj1EEKGLGgHkpBhkHxqw==", "dependencies": {"Avalonia": "11.0.2"}}, "Avalonia.ReactiveUI": {"type": "Direct", "requested": "[11.0.2, )", "resolved": "11.0.2", "contentHash": "LScjxo5TrdfwJqrPvcgn7bnrKu7dbJG5EJQaXRp/eg5q+Kw0RFJmp/hJeOor+Pzp09cYWPh2YzpqAfPFIP5ORw==", "dependencies": {"Avalonia": "11.0.2", "ReactiveUI": "18.3.1", "System.Reactive": "5.0.0"}}, "Avalonia.Themes.Fluent": {"type": "Direct", "requested": "[11.0.2, )", "resolved": "11.0.2", "contentHash": "tHB0MP1UQloEuDrX5rhFLj05++VzFEkV99eklfEIGbqRy6Wo7i0NkDcMt3c1YQ5FQq6fjx6wqSH6llU6Wnm8vQ==", "dependencies": {"Avalonia": "11.0.2"}}, "CommunityToolkit.Mvvm": {"type": "Direct", "requested": "[8.2.1, )", "resolved": "8.2.1", "contentHash": "I24ofWVEdplxYjUez9/bljv/qb8r8Ccj6cvYXHexNBegLaD3iDy3QrzAAOYVMmfGWIXxlU1ZtECQNfU07+6hXQ=="}, "MessageBox.Avalonia": {"type": "Direct", "requested": "[3.1.2, )", "resolved": "3.1.2", "contentHash": "MX6qJuKZQytw9Bek8JK7Zr+C7AykNnrQVM7U0mbAvMORFsG/DOrLRQH+lT1pgQnHVYInS6Mx8Rgjy928abGIyg==", "dependencies": {"Avalonia": "11.0.0", "DialogHost.Avalonia": "0.7.5", "Markdown.Avalonia.Tight": "11.0.0-d1"}}, "NumSharp": {"type": "Direct", "requested": "[0.30.0, )", "resolved": "0.30.0", "contentHash": "1f8m2B/m/ZSsICaqLszspCyA9/sTHK7wBKEH5KsxGg/r3QCYTc2HnfYOGMeCytvo8/j0v/umn5umLOLhdExlFA==", "dependencies": {"System.Memory": "4.5.4"}}, "StyleCop.Analyzers": {"type": "Direct", "requested": "[1.2.0-beta.556, )", "resolved": "1.2.0-beta.556", "contentHash": "llRPgmA1fhC0I0QyFLEcjvtM2239QzKr/tcnbsjArLMJxJlu0AA5G7Fft0OI30pHF3MW63Gf4aSSsjc5m82J1Q==", "dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.556"}}, "Avalonia.Angle.Windows.Natives": {"type": "Transitive", "resolved": "2.1.0.2023020321", "contentHash": "Zlkkb8ipxrxNWVPCJgMO19fpcpYPP+bpOQ+jPtCFj8v+TzVvPdnGHuyv9IMvSHhhMfEpps4m4hjaP4FORQYVAA=="}, "Avalonia.BuildServices": {"type": "Transitive", "resolved": "0.0.28", "contentHash": "MSM0H8d8PlsPOj490DrmM4qOWAWJsweUQznZPqJ92Rdy2Rp8LfQ7JUFF0b3zceO3LXpGKKi+GSUk1GNsjjkfFQ=="}, "Avalonia.FreeDesktop": {"type": "Transitive", "resolved": "11.0.2", "contentHash": "cpkAGeSpTa+kNRH9JW3zjUQkG5yA5UJxz0osb6CPmzdGp08wdZVVdaDyqVNp5EXASQmxend9X9Sd8N9XQjHctA==", "dependencies": {"Avalonia": "11.0.2", "Tmds.DBus.Protocol": "0.15.0"}}, "Avalonia.Native": {"type": "Transitive", "resolved": "11.0.2", "contentHash": "Rb/1OnGEBls4U7txB5lLDw58x30tSGdEYf6WV5RliIRqQ4YeD/b8g87f6kwYAmp8Wuyq5CAuIGrn+Amr0KGJCA==", "dependencies": {"Avalonia": "11.0.2"}}, "Avalonia.Remote.Protocol": {"type": "Transitive", "resolved": "11.0.2", "contentHash": "/5o9sTMkrASOa+Fgff5tFDKJ2BSuEhAx7luRj61viH49+Xe3gceW6uE8c3IQAtNFB0oHHDdgnpo+cQfO/Ok6OQ=="}, "Avalonia.Skia": {"type": "Transitive", "resolved": "11.0.2", "contentHash": "yNVUoXaH1PFJLRIs3Zo1+KW46eY4dwDAE67gnQdSvfFCA7JjR6vLqnwT929PYYqlwL8PUpfrXniQHLbUjA7vPA==", "dependencies": {"Avalonia": "11.0.2", "HarfBuzzSharp": "*******", "HarfBuzzSharp.NativeAssets.Linux": "*******", "HarfBuzzSharp.NativeAssets.WebAssembly": "*******", "SkiaSharp": "2.88.3", "SkiaSharp.NativeAssets.Linux": "2.88.3", "SkiaSharp.NativeAssets.WebAssembly": "2.88.3"}}, "Avalonia.Win32": {"type": "Transitive", "resolved": "11.0.2", "contentHash": "GIxumpv0pXkpdsCA1RUQFw4Vfrk9TtPFfGvqywFsxb1T03J8oSm70XK6+D30FzJoSlBMI7EBxE3p4Zx/n6rLgw==", "dependencies": {"Avalonia": "11.0.2", "Avalonia.Angle.Windows.Natives": "2.1.0.2023020321", "System.Drawing.Common": "6.0.0", "System.Numerics.Vectors": "4.5.0"}}, "Avalonia.X11": {"type": "Transitive", "resolved": "11.0.2", "contentHash": "KU/5oNkw5/su5jZaYKaARn186STLM9m9kpxaCFVAFqBT+b9JYo+Q064TtcMnZspENA/fmVk44y62ENJRq1c0CQ==", "dependencies": {"Avalonia": "11.0.2", "Avalonia.FreeDesktop": "11.0.2", "Avalonia.Skia": "11.0.2"}}, "ColorTextBlock.Avalonia": {"type": "Transitive", "resolved": "11.0.0-d1", "contentHash": "sQAZgCwCS7vT5qGlxivrx6WAb1xkP47p4gAVmv5GQapfW8YXuk9a/t6QLmjJqFTJAAdk8M1EAUde0Ox5HPxjHA==", "dependencies": {"Avalonia": "11.0.0-rc1.1"}}, "DialogHost.Avalonia": {"type": "Transitive", "resolved": "0.7.5", "contentHash": "jpXMSP2dy/K5UY6lclNjVgeqkitT+Vh8Aj4cLD64TShnDoBxfKOqHd8TMaFCkU5fa7hfhysdxZLZN2IrSwUZcw==", "dependencies": {"Avalonia": "11.0.0-rc2.1", "System.Reactive": "6.0.0"}}, "DynamicData": {"type": "Transitive", "resolved": "7.9.5", "contentHash": "xFwVha7o3qUtVYxco5p+7Urcztc/m1gmaEUxOG0i7LNe+vfCfyb0ECAsT2FLm3zOPHb0g8s9qVu5LfPKfRNVng==", "dependencies": {"System.Reactive": "5.0.0"}}, "Google.OrTools.runtime.linux-arm64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "3/he2Q/VkhLDH82/Sw6ieQUb1zMUbs6ZdhhDI2DE6mXqjeUa3lX8caSIyC+IgyXwjAN3K5MaOOLJ8QX22W4yGQ=="}, "Google.OrTools.runtime.linux-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "rldhqtf/OOBifPEoASjAv6qdOlBGSj7HXTgvyKohsA8q0ySCphZOJpYfWoO6Art0r+3FcrldWRoZs0E4iuNQZg=="}, "Google.OrTools.runtime.osx-arm64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "Kp370X4Cmen8Zno6btH45x+qUIG0vT3OaxEgMiezIjCL4nesSO4XTRhKUQwKP5DYmWzSMdfS/ZJUa5rM8L+iAw=="}, "Google.OrTools.runtime.osx-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "VFZkhe6NjrkOkVjKrQXdBgwWCKK3HrqtgSqTIgNrpM30Gb36cQuszlPZxoc6JsbTHSFJFvImUr2qlg2S4CTrXg=="}, "Google.OrTools.runtime.win-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "/2XtRueL9f14BOnTjvzcIGmFXZfIYw/a5MOgJ/UDwBW9iNBgfnoHyzcTbZ0U7W5Oiex+BJtOdwekkmmvSAX1Hg=="}, "HarfBuzzSharp": {"type": "Transitive", "resolved": "*******", "contentHash": "8MwXm9J4dXHuTdzPo29nHgDbt4+6P+RrPrH/qrxcERf29cpLlFbjvP3eFPwHmdUrl4KL2SHEZi2ZuQ5ndeIL1w==", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}}, "HarfBuzzSharp.NativeAssets.Linux": {"type": "Transitive", "resolved": "*******", "contentHash": "Qu1yJSHEN7PD3+fqfkaClnORWN5e2xJ2Xoziz/GUi/oBT1Z+Dp2oZeiONKP6NFltboSOBkvH90QuOA6YN/U1zg==", "dependencies": {"HarfBuzzSharp": "*******"}}, "HarfBuzzSharp.NativeAssets.macOS": {"type": "Transitive", "resolved": "*******", "contentHash": "uwz9pB3hMuxzI/bSkjVrsOJH7Wo1L+0Md5ZmEMDM/j7xDHtR9d3mfg/CfxhMIcTiUC4JgX49FZK0y2ojgu1dww=="}, "HarfBuzzSharp.NativeAssets.WebAssembly": {"type": "Transitive", "resolved": "*******", "contentHash": "a6t2X1GrZDt3ErjFbG+qXdxaO8EvMMUN1AVZYfayh7EACHU3yU/SG/rveKLWhT8Ln5GFLqe2r+5dsDrHK1qScw=="}, "HarfBuzzSharp.NativeAssets.Win32": {"type": "Transitive", "resolved": "*******", "contentHash": "Wo6QpE4+a+PFVdfIBoLkLr4wq2uC0m9TZC8FAfy4ZnLsUc10WL0Egk9EBHHhDCeokNOXDse5YtvuTYtS/rbHfg=="}, "libisl.linux-x64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "MW+0UxLeAsVyJ6bn0yka2R5HQdCbtCg/zRZswFaWd8Qi9aVNtJmHVBIMJR1kkNk9vjYLTmGIPPUqwJTKXY7Oog=="}, "libisl.osx-arm64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "/LNMumw054jnLUMqsnOG+00ya3OpHcBlXNXtspbpQNvRnfhCMAdMc5hDanjhhU8Plq+GuLwHWS4C7rYdm/yOsA=="}, "libisl.osx-x64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "hRMKX5KF8zePmfIbH+btnPRi57bTgEhhzG6kA9koIV4fLR8XpKVKRzHh9pQotFX6LSjr297e3vm/yyStLJR1EQ=="}, "libortki": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "svfuG5mxGY/QC/5DVheHOCELmdSP90RtxQ73j23KarPXZ9ZXW+7v1l5J77hGDyQbEh1BGrnGgKBlyn76RauGHg==", "dependencies": {"libortki-linux": "0.0.2", "libortki-osx": "0.0.2", "libortki-osx-arm64": "0.0.2", "libortki-win": "0.0.2"}}, "libortki-linux": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "b04LWD4lgGy60tys3hPFhnUpgWDM6dN5r1PI7GOcPj8VupXCaI70LKNQ5/5twbDE6rkowOGanVTw0S2wBGBqBQ=="}, "libortki-osx": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "O6Q9GLULkDkZEPAZJVKLPH0ROXGVOE7BxuddgOcHNK2oiTEM7wIRnzp2OIlYgLpaOLyxJMisbGOhtWgdzt2Wng=="}, "libortki-osx-arm64": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "4Qn2dirJmRicnUG945oWpq7HVGwgqCKKxYPMISv/MRvmpZBbXrZ1cVvRaF8WwTu4XXgfKTa1sLv+i8zLifUMeQ=="}, "libortki-win": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "HAoROgAKn8XBun11X43HZuspKlo5JGy8/OYw5IUPo7FVh5TCaPrLjGmyGYYZ2dqLlv31yv/b6s254PIRGn95cA=="}, "Markdown.Avalonia.Tight": {"type": "Transitive", "resolved": "11.0.0-d1", "contentHash": "fwl4wrvTdLFLUfkFezRY6SSfPmXIJcRyYChMDiMKFEGo1rKdPMnmcJbK+BWYXxZS+g66E6SFl/ca4kakhTk1HQ==", "dependencies": {"Avalonia": "11.0.0-rc1.1", "ColorTextBlock.Avalonia": "11.0.0-d1"}}, "MicroCom.Runtime": {"type": "Transitive", "resolved": "0.11.0", "contentHash": "MEnrZ3UIiH40hjzMDsxrTyi8dtqB5ziv3iBeeU4bXsL/7NLSal9F1lZKpK+tfBRnUoDSdtcW3KufE4yhATOMCA=="}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg=="}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA=="}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ=="}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Logging.Debug": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.Win32.SystemEvents": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "hqTM5628jSsQiv+HGpiq3WKBl2c8v1KZfby2J6Pr7pEPlK9waPdgEO6b8A/+/xn/yZ9ulv8HuqK71ONy2tg67A=="}, "NetFabric.Hyperlinq.Abstractions": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "WXnEcGwmXfa8gW9N2MlcaPNUzM3NLMwnAhacbtH554F8YcoXbIkTB+uGa1Aa+9gyb/9JZgYVHnmADgJUKP52nA=="}, "ReactiveUI": {"type": "Transitive", "resolved": "18.3.1", "contentHash": "0tclGtjrRPfA2gbjiM7O3DeNmo6/TpDn7CMN6jgzDrbgrnysM7oEzjGEeXbtXaOxH6kEf6RiMKWobZoSgbBXhQ==", "dependencies": {"DynamicData": "7.9.5", "Splat": "14.4.1"}}, "SkiaSharp": {"type": "Transitive", "resolved": "2.88.3", "contentHash": "GG8X3EdfwyBfwjl639UIiOVOKEdeoqDgYrz0P1MUCnefXt9cofN+AK8YB/v1+5cLMr03ieWCQdDmPqnFIzSxZw==", "dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.3", "SkiaSharp.NativeAssets.macOS": "2.88.3"}}, "SkiaSharp.NativeAssets.Linux": {"type": "Transitive", "resolved": "2.88.3", "contentHash": "wz29evZVWRqN7WHfenFwQIgqtr8f5vHCutcl1XuhWrHTRZeaIBk7ngjhyHpjUMcQxtIEAdq34ZRvMQshsBYjqg==", "dependencies": {"SkiaSharp": "2.88.3"}}, "SkiaSharp.NativeAssets.macOS": {"type": "Transitive", "resolved": "2.88.3", "contentHash": "CEbWAXMGFkPV3S1snBKK7jEG3+xud/9kmSAhu0BEUKKtlMdxx+Qal0U9bntQREM9QpqP5xLWZooodi8IlV8MEg=="}, "SkiaSharp.NativeAssets.WebAssembly": {"type": "Transitive", "resolved": "2.88.3", "contentHash": "fNKLe6jFqW4rYwaCGgvr+J7heB6S92Z52zp2z7sDSIWXgkkelrhSShDgMd/WKrSYPqlOmfOYnIGW1CQBq9amfg=="}, "SkiaSharp.NativeAssets.Win32": {"type": "Transitive", "resolved": "2.88.3", "contentHash": "MU4ASL8VAbTv5vSw1PoiWjjjpjtGhWtFYuJnrN4sNHFCePb2ohQij9JhSdqLLxk7RpRtWPdV93fbA53Pt+J0yw=="}, "Splat": {"type": "Transitive", "resolved": "14.4.1", "contentHash": "Z1Mncnzm9pNIaIbZ/EWH6x5ESnKsmAvu8HP4StBRw+yhz0lzE7LCbt22TNTPaFrYLYbYCbGQIc/61yuSnpLidg=="}, "StyleCop.Analyzers.Unstable": {"type": "Transitive", "resolved": "1.2.0.556", "contentHash": "zvn9Mqs/ox/83cpYPignI8hJEM2A93s2HkHs8HYMOAQW0PkampyoErAiIyKxgTLqbbad29HX/shv/6LGSjPJNQ=="}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg=="}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.ComponentModel.Annotations": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "UxYQ3FGUOtzJ7LfSdnYSFd7+oEv6M8NgUatatIN2HxNtDdlcvFAf+VIq4Of9cDMJEJC0aSRv/x898RYhB4Yppg=="}, "System.Diagnostics.Contracts": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A=="}, "System.Drawing.Common": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "NfuoKUiP2nUWwKZN6twGqXioIe1zVD0RIj2t976A+czLHr2nY454RwwXs6JU9Htc6mwqL6Dn/nEL3dpVf2jOhg==", "dependencies": {"Microsoft.Win32.SystemEvents": "6.0.0"}}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "6.0.0", "contentHash": "mXX66shZ4xLlI3vNLaJ0lt8OIZdmXTvIqXRdQX5HLVGSkLhINLsVhyZuX2UdRFnOGkqnwmMUs40pIIQ7mna4+A=="}, "System.Linq": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw=="}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.ObjectModel": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ=="}, "System.Reflection.Emit.ILGeneration": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA=="}, "System.Reflection.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "System.Text.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "System.ValueTuple": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ=="}, "Tmds.DBus.Protocol": {"type": "Transitive", "resolved": "0.15.0", "contentHash": "QVo/Y39nTYcCKBqrZuwHjXdwaky0yTQPIT3qUTEEK2MZfDtZWrJ2XyZ59zH8LBgB2fL5cWaTuP2pBTpGz/GeDQ==", "dependencies": {"System.IO.Pipelines": "6.0.0"}}, "nncase.codegen": {"type": "Project", "dependencies": {"Extension.Mathematics": "[1.2.12, )", "Nncase.Core": "[1.0.0, )", "Nncase.IO": "[1.0.0, )"}}, "nncase.compiler": {"type": "Project", "dependencies": {"DryIoc.Microsoft.DependencyInjection": "[6.2.0, )", "DryIoc.dll": "[5.4.3, )", "Microsoft.Extensions.Hosting": "[8.0.0, )", "Nncase.CodeGen": "[1.0.0, )", "Nncase.Core": "[1.0.0, )", "Nncase.Diagnostics": "[1.0.0, )", "Nncase.EGraph": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Graph": "[1.0.0, )", "Nncase.Importer": "[1.0.0, )", "Nncase.Modules.NTT": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "Nncase.Quantization": "[1.0.0, )", "Nncase.Schedule": "[1.0.0, )", "Nncase.Simulator": "[1.0.0, )", "Razor.Templating.Core": "[2.0.0, )"}}, "nncase.core": {"type": "Project", "dependencies": {"CommunityToolkit.HighPerformance": "[8.2.2, )", "DryIoc.dll": "[5.4.3, )", "GiGraph.Dot": "[3.0.1, )", "Google.OrTools": "[9.14.6206, )", "ISLSharp": "[1.0.5, )", "Microsoft.Extensions.Hosting.Abstractions": "[8.0.0, )", "Microsoft.Extensions.Logging.Abstractions": "[8.0.1, )", "Microsoft.Extensions.Options": "[8.0.2, )", "NetFabric.Hyperlinq": "[3.0.0-beta48, )", "System.CommandLine": "[2.0.0-beta4.22272.1, )", "System.Reactive": "[6.0.0, )"}}, "nncase.diagnostics": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )"}}, "nncase.egraph": {"type": "Project", "dependencies": {"GiGraph.Dot": "[3.0.1, )", "Google.OrTools": "[9.14.6206, )", "NetFabric.Hyperlinq": "[3.0.0-beta48, )", "Nncase.Core": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Graph": "[1.0.0, )"}}, "nncase.evaluator": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "OrtKISharp": "[0.0.2, )"}}, "nncase.graph": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "QuikGraph": "[2.5.0, )", "QuikGraph.Graphviz": "[2.5.0, )"}}, "nncase.importer": {"type": "Project", "dependencies": {"Clawfoot.Extensions.Newtonsoft": "[0.1.0, )", "LanguageExt.Core": "[4.4.9, )", "Nncase.Core": "[1.0.0, )", "Onnx.Protobuf": "[1.0.0, )", "TFLite.Schema": "[1.0.0, )"}}, "nncase.io": {"type": "Project"}, "nncase.modules.ntt": {"type": "Project", "dependencies": {"Nncase.CodeGen": "[1.0.0, )", "Nncase.Diagnostics": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "Nncase.Schedule": "[1.0.0, )", "Nncase.Targets": "[1.0.0, )", "Razor.Templating.Core": "[2.0.0, )"}}, "nncase.passes": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "Nncase.EGraph": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Graph": "[1.0.0, )", "QuikGraph.Graphviz": "[2.5.0, )"}}, "nncase.quantization": {"type": "Project", "dependencies": {"Newtonsoft.Json": "[13.0.3, )", "Nncase.Core": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "OrtKISharp": "[0.0.2, )", "System.Linq.Async": "[6.0.1, )"}}, "nncase.schedule": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )"}}, "nncase.simulator": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )"}}, "nncase.targets": {"type": "Project", "dependencies": {"Nncase.CodeGen": "[1.0.0, )", "Nncase.Core": "[1.0.0, )", "Nncase.Schedule": "[1.0.0, )"}}, "onnx.protobuf": {"type": "Project", "dependencies": {"Google.Protobuf": "[3.27.3, )"}}, "tflite.schema": {"type": "Project", "dependencies": {"Nncase.FlatBuffers": "[2.0.0, )"}}, "Clawfoot.Extensions.Newtonsoft": {"type": "CentralTransitive", "requested": "[0.1.0, )", "resolved": "0.1.0", "contentHash": "A8p8THcOiOoexdYUUHgEVeW2BgsFLRqm65+4WuE3Te0XyZdiq+3Alu0D8ktMFUU+0eFeXr6sYcNsDTD3OsVJ4w==", "dependencies": {"Newtonsoft.Json": "12.0.2"}}, "CommunityToolkit.HighPerformance": {"type": "CentralTransitive", "requested": "[8.2.2, )", "resolved": "8.2.2", "contentHash": "+zIp8d3sbtYaRbM6hqDs4Ui/z34j7DcUmleruZlYLE4CVxXq+MO8XJyIs42vzeTYFX+k0Iq1dEbBUnQ4z/Gnrw=="}, "DryIoc.dll": {"type": "CentralTransitive", "requested": "[5.4.3, )", "resolved": "5.4.3", "contentHash": "yhXOG3SOxeWYxBAWskNRDD8fzw5hriEawv4VN4WKaFHOuubiop4kxe2rkWTEyCnTgRVgxVUSQCDBBkZqwPm1iQ=="}, "DryIoc.Microsoft.DependencyInjection": {"type": "CentralTransitive", "requested": "[6.2.0, )", "resolved": "6.2.0", "contentHash": "vR0CUZ/H/1WamB9VDHyBg8nTxqGAU8/hJo4MdDQrwfdsm1E2d9HrJNDJo+V8gAg0GXpC1vgHv7JenpehT0iybw==", "dependencies": {"DryIoc.dll": "5.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Extension.Mathematics": {"type": "CentralTransitive", "requested": "[1.2.12, )", "resolved": "1.2.12", "contentHash": "D4mn5Cab4ztPLJ0V8uMErDrO/Y61098nwrvyIOLZymVAYOQcwP1vomVWKbTagf1aPU3cX5Q7adZtQEQwOy6XEg=="}, "GiGraph.Dot": {"type": "CentralTransitive", "requested": "[3.0.1, )", "resolved": "3.0.1", "contentHash": "TseDWNkBFKrrrzamgl2yeSDk9gmxQZvymcTqRydY/EL9REM9jBwCHFceg7AutXw3ULUL/FKhc1uFzfd2Cjpe/A=="}, "Google.OrTools": {"type": "CentralTransitive", "requested": "[9.14.6206, )", "resolved": "9.14.6206", "contentHash": "HLZCX691VXzR5x6YAHUxHvxHC+KyXJIiqKiS/Fu/g23W8XZgmfSGctnchVkrUT8a9Sosyvp1G0GQlV/i9PdVNQ==", "dependencies": {"Google.OrTools.runtime.linux-arm64": "9.14.6206", "Google.OrTools.runtime.linux-x64": "9.14.6206", "Google.OrTools.runtime.osx-arm64": "9.14.6206", "Google.OrTools.runtime.osx-x64": "9.14.6206", "Google.OrTools.runtime.win-x64": "9.14.6206", "Google.Protobuf": "3.31.1"}}, "Google.Protobuf": {"type": "CentralTransitive", "requested": "[3.27.3, )", "resolved": "3.31.1", "contentHash": "gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A=="}, "ISLSharp": {"type": "CentralTransitive", "requested": "[1.0.5, )", "resolved": "1.0.5", "contentHash": "9v3KxWpuiCJjK93ZpSjy/VVgaW2xBjc6KZ30TePyPFK2oGpeGEI5C7d9lda1X2dE9+XEJIlBKq0P7JvE6eiiFw==", "dependencies": {"libisl.linux-x64": "0.0.4", "libisl.osx-arm64": "0.0.4", "libisl.osx-x64": "0.0.4"}}, "LanguageExt.Core": {"type": "CentralTransitive", "requested": "[4.4.9, )", "resolved": "4.4.9", "contentHash": "K9VGWkThJkaomifa3zcmwysw1BaSqIZZPZc6trBnJN8u9mpmA/cMMwCWEa/v7bPv/+NnG6PbyIDB7HtxBX7yCQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CSharp": "4.7.0", "System.Diagnostics.Contracts": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.Memory": "4.5.5", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}}, "Microsoft.Extensions.Hosting": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "Microsoft.Extensions.Options": {"type": "CentralTransitive", "requested": "[8.0.2, )", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "NetFabric.Hyperlinq": {"type": "CentralTransitive", "requested": "[3.0.0-beta48, )", "resolved": "3.0.0-beta48", "contentHash": "oYUhXvxNS8bBJWqNkvx5g8y0P/0LtyqS2pN0w4OWjVDNWEpLbdbvPy9w/9z1n2PrqIjX3jxUsEnoCmxxGnI3gw==", "dependencies": {"NetFabric.Hyperlinq.Abstractions": "1.3.0", "System.Buffers": "4.5.1", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}}, "Newtonsoft.Json": {"type": "CentralTransitive", "requested": "[13.0.3, )", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Nncase.FlatBuffers": {"type": "CentralTransitive", "requested": "[2.0.0, )", "resolved": "2.0.0", "contentHash": "ir3uek0+7Y8SwOUGUR8y94sgpVDWLAjKGBm9z7cLe/38GyPxWbIYHPnHZHksNTExTsx3Ie9GtwagkgR/jm64hA=="}, "OrtKISharp": {"type": "CentralTransitive", "requested": "[0.0.2, )", "resolved": "0.0.2", "contentHash": "q8j0yR5836Zhv9WB9BFkQt1UaEFyibq8bqJcTiULlILF6/sz8z7Wy2N8sgYdDKsdW25zncIz7j6IDbKM5ynePg==", "dependencies": {"libortki": "0.0.2"}}, "QuikGraph": {"type": "CentralTransitive", "requested": "[2.5.0, )", "resolved": "2.5.0", "contentHash": "sG+mrPpXwxlXknRK5VqWUGiOmDACa9X+3ftlkQIMgOZUqxVOQSe0+HIU9PTjwqazy0pqSf8MPDXYFGl0GYWcKw=="}, "QuikGraph.Graphviz": {"type": "CentralTransitive", "requested": "[2.5.0, )", "resolved": "2.5.0", "contentHash": "pCKpErtHGxUi72OT+2aIg1pdHdUqpqEM5J/i9rmVsEVDE4X0xb1HBPWdxv/FLZmbBjk0ZogZXZttUL3CnAPpNw==", "dependencies": {"QuikGraph": "2.5.0"}}, "Razor.Templating.Core": {"type": "CentralTransitive", "requested": "[2.0.0, )", "resolved": "2.0.0", "contentHash": "KUsCFq6RYUbFk9NuFLgYNiw+mzmNWkSGv6jzbJAzsgICgaS6e/T+BY+IFeuUIhlDNJE+eZjF4RXCFKizM7bGPA=="}, "System.CommandLine": {"type": "CentralTransitive", "requested": "[2.0.0-beta4.22272.1, )", "resolved": "2.0.0-beta4.22272.1", "contentHash": "1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg=="}, "System.Linq.Async": {"type": "CentralTransitive", "requested": "[6.0.1, )", "resolved": "6.0.1", "contentHash": "0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}}, "System.Reactive": {"type": "CentralTransitive", "requested": "[6.0.0, )", "resolved": "6.0.0", "contentHash": "31kfaW4ZupZzPsI5PVe77VhnvFF55qgma7KZr/E0iFTs6fmdhhG8j0mgEx620iLTey1EynOkEfnyTjtNEpJzGw=="}}}}