{"version": 2, "dependencies": {"net8.0": {"StyleCop.Analyzers": {"type": "Direct", "requested": "[1.2.0-beta.556, )", "resolved": "1.2.0-beta.556", "contentHash": "llRPgmA1fhC0I0QyFLEcjvtM2239QzKr/tcnbsjArLMJxJlu0AA5G7Fft0OI30pHF3MW63Gf4aSSsjc5m82J1Q==", "dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.556"}}, "Google.OrTools.runtime.linux-arm64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "3/he2Q/VkhLDH82/Sw6ieQUb1zMUbs6ZdhhDI2DE6mXqjeUa3lX8caSIyC+IgyXwjAN3K5MaOOLJ8QX22W4yGQ=="}, "Google.OrTools.runtime.linux-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "rldhqtf/OOBifPEoASjAv6qdOlBGSj7HXTgvyKohsA8q0ySCphZOJpYfWoO6Art0r+3FcrldWRoZs0E4iuNQZg=="}, "Google.OrTools.runtime.osx-arm64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "Kp370X4Cmen8Zno6btH45x+qUIG0vT3OaxEgMiezIjCL4nesSO4XTRhKUQwKP5DYmWzSMdfS/ZJUa5rM8L+iAw=="}, "Google.OrTools.runtime.osx-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "VFZkhe6NjrkOkVjKrQXdBgwWCKK3HrqtgSqTIgNrpM30Gb36cQuszlPZxoc6JsbTHSFJFvImUr2qlg2S4CTrXg=="}, "Google.OrTools.runtime.win-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "/2XtRueL9f14BOnTjvzcIGmFXZfIYw/a5MOgJ/UDwBW9iNBgfnoHyzcTbZ0U7W5Oiex+BJtOdwekkmmvSAX1Hg=="}, "libisl.linux-x64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "MW+0UxLeAsVyJ6bn0yka2R5HQdCbtCg/zRZswFaWd8Qi9aVNtJmHVBIMJR1kkNk9vjYLTmGIPPUqwJTKXY7Oog=="}, "libisl.osx-arm64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "/LNMumw054jnLUMqsnOG+00ya3OpHcBlXNXtspbpQNvRnfhCMAdMc5hDanjhhU8Plq+GuLwHWS4C7rYdm/yOsA=="}, "libisl.osx-x64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "hRMKX5KF8zePmfIbH+btnPRi57bTgEhhzG6kA9koIV4fLR8XpKVKRzHh9pQotFX6LSjr297e3vm/yyStLJR1EQ=="}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA=="}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "NetFabric.Hyperlinq.Abstractions": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "WXnEcGwmXfa8gW9N2MlcaPNUzM3NLMwnAhacbtH554F8YcoXbIkTB+uGa1Aa+9gyb/9JZgYVHnmADgJUKP52nA=="}, "StyleCop.Analyzers.Unstable": {"type": "Transitive", "resolved": "1.2.0.556", "contentHash": "zvn9Mqs/ox/83cpYPignI8hJEM2A93s2HkHs8HYMOAQW0PkampyoErAiIyKxgTLqbbad29HX/shv/6LGSjPJNQ=="}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg=="}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA=="}, "nncase.core": {"type": "Project", "dependencies": {"CommunityToolkit.HighPerformance": "[8.2.2, )", "DryIoc.dll": "[5.4.3, )", "GiGraph.Dot": "[3.0.1, )", "Google.OrTools": "[9.14.6206, )", "ISLSharp": "[1.0.5, )", "Microsoft.Extensions.Hosting.Abstractions": "[8.0.0, )", "Microsoft.Extensions.Logging.Abstractions": "[8.0.1, )", "Microsoft.Extensions.Options": "[8.0.2, )", "NetFabric.Hyperlinq": "[3.0.0-beta48, )", "System.CommandLine": "[2.0.0-beta4.22272.1, )", "System.Reactive": "[6.0.0, )"}}, "CommunityToolkit.HighPerformance": {"type": "CentralTransitive", "requested": "[8.2.2, )", "resolved": "8.2.2", "contentHash": "+zIp8d3sbtYaRbM6hqDs4Ui/z34j7DcUmleruZlYLE4CVxXq+MO8XJyIs42vzeTYFX+k0Iq1dEbBUnQ4z/Gnrw=="}, "DryIoc.dll": {"type": "CentralTransitive", "requested": "[5.4.3, )", "resolved": "5.4.3", "contentHash": "yhXOG3SOxeWYxBAWskNRDD8fzw5hriEawv4VN4WKaFHOuubiop4kxe2rkWTEyCnTgRVgxVUSQCDBBkZqwPm1iQ=="}, "GiGraph.Dot": {"type": "CentralTransitive", "requested": "[3.0.1, )", "resolved": "3.0.1", "contentHash": "TseDWNkBFKrrrzamgl2yeSDk9gmxQZvymcTqRydY/EL9REM9jBwCHFceg7AutXw3ULUL/FKhc1uFzfd2Cjpe/A=="}, "Google.OrTools": {"type": "CentralTransitive", "requested": "[9.14.6206, )", "resolved": "9.14.6206", "contentHash": "HLZCX691VXzR5x6YAHUxHvxHC+KyXJIiqKiS/Fu/g23W8XZgmfSGctnchVkrUT8a9Sosyvp1G0GQlV/i9PdVNQ==", "dependencies": {"Google.OrTools.runtime.linux-arm64": "9.14.6206", "Google.OrTools.runtime.linux-x64": "9.14.6206", "Google.OrTools.runtime.osx-arm64": "9.14.6206", "Google.OrTools.runtime.osx-x64": "9.14.6206", "Google.OrTools.runtime.win-x64": "9.14.6206", "Google.Protobuf": "3.31.1"}}, "Google.Protobuf": {"type": "CentralTransitive", "requested": "[3.27.3, )", "resolved": "3.31.1", "contentHash": "gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A=="}, "ISLSharp": {"type": "CentralTransitive", "requested": "[1.0.5, )", "resolved": "1.0.5", "contentHash": "9v3KxWpuiCJjK93ZpSjy/VVgaW2xBjc6KZ30TePyPFK2oGpeGEI5C7d9lda1X2dE9+XEJIlBKq0P7JvE6eiiFw==", "dependencies": {"libisl.linux-x64": "0.0.4", "libisl.osx-arm64": "0.0.4", "libisl.osx-x64": "0.0.4"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "Microsoft.Extensions.Options": {"type": "CentralTransitive", "requested": "[8.0.2, )", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "NetFabric.Hyperlinq": {"type": "CentralTransitive", "requested": "[3.0.0-beta48, )", "resolved": "3.0.0-beta48", "contentHash": "oYUhXvxNS8bBJWqNkvx5g8y0P/0LtyqS2pN0w4OWjVDNWEpLbdbvPy9w/9z1n2PrqIjX3jxUsEnoCmxxGnI3gw==", "dependencies": {"NetFabric.Hyperlinq.Abstractions": "1.3.0", "System.Buffers": "4.5.1", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}}, "System.CommandLine": {"type": "CentralTransitive", "requested": "[2.0.0-beta4.22272.1, )", "resolved": "2.0.0-beta4.22272.1", "contentHash": "1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg=="}, "System.Reactive": {"type": "CentralTransitive", "requested": "[6.0.0, )", "resolved": "6.0.0", "contentHash": "31kfaW4ZupZzPsI5PVe77VhnvFF55qgma7KZr/E0iFTs6fmdhhG8j0mgEx620iLTey1EynOkEfnyTjtNEpJzGw=="}}}}